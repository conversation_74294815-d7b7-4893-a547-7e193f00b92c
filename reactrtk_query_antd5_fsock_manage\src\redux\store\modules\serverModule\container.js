import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import ServerModule from "./index";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getServerListData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData1: data.list
        };
    }
    return ownProps;
};

let initData = function (search) {
    this.searchInputFormRef.current.setFieldsValue({
        search: search
    });

    window.setTimeout(() => {
        let inputEle = this.searchInputRef.current.input;
        let parentEle = inputEle.parentElement;
        let buttonEle = parentEle.querySelector('.ant-input-group-addon button');
        buttonEle.click();
    }, 100);
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期
            let search = moduleParam.search || '';
            initData.call(this, search);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
            let search = moduleParam.search || '';
            initData.call(this, search);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData1 !== this.props.tableData1) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData1);
            }
        },
        inputOnSearch: function (value, event) {
            dispatch(baseApi.endpoints.getServerListData.initiate({
                search: value
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
        },
        handleAddOk: function () {
            let modalInst = this.addModalRef.current;
            let formInst = modalInst.formRef.current;
            let tableInst = this.tableRef;
            //校验表单
            formInst.validateFields()
                .then(values => {
                    // 校验通过
                    dispatch(baseApi.endpoints.postServerData.initiate(formInst.getFieldsValue(), {
                        subscribe: false, forceRefetch: true,
                    })).then(() => {
                        formInst.resetFields();

                        //关闭模态框
                        modalInst.setState({
                            visible: false
                        });

                        //刷新表格
                        dispatch(baseApi.endpoints.getServerListData.initiate({
                            search: ''
                        }, {
                            subscribe: false, forceRefetch: true
                        })).then((response) => {
                            let data = response.data.list;
                            tableInst.current._dataMethod_.refresh(data);
                        });
                    });
                })
        },
        handleModifyOk: function () {
            let modalInst = this.modifyModalRef.current;
            let formInst = modalInst.formRef.current;
            let tableInst = this.tableRef;
            let this_ = this;
            //校验表单
            formInst.validateFields()
                .then(values => {
                    // 校验通过
                    dispatch(baseApi.endpoints.putServerData.initiate(formInst.getFieldsValue(), {
                        subscribe: false, forceRefetch: true,
                    })).then(() => {
                        formInst.resetFields();

                        //关闭模态框
                        modalInst.setState({
                            visible: false
                        });

                        //清空选项
                        this_.setState({
                            selectedRowKeys: []
                        });

                        //刷新表格
                        dispatch(baseApi.endpoints.getServerListData.initiate({
                            search: ''
                        }, {
                            subscribe: false, forceRefetch: true
                        })).then((response) => {
                            let data = response.data.list;
                            tableInst.current._dataMethod_.refresh(data);
                        });
                    });
                })
                .catch(info => {
                    console.log('Validate Failed:', info);
                });
        },
        handleDelOk: function () {
            let tableInst = this.tableRef;
            let selectedId = this.selectedRows[0].id;
            dispatch(baseApi.endpoints.delServerData.initiate({id: selectedId}, {
                subscribe: false, forceRefetch: true,
            })).then(() => {
                //隐藏弹窗
                let delModalInst = this.delModalRef.current;
                delModalInst.setState({
                    visible: false
                });

                //刷新表格
                dispatch(baseApi.endpoints.getServerListData.initiate({
                    search: ''
                }, {
                    subscribe: false, forceRefetch: true
                })).then((response) => {
                    let data = response.data.list;
                    tableInst.current._dataMethod_.refresh(data);
                });

                window.global.Notification.open({
                    type: window.global.Notification.TYPE.SUCCESS,
                    message: "删除服务器成功.",
                    delay: 3,
                });
            });
        },
        modifyServerConfigBtnOnClick: function (event) {
            let target = event.target;
            let serverId = target.dataset['serverid'];
            dispatch(baseApi.endpoints.modifyServerConfig.initiate({id: serverId}, {
                subscribe: false, forceRefetch: true,
            })).then((response) => {
                if (response.data.status) {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "修改服务器配置成功.",
                        delay: 3,
                    });

                    let tableInst = this.tableRef;
                    //刷新表格
                    dispatch(baseApi.endpoints.getServerListData.initiate({
                        search: ''
                    }, {
                        subscribe: false, forceRefetch: true
                    })).then((response) => {
                        let data = response.data.list;
                        tableInst.current._dataMethod_.refresh(data);
                    });
                } else {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: "修改服务器配置失败." + response.data.errorMsg,
                        delay: 3,
                    });
                }
            });
        },
        installDockerBtnOnClick: function (event) {
            let target = event.target;
            let serverId = target.dataset['serverid'];
            dispatch(baseApi.endpoints.installDockerToServer.initiate({id: serverId}, {
                subscribe: false, forceRefetch: true,
            })).then((response) => {
                if (response.data.status) {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "安装docker成功.",
                        delay: 3,
                    });

                    let tableInst = this.tableRef;
                    //刷新表格
                    dispatch(baseApi.endpoints.getServerListData.initiate({
                        search: ''
                    }, {
                        subscribe: false, forceRefetch: true
                    })).then((response) => {
                        let data = response.data.list;
                        tableInst.current._dataMethod_.refresh(data);
                    });
                } else {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: "安装docker失败." + response.data.errorMsg,
                        delay: 3,
                    });
                }
            });
        },
        startServerBtnOnClick: function (event) {
            let target = event.target;
            let serverId = target.dataset['serverid'];
            dispatch(baseApi.endpoints.startServer.initiate({id: serverId}, {
                subscribe: false, forceRefetch: true,
            })).then((response) => {
                if (response.data.status) {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "启动服务器成功.",
                        delay: 3,
                    });

                    let tableInst = this.tableRef;
                    //刷新表格
                    dispatch(baseApi.endpoints.getServerListData.initiate({
                        search: ''
                    }, {
                        subscribe: false, forceRefetch: true
                    })).then((response) => {
                        let data = response.data.list;
                        tableInst.current._dataMethod_.refresh(data);
                    });
                } else {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: "启动服务器失败." + response.data.errorMsg,
                        delay: 3,
                    });
                }
            });
        },
        stopServerBtnOnClick: function (event) {
            let target = event.target;
            let serverId = target.dataset['serverid'];
            dispatch(baseApi.endpoints.stopServer.initiate({id: serverId}, {
                subscribe: false, forceRefetch: true,
            })).then((response) => {
                if (response.data.status) {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "停止服务成功.",
                        delay: 3,
                    });

                    let tableInst = this.tableRef;
                    //刷新表格
                    dispatch(baseApi.endpoints.getServerListData.initiate({
                        search: ''
                    }, {
                        subscribe: false, forceRefetch: true
                    })).then((response) => {
                        let data = response.data.list;
                        tableInst.current._dataMethod_.refresh(data);
                    });
                } else {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: "停止服务器失败." + response.data.errorMsg,
                        delay: 3,
                    });
                }
            });
        },
    };
};

let Container = ModuleContainerCreator.create(ServerModule, mapStateToProps, mapDispatchToProps);
export default Container;
