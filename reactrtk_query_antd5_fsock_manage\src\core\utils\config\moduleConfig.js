/**
 *模型配置
 */
let moduleConfig = [
    {
        moduleId: '/indexModule5',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/indexModules/index5" */ "../../../redux/store/modules/indexModule5/container");
        }
    },
    {
        moduleId: '/userModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/userModule" */ "../../../redux/store/modules/userModule/container");
        }
    },
    {
        moduleId: '/userAccessLogModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/userAccessLogModule" */ "../../../redux/store/modules/userAccessLogModule/container");
        }
    },
    {
        moduleId: '/groupModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/groupModule" */ "../../../redux/store/modules/groupModule/container");
        }
    },
    {
        moduleId: '/trafficRuleModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/trafficRuleModule" */ "../../../redux/store/modules/trafficRuleModule/container");
        }
    },
    {
        moduleId: '/userModule/userGroupAssignModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/userModule/userGroupAssignModule" */ "../../../redux/store/modules/userGroupAssignModule/container");
        }
    },
    {
        moduleId: '/userModule/userTrafficRuleAssignModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/userModule/userTrafficRuleAssignModule" */ "../../../redux/store/modules/userTrafficRuleAssignModule/container");
        }
    },
    {
        moduleId: '/groupModule/groupTrafficRuleAssign',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/groupModule/groupTrafficRuleAssign" */ "../../../redux/store/modules/groupTrafficRuleAssignModule/container");
        }
    },
    {
        moduleId: '/groupModule/groupTrafficCapacityAssignModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/groupModule/groupTrafficRuleAssign" */ "../../../redux/store/modules/groupTrafficCapacityAssignModule/container");
        }
    },
    {
        moduleId: '/groupModule/userTrafficRuleAssign',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/userModule/userTrafficRuleAssign" */ "../../../redux/store/modules/userTrafficRuleAssignModule/container");
        }
    },
    {
        moduleId: '/serverModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/serverModule" */ "../../../redux/store/modules/serverModule/container");
        }
    },
    {
        moduleId: '/packageResourcesModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/serverModule" */ "../../../redux/store/modules/packageResourcesModule/container");
        }
    },
    {
        moduleId: '/packageResourcesModule/serverAssignPackageResourcesModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/packageResourcesModule/serverAssignPackageResourcesModule" */ "../../../redux/store/modules/serverAssignPackageResourcesModule/container");
        }
    },
    {
        moduleId: '/orderListModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/orderListModule" */ "../../../redux/store/modules/orderListModule/container");
        }
    },
    {
        moduleId: '/deliverModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/deliverModule" */ "../../../redux/store/modules/deliverModule/container");
        }
    },
];

export default moduleConfig;
