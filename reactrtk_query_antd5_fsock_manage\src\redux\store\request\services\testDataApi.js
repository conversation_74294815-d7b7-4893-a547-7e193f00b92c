const url = 'testData';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getTopMenuData: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/topMenuData',
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        // getLeftMenuData: builder.query({
        //     query: (queryArg) => {
        //
        //         // let formData = new FormData();
        //         // formData.set(pId, pId);
        //         return {
        //             url: 'testData/leftMenuData',
        //             method: 'GET',
        //             params: queryArg,
        //
        //             // method: 'POST',
        //             // body: formData,
        //         }
        //     },
        //     transformResponse: transformResponse,
        //     onQueryStarted: onQueryStarted
        // }),
        getTableData: builder.query({
            query: (name) => url + '/table1Data',
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        getLineChartData: builder.query({
            query: (name) => url + '/lineChartData',
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        getLineChartPushData: builder.query({
            query: (name) => url + '/lineChartPushData',
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        })
    }
}
