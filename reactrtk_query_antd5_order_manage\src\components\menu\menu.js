import React, {Component} from 'react';
import {Menu} from 'antd';
import * as icons from '@ant-design/icons';
import Link from '../../redux/store/components/link/container'

// const ICON_MAP = {
//     HomeOutlined,
//     DownCircleOutlined,
//     PlayCircleOutlined
// };

const MENU_TYPE = {
    ITEM: 'item',
    SUB: 'sub'
}

/**
 * 使用menu items属性渲染
 */
class Menu_ extends Component {
    constructor(props) {
        super(props);
        this.onOpenChange = this.onOpenChange.bind(this);
        this.state = {
            openKeys: props.openKeys ? props.openKeys : []
        };
    }

    /**
     * 点击切换菜单回调
     * @param openKeys
     */
    onOpenChange = (openKeys) => {
        const latestOpenKey = openKeys.find(key => this.state.openKeys.indexOf(key) === -1);
        if (this.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
            this.setState({openKeys});
        } else {
            this.setState({
                openKeys: latestOpenKey ? [latestOpenKey] : [],
            });
        }
    };

    /**
     *渲染菜单items数据
     * @param config
     * {
            style: {height: '100%', borderRight: 0},
            theme: 'dark',
            mode: 'inline',
            defaultSelectedKeys: ['sub1_item1'],
            defaultOpenKeys: ['sub1'],
            //使用需要链接
            hasLink: true,
     *  }
     * @param data
     [
     {
                "id": "sub1",
                "pid": "",
                "type": "sub",
                "iconType": "HomeOutlined",
                "name": "menuSub1"
            },
     {
                "id": "sub1_item1",
                "pid": "sub1",
                "type": "item",
                "name": "sub1_item1",
                "iconType": "HomeOutlined",
                "href": "#indexModule1"
            },  {
                "id": "sub1_sub1",
                "pid": "sub1",
                "type": "sub",
                "iconType": "DownCircleOutlined",
                "name": "sub1_sub1"
            }, {
                "id": "sub1_sub1_item1",
                "pid": "sub1_sub1",
                "type": "item",
                "name": "sub1_sub1_item1",
                "iconType": "HomeOutlined",
                "href": "#indexModule6"
            }
     ]
     * @returns {*[]}
     */
    renderMenu(config, data = []) {
        let rootMenus = data.filter((item) => {
            return !item.pid;
        });

        let subMenus = [];
        for (let i = 0, rootMenu; i < rootMenus.length; i++) {
            rootMenu = rootMenus[i];

            let subMenu = {};
            this.renderSub(config, data, rootMenu, subMenu);
            subMenus.push(subMenu);
        }

        return subMenus;
    }

    /**
     * 渲染子菜单
     * @param config
     * @param data
     * @param pItem
     * @param subMenuObj
     */
    renderSub(config, data, pItem, subMenuObj) {
        Object.assign(subMenuObj, {
            key: pItem.id,
            label: pItem.name,
        });

        //sub菜单添加icon
        let Icon = icons[pItem.iconType];
        Icon && (subMenuObj.icon = <Icon/>);

        //pItem不是目录菜单，直接返回item
        if (pItem.type !== MENU_TYPE.SUB) {
            subMenuObj.label = config.hasLink ? <Link href={pItem.href}>{pItem.name}</Link> : pItem.name;
            return;
        }

        for (let i = 0, item; i < data.length; i++) {
            item = data[i];
            if (item.pid === pItem.id) {
                subMenuObj.children || (subMenuObj.children = []);
                if (item.type === MENU_TYPE.ITEM) {
                    let item_ = {
                        key: item.id,
                        label: config.hasLink ? <Link href={item.href}>{item.name}</Link> : item.name,
                    };
                    let Icon = icons[item.iconType];
                    Icon && (item_.icon = <Icon/>);

                    subMenuObj.children.push(item_);
                } else if (item.type === MENU_TYPE.SUB) {
                    let item_ = {};
                    subMenuObj.children.push(item_);
                    this.renderSub(config, data, item, item_);
                }
            }
        }
    }

    render() {
        //计算出根菜单的id列表
        this.rootSubmenuKeys = [];
        if (this.props.data) {
            for (let i = 0; i < this.props.data.length; i++) {
                let item = this.props.data[i];
                if (!item.pid) {
                    this.rootSubmenuKeys.push(item.id);
                }
            }
        }

        return (
            <Menu
                onClick={this.props.onItemClick}
                theme={this.props.config.theme}
                mode={this.props.config.mode}
                defaultSelectedKeys={this.props.config.defaultSelectedKeys}
                defaultOpenKeys={this.props.config.defaultOpenKeys}
                onOpenChange={this.onOpenChange}
                openKeys={this.state.openKeys}
                style={this.props.config.style}
                /*
                * [
                      { label: '菜单项一', key: 'item-1' },
                      { label: '菜单项二', key: 'item-2' },
                      {
                        label: '子菜单',
                        key: 'submenu',
                        icon: <DownCircleOutlined/>,
                        children: [{ label: '子菜单项', key: 'submenu-item-1' }],
                      },
                   ]
                * */
                items={this.renderMenu(this.props.config, this.props.data)}
            >
            </Menu>
        );
    }
}

export default Menu_;