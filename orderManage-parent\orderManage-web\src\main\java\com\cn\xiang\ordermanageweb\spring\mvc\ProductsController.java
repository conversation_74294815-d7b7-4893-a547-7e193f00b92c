package com.cn.xiang.ordermanageweb.spring.mvc;

import com.cn.xiang.ordermanageweb.po.ProductsPo;
import com.cn.xiang.ordermanageweb.services.IProductsService;
import com.cn.xiang.ordermanageweb.spring.support.ControllerSupport;
import com.cn.xiang.ordermanageweb.utils.JSONResultMessage;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api_v1/product")
public class ProductsController extends ControllerSupport {
    @Autowired
    private IProductsService productsService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        List<ProductsPo> list = productsService.list();

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "findById", method = RequestMethod.GET)
    public Object findById(@Validated @NotEmpty String productId) {
        ProductsPo product = productsService.getById(productId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("product", product);
        return message;
    }

//    @RequestMapping(value = "testAdd", method = RequestMethod.GET)
//    public Object testAdd() {
//        ProductsPo productsPo = new ProductsPo();
//        productsPo.setName("JAPAN.TKY.BGP.Basic");
//        productsPo.setBandwidth("500");
//        productsPo.setMonthlyTraffic(60);
//        productsPo.setPrice(30);
//        productsPo.setStock(5);
//        productsService.save(productsPo);
//
//        productsPo = new ProductsPo();
//        productsPo.setName("JAPAN.TKY.BGP.Basic");
//        productsPo.setBandwidth("200");
//        productsPo.setMonthlyTraffic(60);
//        productsPo.setPrice(25);
//        productsPo.setStock(30);
//        productsService.save(productsPo);
//
//        productsPo = new ProductsPo();
//        productsPo.setName("JAPAN.TKY.BGP.Basic");
//        productsPo.setBandwidth("200");
//        productsPo.setMonthlyTraffic(60);
//        productsPo.setPrice(25);
//        productsPo.setStock(30);
//        productsService.save(productsPo);
//
//        productsPo = new ProductsPo();
//        productsPo.setName("JAPAN.TKY.BGP.Basic");
//        productsPo.setBandwidth("300");
//        productsPo.setMonthlyTraffic(60);
//        productsPo.setPrice(35);
//        productsPo.setStock(50);
//        productsService.save(productsPo);
//
//        JSONResultMessage message = this.getJSONResultMessage();
//        message.success();
//        return message;
//    }
}
