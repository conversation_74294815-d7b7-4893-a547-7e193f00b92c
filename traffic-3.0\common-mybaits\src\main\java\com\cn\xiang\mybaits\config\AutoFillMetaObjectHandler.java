package com.cn.xiang.mybaits.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

public class AutoFillMetaObjectHandler implements MetaObjectHandler {
    private static final String ANONYMOUS = "anonymous";

    /**
     * 获取当前用户
     * TODO: 从SecurityContextHolder获取当前用户
     *
     * @return
     */
    private String getCurrentUsername() {
        return ANONYMOUS;
    }

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        this.setFieldValByName("createdBy", getCurrentUsername(), metaObject);
        this.setFieldValByName("createdTime", now, metaObject);
        this.setFieldValByName("updatedBy", getCurrentUsername(), metaObject);
        this.setFieldValByName("updatedTime", now, metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        this.setFieldValByName("updatedBy", getCurrentUsername(), metaObject);
        this.setFieldValByName("updatedTime", now, metaObject);
    }
}
