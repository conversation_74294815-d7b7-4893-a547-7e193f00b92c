package com.xiang.traffic.client;

import com.alibaba.fastjson.JSON;
import com.xiang.traffic.ComponentException;
import com.xiang.traffic.LifecycleState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.concurrent.locks.LockSupport;

/**
 * 客户端启动引导类
 *
 * <AUTHOR>
 * @date 2024/6/6 15:15
 */
public class ClientBoot {

    private static final Logger log = LoggerFactory.getLogger(ClientBoot.class);

    private static final Client client = new StandardClient();

    public static void main(String[] args) {
        if (log.isDebugEnabled()) {
            log.debug("System properties: {}", JSON.toJSONString(System.getProperties()));
            log.debug("System env: {}", JSON.toJSONString(System.getenv()));
        }

        log.info("traffic client {} start...", client.getVersion());
        long st = System.currentTimeMillis();
        try {
            client.init();
            client.start();
            System.gc();
        } catch (ComponentException e) {
            log.error("traffic client {} start failure, cause:", client.getVersion(), e);
            log.info("submit issue at https://github.com/abc123lzf/flyingsocks");
            Client.exitWithNotify(1, "exitmsg.client_boot.start_failure", e.getMessage());
        }

        long ed = System.currentTimeMillis();
        log.info("traffic client {} start complete, use {} millisecond", client.getVersion(), ed - st);

        boolean running = client.runGUITask();
        if (!running) {
            Thread mainThread = Thread.currentThread();
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                if (!client.getState().after(LifecycleState.STOPING)) {
                    client.stop();
                }
                LockSupport.unpark(mainThread);
            }, "Thread-ShutdownHook"));

            LockSupport.park();
            log.info("Shutdown client at {}", LocalDateTime.now());
        }
    }
//    public static void main(String[] args) {
//        Properties p = System.getProperties();
//        for(Map.Entry<Object, Object> entry:p.entrySet()){
//            System.out.println(entry.getKey()+"="+entry.getValue());
//        }
//    }


    private ClientBoot() {
        throw new UnsupportedOperationException();
    }
}
