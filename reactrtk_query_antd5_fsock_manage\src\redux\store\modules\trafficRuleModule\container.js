import TrafficRuleModule from './index';
import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getTrafficRuleListData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData: data.list
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期

            const {unsubscribe} = dispatch(baseApi.endpoints.getTrafficRuleListData.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);

        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
            const {unsubscribe} = dispatch(baseApi.endpoints.getTrafficRuleListData.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);

        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData !== this.props.tableData) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData);
            }
        },
        handleAddOk: function () {
            let modalInst = this.addModalRef.current;
            let formInst = modalInst.formRef.current;
            let tableInst = this.tableRef;
            //校验表单
            formInst.validateFields()
                .then(values => {
                    // 校验通过
                    dispatch(baseApi.endpoints.postTrafficRuleData.initiate(formInst.getFieldsValue(), {
                        subscribe: false, forceRefetch: true,
                    })).then((res) => {
                        formInst.resetFields();

                        //关闭模态框
                        modalInst.setState({
                            visible: false
                        });

                        //刷新表格
                        dispatch(baseApi.endpoints.getTrafficRuleListData.initiate(undefined, {
                            subscribe: false, forceRefetch: true
                        })).then((response) => {
                            let data = response.data.list;
                            tableInst.current._dataMethod_.refresh(data);

                            window.global.Notification.open({
                                type: window.global.Notification.TYPE.SUCCESS,
                                message: "添加流量规则成功.",
                                delay: 3,
                            });
                        });
                    });
                })
        },
        handleModifyOk: function () {
            let modalInst = this.modifyModalRef.current;
            let formInst = modalInst.formRef.current;
            let tableInst = this.tableRef;
            let this_ = this;
            //校验表单
            formInst.validateFields()
                .then(values => {
                    // 校验通过
                    dispatch(baseApi.endpoints.putTrafficRuleData.initiate(formInst.getFieldsValue(), {
                        subscribe: false, forceRefetch: true,
                    })).then(() => {
                        formInst.resetFields();

                        //关闭模态框
                        modalInst.setState({
                            visible: false
                        });

                        //刷新表格
                        dispatch(baseApi.endpoints.getTrafficRuleListData.initiate(undefined, {
                            subscribe: false, forceRefetch: true
                        })).then((response) => {
                            let data = response.data.list;
                            tableInst.current._dataMethod_.refresh(data);

                            //清空选项
                            this_.setState({
                                selectedRowKeys: [],
                            });

                            window.global.Notification.open({
                                type: window.global.Notification.TYPE.SUCCESS,
                                message: "修改流量规则成功.",
                                delay: 3,
                            });
                        });
                    });
                })
                .catch(info => {
                    console.log('Validate Failed:', info);
                });
        },
        handleDelOk: function () {
            let tableInst = this.tableRef;
            let selectedId = this.selectedRows[0].id;
            dispatch(baseApi.endpoints.delTrafficRuleData.initiate({id: selectedId}, {
                subscribe: false, forceRefetch: true,
            })).then(() => {
                //隐藏弹窗
                let delModalInst = this.delModalRef.current;
                delModalInst.setState({
                    visible: false
                });

                //刷新表格
                dispatch(baseApi.endpoints.getTrafficRuleListData.initiate(undefined, {
                    subscribe: false, forceRefetch: true
                })).then((response) => {
                    let data = response.data.list;
                    tableInst.current._dataMethod_.refresh(data);

                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "删除流量规则成功.",
                        delay: 3,
                    });
                });
            });
        }
    };
};

let Container = ModuleContainerCreator.create(TrafficRuleModule, mapStateToProps, mapDispatchToProps);
export default Container;
