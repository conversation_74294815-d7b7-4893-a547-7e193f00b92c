<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.mybaits.mapper.IUserMapper">
    <resultMap id="userResultMap" type="com.cn.xiang.mybaits.po.UserPo">
        <!-- <id column="id" property="id"/>
         <result column="group_id" property="groupId"/>
         <result column="created_by" property="createdBy"/>
         <result column="created_time" property="createdTime"/>
         <result column="updated_by" property="updatedBy"/>
         <result column="updated_time" property="updatedTime"/>
         <result column="username" property="username"/>
         <result column="password" property="password"/>-->

        <association property="trafficRulePo" column="traffic_rule_id"
                     select="com.cn.xiang.mybaits.mapper.ITrafficRuleMapper.selectById"/>
    </resultMap>

    <select id="findUserById1" parameterType="String" resultType="com.cn.xiang.mybaits.po.UserPo">
        select * from tb_user where id = #{id}
    </select>
    <select id="findUserByGroupNameAndUsername" parameterType="String" resultType="com.cn.xiang.mybaits.po.UserPo">
        select u.* from tb_user u, tb_group g where g.id = u.group_id and g.name = #{groupName} and u.username=
        #{username}
    </select>

    <select id="findUsersByGroupName" parameterType="String" resultMap="userResultMap">
        select u.* from tb_user u, tb_group g where g.id = u.group_id and g.name = #{groupName}
    </select>

</mapper>
