package com.xiang.traffic;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Scanner;

/**
 * 表示顶层组件，顶层组件不可拥有父组件
 * 顶层组件除了具有AbstractComponent所有特性外，还可以保存环境参数，加载环境资源，并具有配置管理器
 *
 * @see AbstractComponent
 * @see Environment
 */
public abstract class TopLevelComponent extends AbstractComponent<VoidComponent> implements Environment {

    static {
        try {
            //加载这个类以便支持‘classpath:’类型的URL
            Class.forName("com.xiang.traffic.url.ClasspathURLHandlerFactory");
        } catch (ClassNotFoundException e) {
            throw new ComponentException(e);
        }
    }

    protected final String version;

    /**
     * 配置管理器实例
     */
    private final ConfigManager<TopLevelComponent> configManager = new DefaultConfigManager<>(this);

    protected TopLevelComponent() {
        super();
        this.version = readVersion();
        addModule(configManager);
    }

    protected TopLevelComponent(String name) {
        super(name, null);
        this.version = readVersion();
        addModule(configManager);
    }

    @Override
    public final VoidComponent getParentComponent() {
        return null;
    }

    public final String getVersion() {
        return version;
    }

    @Override
    public String getEnvironmentVariable(String key) {
        return System.getenv(key);
    }

    @Override
    public String getSystemProperties(String key) {
        return System.getProperty(key);
    }

    @Override
    public void setSystemProperties(String key, String value) {
        System.setProperty(key, value);
    }

    @Override
    public InputStream loadResource(String path) throws IOException {
        try {
            URL url = new URL(path);
            return url.openStream();
        } catch (MalformedURLException e) {
            File file = new File(path);
            if (file.exists() && file.isFile()) {
                return new FileInputStream(file);
            } else {
                if (log.isWarnEnabled()) log.warn("URL or file path error", e);
                return null;
            }
        } catch (IOException e) {
            if (log.isErrorEnabled()) log.error("load resource {} occur a IOException", path);
            throw e;
        }
    }

    protected ConfigManager<?> getConfigManager() {
        return configManager;
    }

    private static String readVersion() {
        try (InputStream versionInputStream = new URL("classpath://META-INF/version").openStream(); Scanner sc = new Scanner(versionInputStream)) {
            String version = sc.nextLine();
            String tag = sc.nextLine();
            return version + "-" + tag;
        } catch (IOException e) {
            throw new Error(e);
        }
    }
}
