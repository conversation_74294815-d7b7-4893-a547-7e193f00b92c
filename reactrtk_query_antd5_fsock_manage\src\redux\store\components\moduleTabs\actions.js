import moduleTabsReducerObj from './reducer'

/**
 * 添加tab
 * @param tabObj
 * {
        title: '',
        key: ''
 * }
 * @returns {Function}
 */
const addTab = (tabObj) => {
    return function (dispatch, getState) {
        dispatch(moduleTabsReducerObj.actions.addTab({
            title: tabObj.title,
            key: tabObj.key
        }));
    }
};
/**
 * 选中tab
 * @param activeKey
 * @returns {Function}
 */
const selectTab = (activeKey) => {
    return function (dispatch, getState) {
        dispatch(moduleTabsReducerObj.actions.selectTab({
            activeKey: activeKey
        }));
    }
};

/**
 * 删除tab
 * @param key
 * @returns {Function}
 */
const removeTab = (key) => {
    return function (dispatch, getState) {
        dispatch(moduleTabsReducerObj.actions.removeTab({
            key: key,
        }));
    }
};

/**
 * 修改tab title
 * @param key
 * @param title
 * @returns {Function}
 */
const changeTabTitle = (key, title) => {
    return function (dispatch, getState) {
        dispatch(moduleTabsReducerObj.actions.changeTabTitle({
            key: key,
            title: title
        }));
    }
};

let result = {
    moduleTabs: {
        addTab,
        selectTab,
        removeTab,
        changeTabTitle
    }
};
export default result;
