const url = 'group';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getGroupListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '?search=' + queryArg.search,
                    method: 'GET',
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let list = content.list;
                for (let i = 0; i < list.length; i++) {
                    list[i].key = list[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
        postGroupData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'POST',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        putGroupData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'PUT',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        delGroupData: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url,
                    method: 'DELETE',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        postAssignGroupTrafficRule: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('groupId', queryArg.groupId);
                formData.set('trafficRuleId', queryArg.trafficRuleId);
                return {
                    url: url + '/groupTrafficRuleAssign' ,
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        postAssignGroupTrafficCapacity: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('groupId', queryArg.groupId);
                formData.set('trafficCapacityId', queryArg.trafficCapacityId);
                return {
                    url: url + '/groupTrafficCapacityAssign' ,
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
