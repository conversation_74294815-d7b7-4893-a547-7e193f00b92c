import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Space, Table} from "antd";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: false
});

class groupTrafficCapacityAssignModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true, selectedRowKeys: [],
        }
        this.tableRef = React.createRef();
    }

    selectedRows;

    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            selectedRowKeys: selectedRowKeys, btnDisabled: false,
        })
    }

    render() {
        return (<div>
            <Space style={{marginBottom: 16}}>
                <Button type="primary" disabled={this.state.btnDisabled}
                        onClick={this.props.assignBtnOnClick.bind(this)}>
                    分配
                </Button>
            </Space>
            <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={{
                type: 'radio', selectedRowKeys: this.state.selectedRowKeys, onChange: this.onChange
            }}>
                <Column align={"center"} title="月流量" dataIndex="trafficCapacity"/>
                <Column align={"center"} title="月流量（计算出的）" dataIndex="trafficCapacityHuman"
                        render={(text, record, index) => {
                            let trafficCapacity = record.trafficCapacity;
                            return trafficCapacity / 1000 / 1000 / 1000 + " GB";
                        }}/>
                <Column align={"center"} title="月流量（human）" dataIndex="trafficCapacityHuman"/>
            </TableWrapper>
        </div>)
    }
}

export default groupTrafficCapacityAssignModule;
