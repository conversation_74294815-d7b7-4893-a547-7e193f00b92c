package com.cn.xiang.mybaits.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cn.xiang.mybaits.po.UserPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5 12:14
 */
@Mapper
public interface IUserMapper extends BaseMapper<UserPo> {

    public UserPo findUserById1(@Param("id") String id);

    /**
     * 根据组名和用户名，查找用户
     *
     * @param groupName
     * @param username
     * @return
     */
    public UserPo findUserByGroupNameAndUsername(@Param("groupName") String groupName, @Param("username") String username);

    /**
     * 根据组名查找用户列表
     *
     * @param groupName
     * @return
     */
    List<UserPo> findUsersByGroupName(@Param("groupName") String groupName);
}
