import {connect} from 'react-redux'
import LeftMenu from '../../../../../components/menu/LeftMenu';
import Utils from "../../../../../core/utils/utils";
import {baseApi} from "../../../request/services/base/base";

const mapStateToProps = (state, ownProps) => {
    // if (Utils.isFetched(state, 'menu/leftMenuData')) {
    //     return {
    //         data: state.fetchBySubreddit['menu/leftMenuData'].data
    //     }
    // }
    let result = Utils.selectLatestDataByEndpoint('getLeftMenuData', baseApi, state);
    if (result) {
        const {data, isSuccess} = result;
        if (isSuccess) {
            return {
                data: data.list
            };
        }
    }

    return ownProps;
};

const mapDispatchToProps = (dispatch, ownProps) => {
    return {
        loadLeftMenu: function () {
            dispatch(baseApi.endpoints.getLeftMenuData.initiate({
                pId: ""
            }, {
                forceRefetch: true
            }));
        }
    }
}

const Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(LeftMenu);

export default Container;
