package com.cn.xiang.fsockmanageweb.task.jobs;

import com.cn.xiang.fsockmanageweb.services.IUserService;
import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import com.cn.xiang.fsockmanageweb.task.ScheduleManager;
import com.cn.xiang.fsockmanageweb.task.utils.enum_.ScheduleStatus;
import com.cn.xiang.fsockmanageweb.task.vo.ScheduleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * 用户过期任务，每15分钟触发一次
 *
 * <AUTHOR>
 * @date 2024/7/3 15:35
 */
public class UserExpirationJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserExpirationJob.class);

    public void execute(String params) {
        LOGGER.info("UserExpirationJob trigger，每15分钟执行一次。");
        IUserService userService = ApplicationContextHolder.getBean(IUserService.class);
        userService.checkUserExpireTime(params);
    }

    public static void init() {
        ScheduleJob scheduleJob = new ScheduleJob();
        scheduleJob.setJobName("userExpirationJob");
        scheduleJob.setBeanName("userExpirationJob");
        scheduleJob.setMethodName("execute");
        scheduleJob.setId("userExpirationJobId");
//        scheduleJob.setCronExpression("0/1 * * * * ?");
        //每15分钟执行一次
        scheduleJob.setCronExpression("0 0/15 * * * ? *");
        scheduleJob.setParams("userExpirationJob params");
        scheduleJob.setStatus(ScheduleStatus.RUNNING.getCode());
        scheduleJob.setCreateTime(new Date(System.currentTimeMillis()));
        scheduleJob.setUpdateTime(new Date(System.currentTimeMillis()));

        ScheduleManager scheduleManager = ApplicationContextHolder.getBean(ScheduleManager.class);
        scheduleManager.createScheduleJob(scheduleJob);
    }
}
