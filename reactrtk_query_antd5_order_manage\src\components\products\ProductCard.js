import React from "react";
import {<PERSON><PERSON>, <PERSON>, <PERSON>, Col} from 'antd'
import './index.less'

class ProductCard extends React.Component {
    render() {
        // let productList = [{
        //     id: 1,
        //     name: "JAPAN.TKY.BGP.Basic",
        //     price: 30,
        //     bandwidth: 200,
        //     monthlyTraffic: 60,
        //     stock: 5,
        // }, {
        //     id: 2,
        //     name: "JAPAN.TKY.BGP.Basic",
        //     price: 25,
        //     bandwidth: 500,
        //     monthlyTraffic: 60,
        //     stock: 2,
        // }, {
        //     id: 3,
        //     name: "JAPAN.TKY.BGP.Basic",
        //     price: 30,
        //     bandwidth: 200,
        //     monthlyTraffic: 60,
        //     stock: 14,
        // }, {
        //     id: 4,
        //     name: "JAPAN.TKY.BGP.Basic",
        //     price: 30,
        //     bandwidth: 300,
        //     monthlyTraffic: 60,
        //     stock: 15,
        // }, {
        //     id: 5,
        //     name: "JAPAN.TKY.BGP.Basic",
        //     price: 35,
        //     bandwidth: 500,
        //     monthlyTraffic: 60,
        //     stock: 1,
        // }];
        let productList = this.props.productList || [];

        let size = productList.length;
        let rows = size / 4 + 1;
        let colsRest = size % 4;

        let result = [];
        let index = 0;
        let cols = 4;
        for (let row = 0; row < rows; row++) {
            let colArr = []
            if (row === rows - 1) {
                cols = colsRest;
            } else {
                cols = 4;
            }
            for (let j = 0; j < cols; j++) {
                if (index < size) {
                    colArr.push(<Col key={productList[index].id} span={6}>
                        <Card title={productList[index].name} bordered={false}
                              hoverable={true}>
                            <div>{productList[index].price}元/月</div>
                            <div>{productList[index].monthlyTraffic} GB月流量</div>
                            <div>{productList[index].bandwidth}M 带宽</div>
                            <div className={"button-ctn"}>
                                <Button data-product-id={productList[index].id} onClick={this.props.onBuyBtnClick}
                                        size={"large"}>购买</Button>
                            </div>
                            <div className={"available-ctn"}>{productList[index].stock} Available</div>
                        </Card>
                    </Col>);
                    index++;
                }
            }
            let rowEle;
            if (row >= 1) {
                rowEle = <Row key={'row_' + row} style={{marginTop: 16}} gutter={16}>
                    {colArr}
                </Row>;
            } else {
                rowEle = <Row key={'row_' + row} gutter={16}>
                    {colArr}
                </Row>;
            }
            result.push(rowEle);
        }

        return (
            <div id={"products-module-ctn"}>
                {result}
            </div>
        )
    }
}

export default ProductCard;
