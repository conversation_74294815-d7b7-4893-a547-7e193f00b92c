<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.fsockmanageweb.mapper.IUserAccessLogMapper">
    <select id="findUserTrafficUsage" resultType="java.lang.String">
        select sum(download_traffic_usage) + sum(upload_traffic_usage) from tb_user_access_log where
        user_id = #{userId} and connect_time &gt;= #{startTime} and connect_time&lt;= #{endTime}
        and deleted=0
    </select>
</mapper>
