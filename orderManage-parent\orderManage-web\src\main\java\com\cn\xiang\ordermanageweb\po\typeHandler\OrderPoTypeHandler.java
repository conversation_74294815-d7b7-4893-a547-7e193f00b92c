package com.cn.xiang.ordermanageweb.po.typeHandler;

import com.cn.xiang.ordermanageweb.mapper.IOrdersMapper;
import com.cn.xiang.ordermanageweb.po.OrdersPo;
import com.cn.xiang.ordermanageweb.spring.ApplicationContextHolder;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/2/5 16:48
 */
public class OrderPoTypeHandler implements TypeHandler<OrdersPo> {
    @Override
    public void setParameter(PreparedStatement ps, int i, OrdersPo parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getId());
    }

    @Override
    public OrdersPo getResult(ResultSet rs, String columnName) throws SQLException {
        String orderId = rs.getString(columnName);
        IOrdersMapper ordersMapper = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IOrdersMapper.class);
        return ordersMapper.selectById(orderId);
    }

    @Override
    public OrdersPo getResult(ResultSet rs, int columnIndex) throws SQLException {
        String orderId = rs.getString(columnIndex);
        IOrdersMapper ordersMapper = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IOrdersMapper.class);
        return ordersMapper.selectById(orderId);
    }

    @Override
    public OrdersPo getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return null;
    }
}
