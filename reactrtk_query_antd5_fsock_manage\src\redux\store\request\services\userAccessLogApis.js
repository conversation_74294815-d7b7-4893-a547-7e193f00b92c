const url = 'userAccessLog';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getUserAccessLogList: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '?page=' + queryArg.page + '&pageSize=' + queryArg.pageSize,
                    method: 'GET',
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let records = content.list.records;
                for (let i = 0; i < records.length; i++) {
                    records[i].key = records[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
    }
}
