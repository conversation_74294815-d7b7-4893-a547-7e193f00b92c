package com.xiang.traffic.protocol;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.CompositeByteBuf;

/**
 * 代理阶段消息
 * +-------+------+---------------+
 * |  SVID |  LEN |    MESSAGE    |
 * +-------+------+---------------+
 * <p>
 * 目前SVID有五种类型：
 * 0x00：TCP、UDP流量转发请求与响应
 * 0x01：DNS域名解析请求、响应
 * 0x7E：PONG消息
 * 0x7F：PING消息
 * 0x7D: Disconnect消息
 * 0x7C: TrafficExhausted消息
 *
 * <AUTHOR>
 * @date 2024/6/6 15:18
 */
public abstract class ServiceStageMessage extends Message {

    public static final int LENGTH_FIELD_OFFSET = 1;

    public static final int LENGTH_FIELD_SIZE = 4;

    /**
     * 消息类型
     */
    private byte serviceId;


    protected ServiceStageMessage(byte serviceId) {
        super();
        this.serviceId = serviceId;
    }


    protected ServiceStageMessage(ByteBuf buf) throws SerializationException {
        super(buf);
    }


    @Override
    public final ByteBuf serialize(ByteBufAllocator allocator) throws SerializationException {
        ByteBuf header = allocator.directBuffer(5);
        header.writeByte(serviceId);

        ByteBuf body = serialize0(allocator);
        int length = body.readableBytes();
        header.writeInt(length);

        CompositeByteBuf buf = allocator.compositeBuffer(2);
        buf.addComponent(true, header);
        buf.addComponent(true, body);
        return buf;
    }

    /**
     * 序列化消息体(MESSAGE部分)
     */
    protected abstract ByteBuf serialize0(ByteBufAllocator allocator) throws SerializationException;


    @Override
    protected final void deserialize(ByteBuf buf) throws SerializationException {
        this.serviceId = buf.readByte();

        int length = buf.readInt();
        if (buf.readableBytes() != length) {
            throw new SerializationException(getClass(), "Length field is NOT equals to buf's readable bytes");
        }

        deserialize0(buf);
    }

    /**
     * 反序列化消息体(MESSAGE部分)
     */
    protected abstract void deserialize0(ByteBuf buf) throws SerializationException;

    /**
     * @return 消息类型ID
     */
    public final byte getServiceId() {
        return serviceId;
    }
}
