<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.fsockmanageweb.mapper.IOrdersMapper">

    <select id="listOrders" parameterType="String" resultType="java.util.Map">
        select
        orders.id,products.name,items.quantity,items.price,orders.total_amount,orders.status,orders.created_time,payments.payment_date
        from tb_orders orders
        inner join tb_order_items items on orders.id=items.order_id
        inner join tb_products products on items.product_id = products.id
        inner join tb_payments payments on payments.order_id = orders.id
        where orders.deleted=0
        and items.deleted=0
        and products.deleted=0
        and payments.deleted=0
        order by orders.created_time desc
    </select>

</mapper>
