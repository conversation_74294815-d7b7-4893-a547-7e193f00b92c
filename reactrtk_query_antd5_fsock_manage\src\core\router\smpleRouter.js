import global from '../utils/global'
import actions from '../../redux/actions'
import LifecycleMethodEnum from "../utils/LifecycleMethodEnum";
import Constants from "../utils/constants";

/***
 * 简单路由
 * @type {{selectTab: selectTab, open: open, close: close}}
 */
const SimpleRouter = (() => {
    /**
     * 在store找到moduleId=moduleId，触发该组件的pause生命周期
     * @param moduleId
     */
    const triggerModulePause = (moduleId) => {
        let state = global.store.getState();
        let cmptConfig = state.moduleContainer.cmptRefMap[moduleId];

        //TODO 暂时处理，组件没加载完，不触发pause生命周期
        // 加了遮罩后，应该不会出这问题
        if (cmptConfig.state === Constants.MODULE_STATE.LOADED) {
            let cmptRef = cmptConfig.component;

            //TODO 待处理，由于被pause生命周期的module还没完成渲染，没有注册到state.moduleContainer.cmptRefMap，导致获取到的cmptRef为undefined
            // console.log(state.moduleContainer.cmptRefMap);
            // console.log(cmptRef);

            if (cmptRef.props[LifecycleMethodEnum.MODULE_PAUSE]) {
                let moduleParam = global.ModuleParam.getModuleParam(moduleId);
                cmptRef.props[LifecycleMethodEnum.MODULE_PAUSE].call(cmptRef, moduleParam);
            }
        }
    };
    /**
     *
     * 在store找到moduleId=moduleId的模型，触发该组件的resume生命周期
     * @param moduleId
     */
    const triggerModuleResume = (moduleId) => {
        let state = global.store.getState();
        let cmptConfig = state.moduleContainer.cmptRefMap[moduleId];

        //TODO 暂时处理，组件没加载完，不触发pause生命周期
        // 加了遮罩后，应该不会出这问题
        if (cmptConfig.state === Constants.MODULE_STATE.LOADED) {
            let cmptRef = cmptConfig.component;
            if (cmptRef.props[LifecycleMethodEnum.MODULE_RESUME]) {
                let moduleParam = global.ModuleParam.getModuleParam(moduleId);
                cmptRef.props[LifecycleMethodEnum.MODULE_RESUME].call(cmptRef, moduleParam);
            }
        }
    };
    /**
     *在store找到moduleId=moduleId的模型，触发该组件的destroy生命周期
     * @param moduleId
     */
    const triggerModuleDestroy = (moduleId) => {
        let state = global.store.getState();
        let cmptRef = state.moduleContainer.cmptRefMap[moduleId].component;
        if (cmptRef.props[LifecycleMethodEnum.MODULE_DESTROY]) {
            let moduleParam = global.ModuleParam.getModuleParam(moduleId);
            cmptRef.props[LifecycleMethodEnum.MODULE_DESTROY].call(cmptRef, moduleParam);
        }

        //清除模型参数
        global.ModuleParam.removeModuleParam(moduleId);
    };

    /**
     * 选中操作tab
     * @param targetKey
     * @param pauseInvisableModuleCallback
     */
    const opearteTab = (targetKey, pauseInvisableModuleCallback) => {
        let state = global.store.getState();
        //如果当前的tab正在显示,则不处理
        let found = false;
        for (let moduleObj of state.moduleContainer.modules) {
            if (moduleObj.moduleId === targetKey) {
                if (moduleObj.isVisable) {
                    found = true;
                }
                break;
            }
        }
        if (found) {
            //not handle
            // return;
        } else {
            //执行触发将要隐藏的模型的pause生命周期的回调
            if (pauseInvisableModuleCallback) {
                pauseInvisableModuleCallback();
            }

            //模型已经打开，但隐藏状态，则选中该模型关联的tab
            //发布修改activeKey的action
            global.store.dispatch(actions.moduleTabs.selectTab(targetKey));

            //发布显示模型的action
            global.store.dispatch(actions.modulesContainer.displayModule(targetKey));

            //在store找到moduleId=targetKey的模型，触发该组件的resume生命周期
            triggerModuleResume(targetKey)
        }
    };
    /**
     * 选中tab，不触发前一选中tab的pause生命周期，只在SimpleRouter.close()中使用
     * @param targetKey
     */
    const selectTab = (targetKey) => {
        opearteTab(targetKey);
    };
    /**
     * 改变地址栏hash
     * @param href
     */
    const changeLocationHash = (href) => {
        // window.location.hash = href.replace(/\//, '');
        window.location.hash = href.replace(/\//g, '#');
        // window.location.hash = href;
    };

    let target = {
        switchTab: (targetKey) => {
            opearteTab(targetKey, () => {
                //暂停转变为隐藏的模型
                let state = global.store.getState();
                let preActiveKey = state.moduleTabs.activeKey;
                //在store找到moduleId=preActiveKey的模型，触发该组件的pause生命周期
                triggerModulePause(preActiveKey);
            });
            //改变地址栏hash
            changeLocationHash(targetKey);
        },
        open: (title, href, moduleParam) => {
            //处理页面参数
            if (moduleParam) {
                global.ModuleParam.setModuleParam(href, moduleParam);
            } else {
                //用原来的参数

                //如果没该模型层的参数对象，置为空
                if (!global.ModuleParam.getModuleParam(href)) {
                    global.ModuleParam.setModuleParam(href, {});
                }
            }

            let pathModuleMap = global.moduleRegister.getRegisterModuleMap();
            let module = pathModuleMap[href];
            if (module) {
                let state = global.store.getState();
                let cmptRefMap = {...state.moduleContainer.cmptRefMap};

                //如果模型已经打开
                if (href in cmptRefMap) {
                    global.store.dispatch(global.actions.moduleTabs.changeTabTitle(href, title));
                    target.switchTab(href);
                } else {
                    //触发转变为隐藏的模型的modulePause生命周期
                    //如果有大于1个panes
                    if (state.moduleTabs.panes && state.moduleTabs.panes.length > 0) {
                        let preActiveKey = state.moduleTabs.activeKey;
                        //在store找到moduleId=preActiveKey的模型，触发该组件的pause生命周期
                        triggerModulePause(preActiveKey);
                    }

                    //打开新的模型
                    global.store.dispatch(actions.moduleTabs.addTab({title: title, key: href}));
                    global.store.dispatch(actions.modulesContainer.pushModule(module, href));
                }
            } else {
                global.GlobalMessage.open({
                        type: global.GlobalMessage.TYPE.ERROR,
                        content: `the path[${href} is not matched module,please check config in utils/config/moduleConfig.js]`,
                        delay: 3,
                    }
                );
            }
            //改变地址栏hash
            changeLocationHash(href);
        },
        close: (href) => {
            let moduleId = href;
            if (!Reflect.has(global.moduleRegister.getRegisterModuleMap(), moduleId)) {
                console.log('no module map to moduleId[' + moduleId + ']! Please check the utils/config/moduleConfig.js');
                return;
            }

            let state = global.store.getState();
            let cmptConfig = state.moduleContainer.cmptRefMap[moduleId];
            if (!cmptConfig) {
                console.log('the module of moduleId[' + moduleId + '] is not being opened now! Do nothing for close.');
                return;
            }

            //在store找到moduleId=targetKey的模型，触发该组件的destroy生命周期
            triggerModuleDestroy(moduleId);

            //清除模型参数
            global.ModuleParam.removeModuleParam(moduleId);

            //删除前，先判断，如果删除的是当前选中的tab,则触发前一个tab选中,并触发resume生命周期
            let activeKey = state.moduleTabs.activeKey;
            let panes = state.moduleTabs.panes;
            let lastIndex;
            panes.forEach((pane, i) => {
                if (pane.key === moduleId) {
                    lastIndex = i - 1;
                }
            });

            //过滤出不是删除的panes
            panes = panes.filter(pane => pane.key !== moduleId);
            //如果删除的是选中的tab
            if (panes.length && activeKey === moduleId) {
                if (lastIndex >= 0) {
                    activeKey = panes[lastIndex].key;
                } else {
                    //如果删除的是第一个，lastIndex=-1，则选中第一个tab
                    activeKey = panes[0].key;
                }

                //选中该tab
                selectTab(activeKey);

                //改变地址hash
                changeLocationHash(activeKey);
            }

            //发布删除tab的action
            global.store.dispatch(actions.moduleTabs.removeTab(moduleId));

            //发布删除module的action
            global.store.dispatch(actions.modulesContainer.removeModule(moduleId));

            //发布注销cmptRefMap的action
            global.store.dispatch(actions.modulesContainer.unRegisterCmptRefMap(moduleId));

            //如果移除最后一个tab，把activeKey置空
            if (panes.length === 0) {
                global.store.dispatch(actions.moduleTabs.selectTab(''));
            }
        },
    };
    return target;
})();
export default SimpleRouter;
