package com.cn.xiang.mybaits.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cn.xiang.mybaits.po.UserAccessLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2024/2/5 23:11
 */
@Mapper
public interface IUserAccessLogMapper extends BaseMapper<UserAccessLogPo> {

    @Select("select sum(download_traffic_usage) + sum(upload_traffic_usage) from tb_user_access_log as userAccessLog, tb_user as user where userAccessLog.user_id = user.id and user.username = #{username}")
    public long getUserTrafficUsage(@Param("username") String username);
}
