<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="FrameworkDetectionExcludesConfiguration">
    <file type="web" url="file://$PROJECT_DIR$/fsockManage-parent/fsockManage-common" />
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/fsockManage/pom.xml" />
        <option value="$PROJECT_DIR$/fsockManage/common/pom.xml" />
        <option value="$PROJECT_DIR$/fsockManage-parent/fsockManage-web/pom.xml" />
        <option value="$PROJECT_DIR$/fsockManage-parent/pom.xml" />
        <option value="$PROJECT_DIR$/fsockManage-parent/fsockManage-common/fsockManage-mybaits/pom.xml" />
        <option value="$PROJECT_DIR$/flyingsocks-3.0/pom.xml" />
        <option value="$PROJECT_DIR$/flyingsocks-3.0/eureka-server/pom.xml" />
        <option value="$PROJECT_DIR$/traffic-3.0/pom.xml" />
        <option value="$PROJECT_DIR$/orderManage-parent/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" project-jdk-name="graalvm-21" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>