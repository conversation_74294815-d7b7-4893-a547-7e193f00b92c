const url = 'user/assignUserGroup';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        postAssignUserGroup: builder.query({

            query: (queryArg) => {
                let formData = new FormData();
                formData.set('userId', queryArg.userId);
                formData.set('groupId', queryArg.groupId);
                return {
                    url: url,
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                return response;
            },
            onQueryStarted: onQueryStarted
        }),
    }
}
