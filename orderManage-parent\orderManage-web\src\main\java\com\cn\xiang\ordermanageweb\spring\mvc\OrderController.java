package com.cn.xiang.ordermanageweb.spring.mvc;

import com.cn.xiang.ordermanageweb.services.IDeliverService;
import com.cn.xiang.ordermanageweb.services.IOrdersService;
import com.cn.xiang.ordermanageweb.spring.support.ControllerSupport;
import com.cn.xiang.ordermanageweb.utils.JSONResultMessage;
import com.cn.xiang.ordermanageweb.utils.enum_.OrderTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/api_v1/order")
public class OrderController extends ControllerSupport {
    @Autowired
    private IOrdersService ordersService;

    @Autowired
    private IDeliverService deliverService;

    @RequestMapping(value = "create", method = RequestMethod.POST)
    public Object create(String orderType, String productId) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.valueOf1(orderType);
        String payRedirectPage = ordersService.createOrder(orderTypeEnum, productId);
        this.getHttpSession().setAttribute("payRedirectPage", payRedirectPage);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        List<Map<String, String>> list = ordersService.listOrders();

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "cancel", method = RequestMethod.POST)
    public Object cancel(@Validated @NotEmpty String orderId) {
        ordersService.cancelOrder(orderId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "deliverDetail", method = RequestMethod.GET)
    public Object deliverDetail(@Validated @NotEmpty String orderId) {
        Map<String, String> deliverData = deliverService.deliverDetail(orderId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("deliverData", deliverData);
        return message;
    }
}
