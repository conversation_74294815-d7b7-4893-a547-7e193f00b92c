package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.services.IServerService;
import com.cn.xiang.fsockmanageweb.vo.ServerVo;
import com.cn.xiang.fsockmanageweb.po.ServerPo;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import com.fasterxml.jackson.databind.node.ArrayNode;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping("/api_v1/server")
public class ServerController extends ControllerSupport {
    @Autowired
    private IServerService serverService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list(String search) {
        ArrayNode list = serverService.listWithUserCount(search);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.POST)
    public Object add(@RequestBody @Validated(value = {ServerVo.AddGroup.class}) ServerVo serverVo) {
        ServerPo serverPo = new ServerPo();
        BeanUtils.copyProperties(serverVo, serverPo);
        serverService.addServer(serverPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Object modify(@RequestBody @Validated(value = {ServerVo.ModifyGroup.class}) ServerVo serverVo) {
        ServerPo serverPo = new ServerPo();
        BeanUtils.copyProperties(serverVo, serverPo);
        serverService.updateById(serverPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public Object del(@Validated @NotEmpty String id) {
        serverService.removeById(id);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "/uploadZipToServer", method = RequestMethod.POST)
    public Object uploadZipToServer(@Validated @NotEmpty String id, @NotEmpty String packageResourceId) {
        JSONResultMessage message = this.getJSONResultMessage();
        try {
            serverService.uploadZipToServer(id, packageResourceId);
            message.success();
        } catch (Exception e) {
            message.error(e.getMessage());
        }
        return message;
    }

    @RequestMapping(value = "/modifyServerConfig", method = RequestMethod.POST)
    public Object modifyServerConfig(@Validated @NotEmpty String id) {
        JSONResultMessage message = this.getJSONResultMessage();
        try {
            serverService.modifyServerConfig(id);
            message.success();
        } catch (Exception e) {
            message.error(e.getMessage());
        }
        return message;
    }

    @RequestMapping(value = "/installDockerToServer", method = RequestMethod.POST)
    public Object installDockerToServer(@Validated @NotEmpty String id) {
        JSONResultMessage message = this.getJSONResultMessage();
        try {
            serverService.installDockerToServer(id);
            message.success();
        } catch (Exception e) {
            message.error(e.getMessage());
        }
        return message;
    }

    @RequestMapping(value = "/startServer", method = RequestMethod.POST)
    public Object startServer(@Validated @NotEmpty String id) {
        serverService.startServer(id);
        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "/autoStart", method = RequestMethod.POST)
    public Object autoStart(@Validated @NotEmpty String id, @NotEmpty String packageResourceId) {
        JSONResultMessage message = this.getJSONResultMessage();
        serverService.autoStart(id, packageResourceId);
        message.success();
        return message;
    }

    @RequestMapping(value = "/stopServer", method = RequestMethod.POST)
    public Object stopServer(@Validated @NotEmpty String id) {
        JSONResultMessage message = this.getJSONResultMessage();
        try {
            serverService.stopServer(id);
            message.success();
        } catch (Exception e) {
            message.error(e.getMessage());
        }
        return message;
    }
}
