package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.po.MenuPo;
import com.cn.xiang.fsockmanageweb.services.IMenuService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api_v1/menu")
public class MenuController extends ControllerSupport {
    @Autowired
    private IMenuService menuService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        List<MenuPo> list = menuService.list();

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "/save", method = RequestMethod.GET)
    public Object save() {
        MenuPo menuPo = new MenuPo();
        menuPo.setPid("");
        menuPo.setType("sub");
        menuPo.setName("监控");
        menuPo.setIconType("HomeOutlined");
        menuPo.setHref("");
        menuService.save(menuPo);

        menuPo = new MenuPo();
        menuPo.setPid("");
        menuPo.setType("sub");
        menuPo.setName("系统");
        menuPo.setIconType("HomeOutlined");
        menuPo.setHref("");
        menuService.save(menuPo);

//        MenuPo menuPo = new MenuPo();
//        menuPo.setPid("");
//        menuPo.setType("item");
//        menuPo.setName("sub2_item1");
//        menuPo.setIconType("HomeOutlined");
//        menuPo.setHref("#indexModule5");
//        menuService.save(menuPo);

//        menuPo = new MenuPo();
//        menuPo.setPid("");
//        menuPo.setType("item");
//        menuPo.setName("用户访问日志管理");
//        menuPo.setIconType("GlobalOutlined");
//        menuPo.setHref("#userAccessLogModule");
//        menuService.save(menuPo);
//
//        menuPo = new MenuPo();
//        menuPo.setPid("");
//        menuPo.setType("item");
//        menuPo.setName("组管理");
//        menuPo.setIconType("UsergroupAddOutlined");
//        menuPo.setHref("#groupModule");
//        menuService.save(menuPo);
//
//        menuPo = new MenuPo();
//        menuPo.setPid("");
//        menuPo.setType("item");
//        menuPo.setName("流量规则管理");
//        menuPo.setIconType("LineChartOutlined");
//        menuPo.setHref("#trafficRuleModule");
//        menuService.save(menuPo);


        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }
}
