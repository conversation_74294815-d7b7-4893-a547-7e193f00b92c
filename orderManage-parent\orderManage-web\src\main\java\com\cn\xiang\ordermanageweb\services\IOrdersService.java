package com.cn.xiang.ordermanageweb.services;

import com.alipay.api.AlipayApiException;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.ordermanageweb.po.OrdersPo;
import com.cn.xiang.ordermanageweb.utils.enum_.OrderTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/7 19:57
 */
public interface IOrdersService extends IService<OrdersPo> {
    String createOrder(OrderTypeEnum orderTypeEnum, String productId);

    List<Map<String, String>> listOrders();

    void cancelOrder(String orderId);
}
