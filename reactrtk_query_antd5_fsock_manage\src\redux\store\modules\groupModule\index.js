import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Space, Table} from "antd";
import GroupModifyModal from "../../../../components/group/modal/GroupModifyModal";
import GroupDelModal from "../../../../components/group/modal/GroupDelModal";
import GroupAddModal from "../../../../components/group/modal/GroupAddModal";
import Link from "../../components/link/container";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource', itemKeyName: 'key', isForwardedRef: false
});

class GroupModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true,
            selectedRowKeys: []
        }

        this.tableRef = React.createRef();
        this.addModalRef = React.createRef();
        this.modifyModalRef = React.createRef();
        this.delModalRef = React.createRef();
    }

    selectedRows;

    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            btnDisabled: false,
            selectedRowKeys: selectedRowKeys
        })
    }

    fillForm = () => {
        let modalInst = this.modifyModalRef.current;
        let formInst = modalInst.formRef.current;
        let row = this.selectedRows[0];

        formInst.setFieldsValue({
            id: row.id, name: row.name
        });
    }

    addBtnOnClick = (event) => {
        //打开模态框
        this.addModalRef.current.setState({
            visible: true
        });
    };

    modifyBtnOnClick = (event) => {
        //打开模态框
        this.modifyModalRef.current.setState({
            visible: true
        }, () => {
            //setState第二参数，在组件更新完调用
            this.fillForm();
        });
    };

    delBtnOnClick = (event) => {
        //打开模态框
        this.delModalRef.current.setState({
            visible: true
        });
    };

    render() {
        return (<div>
            <Space style={{marginBottom: 16}}>
                <Button type="primary" onClick={this.addBtnOnClick}>
                    添加
                </Button>
                <Button type="primary" disabled={this.state.btnDisabled} onClick={this.modifyBtnOnClick}>
                    修改
                </Button>
                <Button type="primary" disabled={this.state.btnDisabled} onClick={this.delBtnOnClick}>
                    删除
                </Button>
            </Space>
            <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={{
                type: 'radio',
                selectedRowKeys: this.state.selectedRowKeys,
                onChange: this.onChange
            }}>
                <Column align={"center"} title="组名" dataIndex="name"/>
                <Column align={"center"} title="流量规则" dataIndex={['trafficRulePo', 'name']}
                        render={(text, record, index) => {
                            let trafficRuleName = record['trafficRulePo']['name'];
                            if (!trafficRuleName) {
                                trafficRuleName = '--'
                            }

                            let trafficRuleId = record['trafficRulePo']['id'];
                            let groupId = record['id'];
                            let groupName = record['name'];

                            return <Link key={record.id} href={"#groupModule#groupTrafficRuleAssign"}
                                         data={{
                                             'trafficRuleId': trafficRuleId, 'groupId': groupId
                                         }}
                                         menuName={groupName + '组流量规则分配'}
                            >{trafficRuleName}</Link>
                        }}
                />
                <Column align={"center"} title="月流量" dataIndex={['trafficCapacityPo', 'trafficCapacityHuman']}
                        render={(text, record, index) => {
                            let trafficCapacityName = record['trafficCapacityPo']['trafficCapacityHuman'];
                            if (!trafficCapacityName) {
                                trafficCapacityName = '--'
                            }

                            let trafficCapacityId = record['trafficCapacityPo']['id'];
                            let groupId = record['id'];
                            let groupName = record['name'];

                            return <Link key={record.id} href={"#groupModule#groupTrafficCapacityAssignModule"}
                                         data={{
                                             'trafficCapacityId': trafficCapacityId, 'groupId': groupId
                                         }}
                                         menuName={groupName + '组月流量分配'}
                            >{trafficCapacityName}</Link>
                        }}
                />
            </TableWrapper>
            <GroupAddModal ref={this.addModalRef} userModalRef={this} handleAddOk={() => {
                return this.props.handleAddOk.call(this);
            }}/>
            <GroupModifyModal ref={this.modifyModalRef} userModalRef={this} handleModifyOk={() => {
                return this.props.handleModifyOk.call(this);
            }}/>
            <GroupDelModal ref={this.delModalRef} handleDelOk={() => {
                return this.props.handleDelOk.call(this);
            }}/>
        </div>)
    }
}

export default GroupModule;
