package com.cn.xiang.fsockmanageweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cn.xiang.fsockmanageweb.po.GroupPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @date 2024/2/5 15:36
 */
@Mapper
public interface IGroupMapper extends BaseMapper<GroupPo> {
    GroupPo findGroupByName(@Param("groupName") String groupName);
//    List<GroupPo> listBySearch(@Param("groupName") String groupName);
}
