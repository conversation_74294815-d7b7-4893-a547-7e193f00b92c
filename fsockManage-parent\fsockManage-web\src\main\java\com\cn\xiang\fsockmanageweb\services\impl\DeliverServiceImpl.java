package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.mapper.IDeliverMapper;
import com.cn.xiang.fsockmanageweb.po.*;
import com.cn.xiang.fsockmanageweb.services.*;
import com.cn.xiang.fsockmanageweb.utils.enum_.OrderStatusEnum;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class DeliverServiceImpl extends ServiceImpl<IDeliverMapper, DeliverPo> implements IDeliverService {
    @Autowired
    private IUserService userService;
    @Autowired
    private IOrdersService ordersService;
    @Autowired
    private IServerService serverService;

    @Autowired
    private IOrderSysUserService orderSysUserService;

    @Autowired
    private IOrderItemsService orderItemsService;

    @Autowired
    private IProductsService productsService;

    @DS("order")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void save1(DeliverPo deliverPo) {
        this.save(deliverPo);
    }

    @DS("order")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public DeliverPo getOne1(LambdaQueryWrapper<DeliverPo> eq) {
        return this.getOne(eq);
    }


    @Override
    public boolean deliver(String userId, String orderId) {
        UserPo userPo = userService.getById(userId);
        ServerPo serverPo = serverService.findServerByUserId(userId);
        if (serverPo == null) {
            throw new RuntimeException("该用户未分配服务器");
        }

        OrdersPo ordersPo = ordersService.getById1(orderId);

        if (userPo.isDelivered()) {
            throw new RuntimeException("该用户已发货");
        }

        if (ordersPo.getStatus().equals(OrderStatusEnum.ORDER_STATUS_PAID.getCode())) {
            DeliverPo deliverPo = new DeliverPo();
            deliverPo.setUserPo(userPo);
            deliverPo.setOrdersPo(ordersPo);
            deliverPo.setServerPo(serverPo);

            IDeliverService deliverService = (IDeliverService) AopContext.currentProxy();
            deliverService.save1(deliverPo);

            //更新订单状态
            ordersPo.setStatus(OrderStatusEnum.ORDER_STATUS_DELIVERED.getCode());
            ordersService.updateById1(ordersPo);

            //更新用户有效时间
            LocalDateTime deliverTime = deliverPo.getUpdatedTime();
            OrderItemsPo orderItemsPo = orderItemsService.getOne1(new LambdaQueryWrapper<OrderItemsPo>().eq(OrderItemsPo::getOrderId, orderId));
            int quantity = orderItemsPo.getQuantity();

            deliverTime = deliverTime.plusMonths(quantity);
            String formatDateTime = deliverTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            userPo.setExpiration(formatDateTime);

            //标注用户已发货
            userPo.setDelivered(true);
            userService.updateById(userPo);
            return true;
        }
        return false;
    }

    @DS("order")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public Map<String, String> deliverDetail(String orderId) {
        //order库
        OrdersPo ordersPo = ordersService.getById(orderId);
        OrderSysUserPo orderSysUserPo = orderSysUserService.getById(ordersPo.getUserId());
        OrderItemsPo orderItemsPo = orderItemsService.getOne(new LambdaQueryWrapper<OrderItemsPo>().eq(OrderItemsPo::getOrderId, orderId));
        ProductsPo productsPo = productsService.getById(orderItemsPo.getProductId());
        DeliverPo deliverPo = this.getOne(new LambdaQueryWrapper<DeliverPo>().eq(DeliverPo::getOrdersPo, ordersPo.getId()));

        //flying库
        ServerPo serverPo = serverService.getById1(deliverPo.getServerPo().getId());

        Map<String, String> result = new HashMap<>();
        result.put("orderId", orderId);
        result.put("productName", productsPo.getName());
        result.put("price", orderItemsPo.getPrice() + "");
        result.put("quantity", orderItemsPo.getQuantity() + "");

        result.put("totalPrice", ordersPo.getTotalAmount() + "");

        result.put("buyer", orderSysUserPo.getUsername());
        result.put("username", deliverPo.getUserPo().getUsername());
        result.put("password", deliverPo.getUserPo().getPassword());
        result.put("userExpiration", deliverPo.getUserPo().getExpiration());
        result.put("serverIp", serverPo.getIp());
        result.put("serverPort", serverPo.getPort());
        result.put("serverCertPort", serverPo.getCertPort());
        result.put("orderStatus", ordersPo.getStatus());

        return result;
    }


    @Override
    public void deliverConfirm(String orderId) {
        OrdersPo ordersPo = ordersService.getById1(orderId);
        if (ordersPo.getStatus().equals(OrderStatusEnum.ORDER_STATUS_DELIVERED.getCode())) {
            ordersPo.setStatus(OrderStatusEnum.ORDER_STATUS_COMPLETED.getCode());
            ordersService.updateById1(ordersPo);
        }
    }
}
