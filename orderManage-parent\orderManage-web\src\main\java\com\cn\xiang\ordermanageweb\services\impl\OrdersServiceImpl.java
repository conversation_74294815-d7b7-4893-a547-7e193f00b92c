package com.cn.xiang.ordermanageweb.services.impl;

import com.alipay.api.AlipayApiException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.ordermanageweb.mapper.IOrdersMapper;
import com.cn.xiang.ordermanageweb.mapper.IPaymentsMapper;
import com.cn.xiang.ordermanageweb.po.*;
import com.cn.xiang.ordermanageweb.services.*;
import com.cn.xiang.ordermanageweb.utils.enum_.OrderStatusEnum;
import com.cn.xiang.ordermanageweb.utils.enum_.OrderTypeEnum;
import com.cn.xiang.ordermanageweb.utils.enum_.PayMethodEnum;
import com.cn.xiang.ordermanageweb.utils.enum_.PayStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class OrdersServiceImpl extends ServiceImpl<IOrdersMapper, OrdersPo> implements IOrdersService {
    @Autowired
    private IOrderItemsService orderItemsService;

    @Autowired
    private IProductsService productsService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IOrdersMapper ordersMapper;

    @Autowired
    private IPaymentsMapper paymentsMapper;

    @Autowired
    private IAlipayService alipayService;


    public String createOrder(OrderTypeEnum orderTypeEnum, String productId) {
        //获取用户
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        User user = (User) authentication.getPrincipal();
        SysUserPo sysUserPo = sysUserService.queryByUsername(user.getUsername());

        ProductsPo productsPo = productsService.getById(productId);

        int quantity = 1;
        //扣库存加锁
        synchronized (this) {
            if (orderTypeEnum.equals(OrderTypeEnum.ORDER_TYPE_MONTHLY)) {
            } else if (orderTypeEnum.equals(OrderTypeEnum.ORDER_TYPE_THREE_MONTH)) {
                quantity = quantity * 3;
            }
            //检查库存
            if (productsPo.getStock() < quantity) {
                throw new RuntimeException("库存不足");
            }

            //扣库存
            productsPo.setStock(productsPo.getStock() - quantity);
            productsService.updateById(productsPo);
        }

        //创建订单
        OrdersPo ordersPo = new OrdersPo();
        ordersPo.setStatus(OrderStatusEnum.ORDER_STATUS_CREATED.getCode());
        ordersPo.setUserId(sysUserPo.getId());
        ordersPo.setTotalAmount(productsPo.getPrice() * quantity);
        this.save(ordersPo);

        //创建订单项
        OrderItemsPo orderItemsPo = new OrderItemsPo();
        orderItemsPo.setProductId(productId);
        orderItemsPo.setOrderId(ordersPo.getId());
        orderItemsPo.setQuantity(quantity);
        orderItemsPo.setPrice(productsPo.getPrice());
        orderItemsService.save(orderItemsPo);

        //创建待支付记录
        PaymentsPo paymentsPo = new PaymentsPo();
        paymentsPo.setAmount(ordersPo.getTotalAmount());
        paymentsPo.setOrderId(ordersPo.getId());
        paymentsPo.setPaymentMethod(PayMethodEnum.PAY_METHOD_ALIPAY.getCode());
        paymentsPo.setStatus(PayStatusEnum.PAY_STATUS_CREATED.getCode());
        paymentsMapper.insert(paymentsPo);

        //跳转alipay
        try {
            return alipayService.generatePay(ordersPo.getId());
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public List<Map<String, String>> listOrders() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        User user = (User) authentication.getPrincipal();
        SysUserPo sysUserPo = sysUserService.queryByUsername(user.getUsername());

        List<Map<String, String>> list = ordersMapper.listOrdersByUser(sysUserPo.getId());
        for (Map<String, String> map : list) {
            String totalAmount = map.get("total_amount");
            String createdTime = map.get("created_time");
            String paymentDate = map.get("payment_date");
            map.remove("total_amount");
            map.remove("created_time");
            map.remove("payment_date");

            map.put("totalAmount", totalAmount);
            map.put("createdTime", createdTime);
            map.put("paymentDate", paymentDate);
        }

        return list;
    }

    @Override
    public void cancelOrder(String orderId) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        User user = (User) authentication.getPrincipal();
        SysUserPo sysUserPo = sysUserService.queryByUsername(user.getUsername());

        OrdersPo ordersPo = this.getById(orderId);
        if (!sysUserPo.getId().equals(ordersPo.getUserId())) {
            throw new RuntimeException("取消订单失败");
        }

        ordersPo.setStatus(OrderStatusEnum.ORDER_STATUS_CANCELED.getCode());
        this.updateById(ordersPo);

        //支付记录标注为取消
        PaymentsPo paymentsPo = paymentsMapper.selectOne(new QueryWrapper<PaymentsPo>().eq("order_id", orderId));
        paymentsPo.setStatus(PayStatusEnum.PAY_STATUS_CANCELED.getCode());
        paymentsMapper.updateById(paymentsPo);

        //返还库存
        List<OrderItemsPo> orderItemsPoList = orderItemsService.list(new QueryWrapper<OrderItemsPo>().eq("order_id", orderId));
        for (OrderItemsPo orderItemsPo : orderItemsPoList) {
            ProductsPo productsPo = productsService.getById(orderItemsPo.getProductId());
            productsPo.setStock(productsPo.getStock() + orderItemsPo.getQuantity());
            productsService.updateById(productsPo);
        }
    }

}
