import React, {Component} from 'react';
import Menu from './menu';

class LeftMenu extends Component {
    constructor(props) {
        super(props);
        this.config = {
            style: {height: '100%', borderRight: 0},
            theme: 'dark',
            mode: 'inline',
            defaultSelectedKeys: ['1778059605595947010'],
            defaultOpenKeys: ['1778062943083356161'],
            //使用需要链接
            hasLink: true,
        }
    }

    componentDidMount() {
        this.props.loadLeftMenu();
    }

    selectFirstItem() {
        //选中展开的子菜单第一个菜单项
        let liEle = document.querySelector('.ant-layout-sider-children>ul.ant-menu>li.ant-menu-submenu-open>ul.ant-menu>li.ant-menu-item:first-child a');
        if (liEle) {
            liEle.click();
        }
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        //如果菜单数据更新了，选中第一个菜单选项
        if (this.props.data && prevProps.data !== this.props.data) {
            this.selectFirstItem();
        }
    }

    render() {
        return (<Menu data={this.props.data} config={this.config} openKeys={this.config.defaultOpenKeys}/>);
    }
}

export default LeftMenu;
