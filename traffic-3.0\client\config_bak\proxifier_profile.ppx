<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ProxifierProfile version="101" platform="Windows" product_id="0" product_minver="310">
  <Options>
    <Resolve>
      <AutoModeDetection enabled="false" />
      <ViaProxy enabled="true">
        <TryLocalDnsFirst enabled="false" />
      </ViaProxy>
      <ExclusionList></ExclusionList>
      <DnsUdpMode>2</DnsUdpMode>
    </Resolve>
    <Encryption mode="basic" />
    <HttpProxiesSupport enabled="false" />
    <HandleDirectConnections enabled="false" />
    <ConnectionLoopDetection enabled="true" />
    <ProcessServices enabled="false" />
    <ProcessOtherUsers enabled="false" />
  </Options>
  <ProxyList>
    <Proxy id="100" type="SOCKS5">
      <Address>127.0.0.1</Address>
      <Port>1100</Port>
      <Options>48</Options>
      <Authentication enabled="true">
        <Username>dsafewaf</Username>
        <Password>AAACw/XR0MiIhpFUkwrm7DZLNgU19ADwwGHtBVnBUkLgl7o=</Password>
      </Authentication>
    </Proxy>
    <Proxy id="101" type="HTTPS">
      <Address>127.0.0.1</Address>
      <Port>1110</Port>
      <Options>48</Options>
      <Authentication enabled="true">
        <Username>$usfde21ser</Username>
        <Password>AAAC7jxoNES3INi57fwg5F0yAgLfWO0VDLCSi66VWy0p2Mw=</Password>
      </Authentication>
    </Proxy>
    <Proxy id="102" type="SOCKS5">
      <Address>************</Address>
      <Port>1100</Port>
      <Options>48</Options>
      <Authentication enabled="true">
        <Username>dsafewaf</Username>
        <Password>AAACw/XR0MiIhpFUkwrm7DZLNj1boIhLoXtcvYpATNB/CmA=</Password>
      </Authentication>
    </Proxy>
  </ProxyList>
  <ChainList />
  <RuleList>
    <Rule enabled="true">
      <Name>localhost</Name>
      <Targets>127.0.0.1</Targets>
      <Action type="Direct" />
    </Rule>
    <Rule enabled="true">
      <Name>新建</Name>
      <Targets>************;**************;</Targets>
      <Ports>1090</Ports>
      <Action type="Direct" />
    </Rule>
    <Rule enabled="true">
      <Name>新建</Name>
      <Targets>************;**************;</Targets>
      <Ports>7060</Ports>
      <Action type="Direct" />
    </Rule>
    <Rule enabled="true">
      <Name>新建</Name>
      <Targets>192.168.0.*</Targets>
      <Action type="Direct" />
    </Rule>
    <Rule enabled="true">
      <Name>新建</Name>
      <Applications>360chromex.exe</Applications>
      <Action type="Proxy">100</Action>
    </Rule>
    <Rule enabled="true">
      <Name>新建</Name>
      <Applications>chrome.exe</Applications>
      <Action type="Proxy">100</Action>
    </Rule>
    <Rule enabled="true">
      <Name>Default</Name>
      <Action type="Direct" />
    </Rule>
  </RuleList>
</ProxifierProfile>