package com.xiang.traffic.client.gui.swt;

import com.xiang.traffic.AbstractModule;
import com.xiang.traffic.Config;
import com.xiang.traffic.client.ClientOperator;
import com.xiang.traffic.client.GlobalConfig;
import com.xiang.traffic.client.gui.ResourceManager;
import com.xiang.traffic.client.proxy.http.HttpProxyConfig;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import java.util.function.Consumer;

import static com.xiang.traffic.client.gui.swt.Utils.*;

/**
 * HTTP本地代理设置界面
 *
 * <AUTHOR>
 * @date 2024/6/6 15:01
 */
public class HttpProxySettingModule extends AbstractModule<SWTViewComponent> {

    public static final String NAME = HttpProxySettingModule.class.getSimpleName();

    private final ClientOperator operator;

    private final Shell shell;

    HttpProxySettingModule(SWTViewComponent component) {
        super(Objects.requireNonNull(component), NAME);
        this.operator = component.getParentComponent();

        Image icon;
        try (InputStream is = ResourceManager.openIconImageStream()) {
            icon = loadImage(is);
        } catch (IOException e) {
            throw new Error(e);
        }

        this.shell = createShell(component.getDisplay(), "swtui.http.title", icon, 600, 280);
        initial();
        adaptDPI(shell);
    }

    private void initial() {
        createLabel(shell, "swtui.http.form.label.switch", 20, 5, 80, 30, SWT.CENTER);
        createLabel(shell, "swtui.http.form.label.port", 20, 40, 80, 30, SWT.CENTER);
        createLabel(shell, "swtui.http.form.label.validate", 20, 75, 80, 30, SWT.CENTER);
        createLabel(shell, "swtui.http.form.label.username", 20, 110, 80, 30, SWT.CENTER);
        createLabel(shell, "swtui.http.form.label.password", 20, 145, 80, 30, SWT.CENTER);

        Composite switchComp = new Composite(shell, SWT.NONE);
        switchComp.setBounds(20, 5, 380, 30);
        Button openRadio = createRadio(switchComp, "swtui.http.form.button.switch_open", 140, 5, 80, 30);
        Button closeRadio = createRadio(switchComp, "swtui.http.form.button.switch_close", 230, 5, 80, 30);
        addButtonSelectionListener(openRadio, e -> {
            openRadio.setSelection(true);
            closeRadio.setSelection(false);
        });
        addButtonSelectionListener(closeRadio, e -> {
            openRadio.setSelection(false);
            closeRadio.setSelection(true);
        });

        Text portText = new Text(shell, SWT.BORDER);
        portText.setBounds(160, 40, 380, 30);

        Composite authComp = new Composite(shell, SWT.NONE);
        authComp.setBounds(20, 75, 380, 30);
        Button authOpenRadio = createRadio(authComp, "swtui.http.form.button.validate_open", 140, 0, 80, 30);
        Button authCloseRadio = createRadio(authComp, "swtui.http.form.button.validate_close", 230, 0, 80, 30);
        addButtonSelectionListener(authCloseRadio, e -> {
            authCloseRadio.setSelection(true);
            authOpenRadio.setSelection(false);
        });
        addButtonSelectionListener(authOpenRadio, e -> {
            authCloseRadio.setSelection(false);
            authOpenRadio.setSelection(true);
        });

        Text userText = new Text(shell, SWT.BORDER);
        userText.setBounds(160, 110, 380, 30);

        Text passText = new Text(shell, SWT.BORDER | SWT.PASSWORD);
        passText.setBounds(160, 145, 380, 30);

        Button enterBtn = createButton(shell, "swtui.http.form.button.enter", 140, 180, 150, 35);
        Button cancelBtn = createButton(shell, "swtui.http.form.button.cancel", 330, 180, 150, 35);

        addButtonSelectionListener(enterBtn, e -> {
            boolean open = openRadio.getSelection();
            boolean auth = authOpenRadio.getSelection();
            int port;
            try {
                port = Integer.parseInt(portText.getText());
            } catch (NumberFormatException nfe) {
                showMessageBox(shell, "swtui.http.notice.error.title", "swtui.http.notice.error.port_error", SWT.ICON_ERROR | SWT.OK);
                return;
            }
            String username = userText.getText();
            String password = passText.getText();

            if (auth && StringUtils.isAnyBlank(username, password)) {
                showMessageBox(shell, "swtui.http.notice.error.title", "swtui.http.notice.error.auth_error", SWT.ICON_ERROR | SWT.OK);
                return;
            }

            operator.updateHttpProxyConfig(open, port, auth, username, password);
            showMessageBox(shell, "swtui.http.notice.error.title", "swtui.http.notice.update_success", SWT.ICON_INFORMATION | SWT.OK);
        });

        if (operator.isHttpProxyOpen()) {
            openRadio.setSelection(true);
        } else {
            closeRadio.setSelection(true);
        }

        HttpProxyConfig cfg = operator.getHttpProxyConfig();
        Consumer<HttpProxyConfig> updater = config -> {
            if (config == null) {
                closeRadio.setSelection(true);
                authCloseRadio.setSelection(true);
                return;
            }

            portText.setText(String.valueOf(config.getBindPort()));
            if (config.getUsername() != null) {
                userText.setText(config.getUsername());
            }
            if (config.getPassword() != null) {
                passText.setText(config.getPassword());
            }
            if (config.isAuth()) {
                authOpenRadio.setSelection(true);
            } else {
                authCloseRadio.setSelection(true);
            }
        };

        if (cfg != null) {
            updater.accept(cfg);
        } else {
            closeRadio.setSelection(true);
            authCloseRadio.setSelection(true);
        }

        addButtonSelectionListener(cancelBtn, e -> {
            if (operator.isHttpProxyOpen()) {
                openRadio.setSelection(true);
            } else {
                closeRadio.setSelection(true);
            }
            HttpProxyConfig hpc = operator.getHttpProxyConfig();
            updater.accept(hpc);
            setVisiable(false);
        });

        operator.registerConfigEventListener(event -> {
            if (Config.UPDATE_EVENT.equals(event.getEvent()) && event.getSource() instanceof HttpProxyConfig) {
                HttpProxyConfig hpc = (HttpProxyConfig) event.getSource();
                updater.accept(hpc);
            }
        });

        operator.registerConfigEventListener(event -> {
            if (Config.UPDATE_EVENT.equals(event.getEvent()) && event.getSource() instanceof GlobalConfig) {
                GlobalConfig gc = (GlobalConfig) event.getSource();
                if (gc.isEnableHttpProxy()) {
                    openRadio.setSelection(true);
                } else {
                    closeRadio.setSelection(true);
                }
            }
        });
    }

    void setVisiable(boolean visiable) {
        shell.setVisible(visiable);
    }
}
