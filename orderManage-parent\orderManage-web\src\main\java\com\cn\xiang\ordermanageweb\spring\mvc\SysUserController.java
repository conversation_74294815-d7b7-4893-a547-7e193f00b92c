package com.cn.xiang.ordermanageweb.spring.mvc;

import com.cn.xiang.ordermanageweb.spring.support.ControllerSupport;
import com.cn.xiang.ordermanageweb.utils.JSONResultMessage;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/api_v1/sysUser")
public class SysUserController extends ControllerSupport {
    @RequestMapping(value = "isLogined", method = RequestMethod.GET)
    public Object isLogined() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        JSONResultMessage message = this.getJSONResultMessage();
        if (authentication instanceof UsernamePasswordAuthenticationToken) {
            message.addParameter("isLogined", authentication.isAuthenticated());
            message.success();
        } else {
            message.addParameter("isLogined", false);
            message.success();
        }

        return message;
    }
}
