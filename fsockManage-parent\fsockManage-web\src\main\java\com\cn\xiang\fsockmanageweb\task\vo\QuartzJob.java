package com.cn.xiang.fsockmanageweb.task.vo;

import com.cn.xiang.fsockmanageweb.task.utils.JobInvokeUtil;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

/**
 * <AUTHOR>
 * @date 2024/7/3 15:14
 */
public class QuartzJob extends QuartzJobBean {

    public static final String SCHEDULE_JOB = "SCHEDULE_JOB_KEY";

    @Override
    protected void executeInternal(JobExecutionContext context) {
        // 获取到ScheduleJob对象
        ScheduleJob scheduleJob = (ScheduleJob) context.getMergedJobDataMap().get(SCHEDULE_JOB);
        // 在这个方法中，通过ScheduleJob对象中的类创建对象，并触发指定的方法
        JobInvokeUtil.invokeMethod(scheduleJob);
    }
}
