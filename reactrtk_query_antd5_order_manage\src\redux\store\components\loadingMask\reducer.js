import {createSlice} from '@reduxjs/toolkit';

let initialState = false;

const loadingMaskSlice = createSlice({
    name: 'loadingMaskSlice',
    initialState,
    reducers: {
        showMask: (state, action) => {
            // 更新状态的两种方式：
            // 1、可以通过immer.js的方式，直接修改state,redux-toolkit文档使用的方式
            // 2、不修改state，而是return一个新的状态对象，redux原始写法使用的方式
            return action.payload;
        }
    },
});

let result = {
    reducer: {
        //loadingMash就是挂在store上的key
        loadingMask: loadingMaskSlice.reducer
    },
    actions: loadingMaskSlice.actions
}

export default result;
