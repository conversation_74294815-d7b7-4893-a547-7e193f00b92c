
# Traffic Client - GraalVM Native Image 编译指南

本项目支持使用 GraalVM Native Image 将 Java 应用编译成原生可执行文件，提供更快的启动速度和更小的内存占用。

## 前置要求

### 1. 安装 GraalVM
- 下载并安装 GraalVM 23.1.0 或更高版本
- 下载地址：https://github.com/graalvm/graalvm-releases
- 将 GraalVM 的 `bin` 目录添加到系统 PATH 环境变量

### 2. 安装 Native Image 组件
```bash
# 安装 native-image 组件
gu install native-image
```

### 3. 系统要求
- **Windows**: 需要安装 Microsoft Visual C++ Build Tools 或 Visual Studio
- **Linux**: 需要安装 GCC 编译器和相关开发工具
- **macOS**: 需要安装 Xcode Command Line Tools

## 编译方法

### 方法一：使用编译脚本（推荐）

#### Windows
```cmd
# 在 client 目录下执行
build-native-windows.bat
```

#### Linux/macOS
```bash
# 在 client 目录下执行
chmod +x build-native-linux.sh
./build-native-linux.sh
```

### 方法二：使用 Maven 插件
```bash
# 构建 JAR 包
mvn clean package -DskipTests

# 编译 Native Image
mvn native:compile -Pnative
```

### 方法三：手动编译
```bash
# 1. 构建 JAR 包
mvn clean package -DskipTests

# 2. 使用 native-image 命令编译
native-image \
    --no-fallback \
    --enable-http \
    --enable-https \
    --enable-all-security-services \
    --allow-incomplete-classpath \
    --report-unsupported-elements-at-runtime \
    --initialize-at-build-time=org.slf4j \
    --initialize-at-run-time=io.netty,com.xiang.traffic.client.gui \
    -H:+ReportExceptionStackTraces \
    -H:+AddAllCharsets \
    -H:IncludeResources=".*\.properties$" \
    -H:IncludeResources=".*\.yml$" \
    -H:IncludeResources="META-INF/.*" \
    -H:ConfigurationFileDirectories=src/main/resources/META-INF/native-image \
    -H:Name=traffic-client \
    -jar target/traffic-client-3.1-SNAPSHOT-jar-with-dependencies.jar
```

## 配置文件说明

项目包含以下 GraalVM 配置文件，位于 `src/main/resources/META-INF/native-image/` 目录：

- **reflect-config.json**: 反射配置，包含需要反射访问的类和方法
- **resource-config.json**: 资源配置，包含需要打包到 Native Image 中的资源文件
- **jni-config.json**: JNI 配置，包含本地库调用相关配置
- **proxy-config.json**: 动态代理配置，包含需要代理的接口

## 编译参数说明

- `--no-fallback`: 禁用回退到 JVM 模式
- `--enable-http/https`: 启用 HTTP/HTTPS 支持
- `--enable-all-security-services`: 启用所有安全服务
- `--allow-incomplete-classpath`: 允许不完整的类路径
- `--initialize-at-build-time=org.slf4j`: 在构建时初始化 SLF4J
- `--initialize-at-run-time=io.netty,com.xiang.traffic.client.gui`: 在运行时初始化 Netty 和 GUI 组件
- `-H:+ReportExceptionStackTraces`: 报告异常堆栈跟踪
- `-H:+AddAllCharsets`: 包含所有字符集

## 运行 Native Image

编译完成后，可执行文件将生成在 `target/` 目录下：

```bash
# Windows
target\traffic-client.exe

# Linux/macOS
./target/traffic-client
```

## 故障排除

### 1. 编译失败
- 确保 GraalVM 版本正确（23.1.0+）
- 检查系统是否安装了必要的编译工具
- 查看编译日志中的错误信息

### 2. 运行时错误
- 检查反射配置是否完整
- 确保所有必要的资源文件都已包含
- 查看 JNI 配置是否正确

### 3. 性能问题
- Native Image 首次启动可能需要预热
- 可以使用 PGO (Profile-Guided Optimization) 优化性能

## 高级配置

### 启用 Profile-Guided Optimization (PGO)
```bash
# 第一步：生成 profile 数据
native-image --pgo-instrument [其他参数] -jar your-app.jar

# 运行应用生成 profile 数据
./your-app

# 第二步：使用 profile 数据重新编译
native-image --pgo [其他参数] -jar your-app.jar
```

### 自定义图标（Windows）
```cmd
# 使用 RCEDIT 工具修改可执行文件图标
RCEDIT64 /I traffic-client.exe icon.ico
```

## 注意事项

1. Native Image 编译时间较长，请耐心等待
2. 编译后的可执行文件体积较大，但启动速度更快
3. 某些 Java 特性在 Native Image 中可能不被支持
4. 建议在目标平台上进行编译以获得最佳兼容性
