package com.cn.xiang.fsockmanageweb.services;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.OrdersPo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/27 14:31
 */
public interface IOrdersService extends IService<OrdersPo> {
    public List<Map<String, String>> listOrders();

    OrdersPo getById1(String orderId);

    void updateById1(OrdersPo ordersPo);
}
