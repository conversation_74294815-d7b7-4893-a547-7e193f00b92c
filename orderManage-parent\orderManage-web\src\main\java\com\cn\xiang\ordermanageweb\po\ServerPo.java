package com.cn.xiang.ordermanageweb.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;

/**
 *flyings库中的server表
 */
@TableName("tb_server")
public class ServerPo extends BaseEntity {
    private String ip;

    private String port;
    private String certPort;

    private String shellPort;

    private String username;

    private String password;

    private String groupName;

    private String deployStatus;

    private int maxUserCount;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getCertPort() {
        return certPort;
    }

    public void setCertPort(String certPort) {
        this.certPort = certPort;
    }

    public String getShellPort() {
        return shellPort;
    }

    public void setShellPort(String shellPort) {
        this.shellPort = shellPort;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getDeployStatus() {
        return deployStatus;
    }

    public void setDeployStatus(String deployStatus) {
        this.deployStatus = deployStatus;
    }

    public int getMaxUserCount() {
        return maxUserCount;
    }

    public void setMaxUserCount(int maxUserCount) {
        this.maxUserCount = maxUserCount;
    }
}
