server:
  servlet:
    context-path: /dev
  port: 8080
spring:
  datasource:
    dynamic:
      strict: false
      primary: default
      datasource:
        default:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: hfxiang
          url: ******************************************************************************************************************************
        order:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: hfxiang
          url: **************************************************************************************************************************
quartz-task:
  auto-startup: true

