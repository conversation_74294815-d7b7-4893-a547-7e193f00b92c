@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Traffic Client - Complete Build and Test
echo ========================================

:: 检查Maven
where mvn >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Maven not found! Please install <PERSON><PERSON> and add it to PATH.
    pause
    exit /b 1
)

:: 检查GraalVM
where native-image >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: native-image not found! Please install GraalVM and native-image component.
    pause
    exit /b 1
)

echo Step 1: Building regular JAR...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: Maven build failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Testing regular JAR...
for %%f in (target\traffic-client-*-jar-with-dependencies.jar) do set JAR_FILE=%%f
if exist "%JAR_FILE%" (
    echo Testing JAR: %JAR_FILE%
    echo Starting application for 3 seconds...
    start /b java -jar "%JAR_FILE%"
    timeout /t 3 /nobreak > nul
    taskkill /f /im java.exe > nul 2>&1
    echo JAR test completed.
) else (
    echo WARNING: JAR file not found, skipping JAR test.
)

echo.
echo Step 3: Building Native Image...
call build-native-windows.bat
if %errorlevel% neq 0 (
    echo ERROR: Native image build failed!
    pause
    exit /b 1
)

echo.
echo Step 4: Testing Native Image...
call test-native.bat

echo.
echo ========================================
echo Build and Test Summary:
echo ========================================

if exist "%JAR_FILE%" (
    for %%A in ("%JAR_FILE%") do echo JAR file: %%~nxA (%%~zA bytes)
)

if exist target\traffic-client.exe (
    for %%A in (target\traffic-client.exe) do echo Native executable: %%~nxA (%%~zA bytes)
    
    echo.
    echo SUCCESS: Both JAR and Native Image builds completed successfully!
    echo.
    echo You can run:
    echo   - JAR version: java -jar "%JAR_FILE%"
    echo   - Native version: target\traffic-client.exe
) else (
    echo ERROR: Native executable not found!
    exit /b 1
)

echo.
echo ========================================
pause
