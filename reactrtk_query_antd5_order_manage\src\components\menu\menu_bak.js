import React, {Component} from 'react';
import {Menu} from 'antd';

import {
    HomeOutlined,
    DownCircleOutlined,
    PlayCircleOutlined
} from '@ant-design/icons';
import Mylink from '../../redux/store/components/link/container'

const {SubMenu} = Menu;
const ICON_MAP = {
    HomeOutlined,
    DownCircleOutlined,
    PlayCircleOutlined
};

/**
 * @deprecated 改为Menu的items属性渲染
 */
class Menu_ extends Component {
    constructor(props) {
        super(props);
        this.renderMenu = this.renderMenu.bind(this);
        this.onOpenChange = this.onOpenChange.bind(this);
        this.state = {
            openKeys: props.openKeys ? props.openKeys : []
        };
    }

    /**
     * 点击切换菜单回调
     * @param openKeys
     */
    onOpenChange = (openKeys) => {
        const latestOpenKey = openKeys.find(key => this.state.openKeys.indexOf(key) === -1);
        if (this.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
            this.setState({openKeys});
        } else {
            this.setState({
                openKeys: latestOpenKey ? [latestOpenKey] : [],
            });
        }
    };

    /**
     * {
            menus: [
                {
                    id: 1,
                    pid: '',
                    type: 'sub',
                    iconType: 'DownCircleOutlined',
                    name: 'menuSub1',
                },
                {
                    id: 2,
                    pid: '',
                    type: 'sub',
                    iconType: 'HomeOutlined',
                    name: 'menuSub2',
                },
                {
                    id: 3,
                    pid: '1',
                    type: 'item',
                    name: 'menuItem1',
                    href: '/option1'
                },
                {
                    id: 4,
                    pid: '2',
                    type: 'item',
                    name: 'menuItem2',
                    href: '/option2'
                }
            ],
            menuCfg: {
                defaultSelectedKeys: ['3'],
                defaultOpenKeys: ['1']
            }
        }
     * @param data
     * @returns {*}
     */
    renderMenu(config, data) {
        let rootMenus = [];

        if (data) {
            for (let i = 0, item; i < data.length; i++) {
                item = data[i];
                if (!item.pid) {
                    rootMenus.push(item);
                }
            }
        }

        let subMenus = [];
        for (let i = 0, rootMenu; i < rootMenus.length; i++) {
            rootMenu = rootMenus[i];
            let subMenu = this.renderSub(config, data, rootMenu);
            subMenus.push(subMenu);
        }

        //如果没有subMenu，直接渲染item
        if (subMenus.length === 0) {
            for (let i = 0, rootSubMenu; i < rootMenus.length; i++) {
                rootSubMenu = rootMenus[i];
                subMenus.push(<Menu.Item key={rootSubMenu.id}>{rootSubMenu.name}</Menu.Item>);
            }
        }

        return (
            <Menu
                onClick={this.props.onItemClick}
                theme={config.theme}
                mode={config.mode}
                defaultSelectedKeys={config.defaultSelectedKeys}
                defaultOpenKeys={config.defaultOpenKeys}
                onOpenChange={this.onOpenChange}
                openKeys={this.state.openKeys}
                style={config.style}
            >
                {subMenus}
            </Menu>
        )
    }

    renderSub(config, data, pItem) {
        //不是目录菜单，直接返回item
        if (pItem.type !== 'sub') {
            return <Menu.Item key={pItem.id}>{config.hasLink ? (
                <Mylink href={pItem.href}>{pItem.name}</Mylink>
            ) : pItem.name}</Menu.Item>;
        }

        let menuItems = [];
        for (let i = 0, item; i < data.length; i++) {
            item = data[i];

            if (item.pid === pItem.id) {
                if (item.type === 'item') {
                    menuItems.push(<Menu.Item key={item.id}>{config.hasLink ? (
                        <Mylink href={item.href}>{item.name}</Mylink>
                    ) : item.name}</Menu.Item>);
                } else if (item.type === 'sub') {
                    menuItems.push(this.renderSub(config, data, item));
                }
            }
        }

        let Icon = ICON_MAP[pItem.iconType];
        return <SubMenu key={pItem.id} icon={<Icon/>}
                        title={pItem.name}>{menuItems}</SubMenu>
    }

    render() {
        //计算出根菜单的id列表
        let rootKeys = [];
        if (this.props.data) {
            for (let i = 0; i < this.props.data.length; i++) {
                let item = this.props.data[i];
                if (!item.pid) {
                    rootKeys.push(item.id);
                }
            }
        }
        this.rootSubmenuKeys = rootKeys;

        return this.renderMenu(this.props.config, this.props.data);
    }
}

export default Menu_;