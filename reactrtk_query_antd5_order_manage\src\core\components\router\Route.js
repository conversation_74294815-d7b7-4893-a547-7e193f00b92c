import React, {Component} from 'react';
import AsyncComponent<PERSON><PERSON>Hoc from "../hocComponent/async/AsyncComponentRenderHoc";

/**
 * 路由组件，主要用于在页面中占位置，AsyncComponentRenderHoc()会生成一个action方法，调用该action方法传入path即可完成路由。
 */
class Route extends Component {

    render() {
        let AsyncComponent = this.props.asyncComponent;
        return (
            AsyncComponent ? <AsyncComponent/> : <></>
        );
    }
}

let result = AsyncComponentRenderHoc(Route);
export default result;
