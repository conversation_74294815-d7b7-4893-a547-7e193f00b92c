import React from 'react'
import EChartAdapter from "./adapter/EChartAdapter";

const OPTION = {
    title: {
        text: 'Referer of a Website',
        subtext: 'Fake Data',
        left: 'center'
    },
    tooltip: {
        trigger: 'item'
    },
    legend: {
        orient: 'vertical',
        left: 'left'
    },
    series: [
        {
            name: 'Access From',
            type: 'pie',
            radius: '50%',
            data: [
                {value: 1048, name: 'Search Engine'},
                {value: 735, name: 'Direct'},
                {value: 580, name: 'Email'},
                {value: 484, name: 'Union Ads'},
                {value: 300, name: 'Video Ads'}
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }
    ]
};

/**
 * echarts图表整合示例
 */
class PieChart extends React.Component {

    getOption = () => {
        let option = Object.assign({}, OPTION);
        option = Object.assign(option, this.props.option);

        // let data = this.props.data;
        // if (data) {
        //     option.series[0].data = data;
        // }
        return option;
    }

    render() {
        let {id, option, ...props_} = this.props;
        return <EChartAdapter {...props_}
                              option={this.getOption()}
                              id={this.props.id}/>
    }
}

export default PieChart;