package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.services.IPackageResourcesService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;



@Controller
@RequestMapping("/api_v1/packageResources")
public class PackageResourcesDownloadController extends ControllerSupport {
    @Autowired
    private IPackageResourcesService packageResourcesService;

    @RequestMapping(value = "download", method = RequestMethod.GET)
    public void download(@Validated @NotEmpty String packageResourceId) {
        packageResourcesService.downloadPackageResources(packageResourceId, getHttpServletResponse());
    }
}
