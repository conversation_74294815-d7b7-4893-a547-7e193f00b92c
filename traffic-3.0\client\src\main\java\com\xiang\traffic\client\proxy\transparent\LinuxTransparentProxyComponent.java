package com.xiang.traffic.client.proxy.transparent;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.ComponentException;
import com.xiang.traffic.client.proxy.ProxyComponent;
import com.xiang.traffic.client.proxy.ProxyRequest;
import com.xiang.traffic.client.proxy.misc.MessageDelivererCancelledException;
import com.xiang.traffic.misc.BaseUtils;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.epoll.*;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.ReferenceCountUtil;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.Objects;

/**
 * 实现Linux环境下的透明代理，需要使用epoll
 *
 * <AUTHOR>
 * @date 2024/6/6 15:14
 */
public class LinuxTransparentProxyComponent extends AbstractComponent<ProxyComponent> {

    private final EpollEventLoopGroup eventLoopGroup;

    private int bindPort;

    {
        if (!Epoll.isAvailable()) {
            throw new ComponentException("Epoll not support", Epoll.unavailabilityCause());
        }

        try {
            Class.forName(LinuxNative.class.getName());
        } catch (ClassNotFoundException | UnsatisfiedLinkError e) {
            throw new ComponentException("Could not load Transparent Native Library", e);
        }
    }

    public LinuxTransparentProxyComponent(ProxyComponent component) {
        super("LinuxTransparentProxyComponent", Objects.requireNonNull(component));
        int cpus = getConfigManager().availableProcessors();
        this.eventLoopGroup = new EpollEventLoopGroup(Math.max(cpus / 2, 1));
    }

    @Override
    protected void initInternal() {
        TransparentProxyConfig config = getConfigManager().getConfig(TransparentProxyConfig.NAME, TransparentProxyConfig.class);
        if (config == null) {
            throw new ComponentException("Config [TransparentProxyConfig] not register");
        }

        int port = config.getBindPort();
        if (!BaseUtils.isPort(port)) {
            throw new ComponentException("Port: " + port);
        }

        this.bindPort = port;

        super.initInternal();
    }

    @Override
    protected void startInternal() {
        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(eventLoopGroup)
                .channel(EpollServerSocketChannel.class)
                .option(EpollChannelOption.IP_TRANSPARENT, true)
                .childHandler(new ChannelInitializer<EpollSocketChannel>() {
                    @Override
                    protected void initChannel(EpollSocketChannel ch) {
                        InetSocketAddress address = LinuxNative.getTargetAddress(ch);
                        if (address == null) {
                            log.warn("Could not get target address, client address: {}", ch.remoteAddress());
                            ch.close();
                            return;
                        }

                        log.debug("Proxy target address: {}, from client: {}", address, ch.remoteAddress());
                        ChannelPipeline pipeline = ch.pipeline();

                        ProxyRequest request = new ProxyRequest(address.getHostName(), address.getPort(), ch, ProxyRequest.Protocol.TCP);
                        pipeline.addLast(new IdleStateHandler(0, 20, 0));
                        pipeline.addLast(new ProxyHandler(request));
                    }
                });

        int port = this.bindPort;
        ChannelFuture future = bootstrap.bind("0.0.0.0", port).awaitUninterruptibly();
        if (!future.isSuccess()) {
            log.error("Bind port {} failure: {}", port, future.cause().getMessage());
            throw new ComponentException("Bind port failure", future.cause());
        }

        super.startInternal();
    }

    @Override
    protected void stopInternal() {
        eventLoopGroup.shutdownGracefully();
        super.stopInternal();
    }


    private class ProxyHandler extends ChannelInboundHandlerAdapter {

        private final ProxyRequest proxyRequest;

        ProxyHandler(ProxyRequest request) {
            this.proxyRequest = Objects.requireNonNull(request);
        }

        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
            if (evt instanceof IdleStateEvent) {
                if (proxyRequest.isClose()) {
                    ctx.close();
                }
                return;
            }
            ctx.fireUserEventTriggered(evt);
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            if (cause instanceof MessageDelivererCancelledException) {
                ctx.close();
                return;
            } else if (cause instanceof IOException) {
                log.info("Remote client [{}] force to close: {}", ctx.channel().remoteAddress(), cause.getMessage());
                return;
            }

            log.error("An error occur in ProxyHandler", cause);
            ctx.close();
        }

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (msg instanceof ByteBuf) {
                try {
                    proxyRequest.transferClientMessage((ByteBuf) msg);
                } finally {
                    ReferenceCountUtil.release(msg);
                }
            }

            ctx.fireChannelRead(msg);
        }
    }
}
