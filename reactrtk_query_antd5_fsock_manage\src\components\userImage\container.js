import Utils from '../../core/utils/utils';
import {baseApi} from "../../redux/store/request/services/base/base"
import {connect} from "react-redux";
import UserImage from "./userImage";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('postSysUserLogout', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {};
    }
    return ownProps;
};


const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        logout: function () {
            let this_ = this;
            const promise = dispatch(baseApi.endpoints.postSysUserLogout.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                subscribe: false,
                forceRefetch: true
            }));
            promise.then((res) => {
                this_.hideModal();
                this_.redirectFormRef.current.submit();
            });
        },
    };
};

let Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(UserImage);
export default Container;
