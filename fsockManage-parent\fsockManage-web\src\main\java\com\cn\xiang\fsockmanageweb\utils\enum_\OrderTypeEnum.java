package com.cn.xiang.fsockmanageweb.utils.enum_;

/**
 * <AUTHOR>
 * @date 2024/6/13 14:48
 */
public enum OrderTypeEnum {
    ORDER_TYPE_MONTHLY("monthly", "一个月"),
    ORDER_TYPE_THREE_MONTH("threeMonth", "三个月");

    private String code;
    private String desc;

    OrderTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static OrderTypeEnum valueOf1(String code) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }
        return OrderTypeEnum.ORDER_TYPE_MONTHLY;
    }

}
