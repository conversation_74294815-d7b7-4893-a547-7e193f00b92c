package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.po.*;
import com.cn.xiang.fsockmanageweb.services.*;
import com.cn.xiang.fsockmanageweb.utils.JsonUtils;
import com.cn.xiang.fsockmanageweb.mapper.IServerMapper;
import com.cn.xiang.fsockmanageweb.utils.ShellUtils;
import com.cn.xiang.fsockmanageweb.utils.enum_.DeployStatusEnum;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class ServerServiceImpl extends ServiceImpl<IServerMapper, ServerPo> implements IServerService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServerServiceImpl.class);

    @Autowired
    private IPackageResourcesService packageResourcesService;

    @Autowired
    private IGroupService groupService;

    @Autowired
    private ITrafficRuleService trafficRuleService;

    @Autowired
    private ITrafficCapacityService trafficCapacityService;

    private ExecutorService threadPool = Executors.newFixedThreadPool(5);

    @Override
    public ArrayNode listWithUserCount(String search) {
        List<ServerPo> serverPoList = baseMapper.listBySearch(search);
        ArrayNode arrayNode = JsonUtils.createJsonArrayNode();
        for (ServerPo serverPo : serverPoList) {
            try {
                BeanInfo beanInfo = Introspector.getBeanInfo(ServerPo.class, Object.class);
                ObjectNode objectNode = JsonUtils.createJsonObjectNode();
                for (PropertyDescriptor pd : beanInfo.getPropertyDescriptors()) {
                    objectNode.set(pd.getName(), JsonUtils.beanToJsonNode(pd.getReadMethod().invoke(serverPo)));
                }
                long userCount = baseMapper.listUserCountByGroupName(serverPo.getGroupName());
                objectNode.set("userCount", JsonUtils.beanToJsonNode(userCount));

                arrayNode.add(objectNode);
            } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                throw new RuntimeException(e);
            }
        }
        return arrayNode;
    }

    @Override
    public void addServer(ServerPo serverPo) {
        serverPo.setDeployStatus(DeployStatusEnum.NEW.getCode());
        this.save(serverPo);

        //如果没有该组，创建组，采用默认流量规则
        LambdaQueryWrapper<GroupPo> queryChainWrapper = new LambdaQueryWrapper<>();
        queryChainWrapper.eq(GroupPo::getName, serverPo.getGroupName());
        GroupPo groupPo = groupService.getOne(queryChainWrapper);
        if (groupPo == null) {
            groupPo = new GroupPo();
            groupPo.setName(serverPo.getGroupName());

            LambdaQueryWrapper<TrafficRulePo> trafficRulePoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            trafficRulePoLambdaQueryWrapper.like(TrafficRulePo::getName, "no limit");
            TrafficRulePo trafficRulePo = trafficRuleService.getOne(trafficRulePoLambdaQueryWrapper);
            if (trafficRulePo != null) {
                groupPo.setTrafficRulePo(trafficRulePo);
            }

            //关联默认月流量
            TrafficCapacityPo trafficCapacityPo = trafficCapacityService.getById("1754478760872460223");
            groupPo.setTrafficCapacityPo(trafficCapacityPo);

            groupService.save(groupPo);
        }
    }

    @Override
    public void uploadZipToServer(String id, String packageResourceId) {
        LOGGER.info("start upload zip to server.");
        ServerPo serverPo = this.getById(id);
        ShellUtils shellUtils = ShellUtils.getInstance();
        try {
            shellUtils.init(serverPo.getIp(), Integer.parseInt(serverPo.getShellPort()),
                    serverPo.getUsername(), serverPo.getPassword());

            String distPath = "/root";
            PackageResourcesPo packageResourcesPo = packageResourcesService.getById(packageResourceId);
            String localFileStr = packageResourcesPo.getFilePath();

            //安装unzip
            shellUtils.execCmdErrContent("sudo apt-get update&&sudo apt-get install unzip -y");

            //上传资源包
            String uploadFileName = shellUtils.uploadFile(distPath, localFileStr);

            //解压资源包
            shellUtils.execCmdErrContent("cd " + distPath + "&& unzip " + uploadFileName);

            //关闭连接
            shellUtils.closeConnect();

            //更新server状态
            serverPo.setDeployStatus(DeployStatusEnum.UPLOADED_ZIP.getCode());
            this.updateById(serverPo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void modifyServerConfig(String id) {
        LOGGER.info("start modify config.");
        ServerPo serverPo = this.getById(id);
        ShellUtils shellUtils = ShellUtils.getInstance();
        try {
            String distPath = "/root";
            shellUtils.init(serverPo.getIp(), Integer.parseInt(serverPo.getShellPort()),
                    serverPo.getUsername(), serverPo.getPassword());
            //修改server.json配置文件中的group、port、certPort
            String serverJsonPath = distPath + "/traffic/config/server.json";
            shellUtils.execCmdErrContent("sed -i 's_group1_" + serverPo.getGroupName() + "_' " + serverJsonPath);
            shellUtils.execCmdErrContent("sed -i 's_1090_" + serverPo.getPort() + "_' " + serverJsonPath);
            shellUtils.execCmdErrContent("sed -i 's_7060_" + serverPo.getCertPort() + "_' " + serverJsonPath);

            //关闭连接
            shellUtils.closeConnect();

            //更新server状态
            serverPo.setDeployStatus(DeployStatusEnum.MODIFY_SERVER_CONFIG.getCode());
            this.updateById(serverPo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void installDockerToServer(String id) {
        LOGGER.info("start install docker to server.");
        ServerPo serverPo = this.getById(id);
        ShellUtils shellUtils = ShellUtils.getInstance();
        try {
            shellUtils.init(serverPo.getIp(), Integer.parseInt(serverPo.getShellPort()),
                    serverPo.getUsername(), serverPo.getPassword());
            String distPath = "/root";
            String dockerShellFileName = "install_docker_shell_apt-get.sh";
            String dockerPath = distPath + "/traffic/" + dockerShellFileName;
            //docker shell文件赋予执行权限，安装docker
            shellUtils.execCmdErrContent("sudo chmod u+x " + dockerPath);
            shellUtils.execCmdErrContent(dockerPath);

            //关闭连接
            shellUtils.closeConnect();

            //更新server状态
            serverPo.setDeployStatus(DeployStatusEnum.INSTALL_DOCKER.getCode());
            this.updateById(serverPo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void startServer(String id) {
        LOGGER.info("start server.");
        ServerPo serverPo = this.getById(id);
        ShellUtils shellUtils = ShellUtils.getInstance();
        try {
            String distPath = "/root";
            shellUtils.init(serverPo.getIp(), Integer.parseInt(serverPo.getShellPort()),
                    serverPo.getUsername(), serverPo.getPassword());
            //启动服务器
            String serverStartPath = distPath + "/traffic/";
            //docker compose build执行时，docker配置的代理没效果，所以先执行pull镜像
            shellUtils.execCmdErrContent("cd " + serverStartPath + " &&docker pull mysql:5.7.44 && docker pull openjdk:19-jdk-alpine3.16" +
                    " && docker compose build && docker compose up -d");

            //关闭连接
            shellUtils.closeConnect();

            //更新server状态
            serverPo.setDeployStatus(DeployStatusEnum.SERVER_STARTED.getCode());
            this.updateById(serverPo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void autoStart(String id, String packageResourceId) {
        threadPool.submit(new Runnable() {
            @Override
            public void run() {
                uploadZipToServer(id, packageResourceId);

                modifyServerConfig(id);

                installDockerToServer(id);

                startServer(id);
            }
        });
    }

    @Override
    public void stopServer(String id) {
        LOGGER.info("stop server.");
        ServerPo serverPo = this.getById(id);
        ShellUtils shellUtils = ShellUtils.getInstance();
        try {
            String distPath = "/root";
            shellUtils.init(serverPo.getIp(), Integer.parseInt(serverPo.getShellPort()),
                    serverPo.getUsername(), serverPo.getPassword());
            //停止服务器
            String serverStartPath = distPath + "/traffic/";
            shellUtils.execCmdErrContent("cd " + serverStartPath + " &&docker compose down");

            //关闭连接
            shellUtils.closeConnect();

            //更新server状态
            serverPo.setDeployStatus(DeployStatusEnum.SERVER_STOPED.getCode());
            this.updateById(serverPo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ServerPo findServerByUserId(String userId) {
        return baseMapper.findServerByUserId(userId);
    }

    @DS("default")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public ServerPo getById1(String serverId) {
        return this.getById(serverId);
    }
}
