import moduleContainerReducerObj from './reducer'

/***
 * 添加模型
 * @param Cmpt
 * @param moduleId
 * @returns {Function}
 */
const pushModule = (Cmpt, moduleId) => {
    return function (dispatch, getState) {
        dispatch(moduleContainerReducerObj.actions.pushModule({
            Cmpt,
            moduleId
        }));
    }
};
/**
 * 移除模型
 * @param moduleId
 * @returns {Function}
 */
const removeModule = (moduleId) => {
    return function (dispatch, getState) {
        dispatch(moduleContainerReducerObj.actions.removeModule({
            moduleId
        }));
    }
};
/**
 * 显示模型
 * @param moduleId
 * @returns {Function}
 */
const displayModule = (moduleId) => {
    return function (dispatch, getState) {
        dispatch(moduleContainerReducerObj.actions.displayModule({
            moduleId
        }));
    }
};
/**
 * 注册moduleId与组件引用的映射
 * @param moduleId
 * @param cmptRef
 * @param state
 * @returns {Function}
 */
const registerCmptRefMap = (moduleId, cmptRef, state) => {
    return function (dispatch, getState) {
        dispatch(moduleContainerReducerObj.actions.registerCmptRefMap({
            moduleId,
            cmptRef,
            state
        }));
    }
};
const unRegisterCmptRefMap = (moduleId) => {
    return function (dispatch, getState) {
        dispatch(moduleContainerReducerObj.actions.unregisterCmptRefMap({
            moduleId,
        }));
    }
};

let result = {
    modulesContainer: {
        pushModule,
        removeModule,
        displayModule,
        registerCmptRefMap,
        unRegisterCmptRefMap
    }
};

export default result;
