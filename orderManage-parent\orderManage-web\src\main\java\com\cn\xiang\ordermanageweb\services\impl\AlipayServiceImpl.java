package com.cn.xiang.ordermanageweb.services.impl;

import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.GoodsDetail;
import com.alipay.api.internal.util.AlipaySignature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cn.xiang.ordermanageweb.config.properties.AlipayConfigProperties;
import com.cn.xiang.ordermanageweb.mapper.IOrdersMapper;
import com.cn.xiang.ordermanageweb.pay.AlipayTradePagePayUtils;
import com.cn.xiang.ordermanageweb.po.OrderItemsPo;
import com.cn.xiang.ordermanageweb.po.OrdersPo;
import com.cn.xiang.ordermanageweb.po.PaymentsPo;
import com.cn.xiang.ordermanageweb.po.ProductsPo;
import com.cn.xiang.ordermanageweb.services.IAlipayService;
import com.cn.xiang.ordermanageweb.services.IOrderItemsService;
import com.cn.xiang.ordermanageweb.services.IPaymentsService;
import com.cn.xiang.ordermanageweb.services.IProductsService;
import com.cn.xiang.ordermanageweb.spring.ApplicationContextHolder;
import com.cn.xiang.ordermanageweb.utils.JsonUtils;
import com.cn.xiang.ordermanageweb.utils.enum_.OrderStatusEnum;
import com.cn.xiang.ordermanageweb.utils.enum_.PayStatusEnum;
import com.cn.xiang.ordermanageweb.vo.PayVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class AlipayServiceImpl implements IAlipayService {
    @Autowired
    private IOrdersMapper ordersMapper;
    @Autowired
    private IOrderItemsService orderItemsService;
    @Autowired
    private IProductsService productsService;
    @Autowired
    private IPaymentsService paymentsService;

    @Override
    public String generatePay(String orderId) throws AlipayApiException {
        OrdersPo ordersPo = ordersMapper.selectById(orderId);
        List<OrderItemsPo> orderItemsPoList = orderItemsService.list(new LambdaQueryWrapper<OrderItemsPo>().eq(OrderItemsPo::getOrderId, orderId));
        List<GoodsDetail> goodsDetailList = new ArrayList<>();

        String productName = "";
        for (OrderItemsPo orderItemsPo : orderItemsPoList) {
            GoodsDetail goodsDetail = new GoodsDetail();
            goodsDetail.setQuantity((long) orderItemsPo.getQuantity());
            goodsDetail.setPrice(orderItemsPo.getPrice() + "");
            goodsDetail.setGoodsId(orderItemsPo.getProductId());

            ProductsPo productsPo = productsService.getById(orderItemsPo.getProductId());
            goodsDetail.setGoodsName(productsPo.getName());
            if (productName.isEmpty()) {
                productName = productsPo.getName();
            }

            goodsDetail.setAlipayGoodsId(orderItemsPo.getProductId());
            goodsDetailList.add(goodsDetail);
        }

        PayVo payVo = new PayVo();
        payVo.setTotalAmount(ordersPo.getTotalAmount());
        payVo.setGoodsDetails(goodsDetailList);
        payVo.setSubject(productName);
        payVo.setOutTradeNo(ordersPo.getId());

        return AlipayTradePagePayUtils.pay(payVo);
    }

    @Override
    public ObjectNode tradeQuery(String outTradeNo, String tradeNo) throws AlipayApiException {
        //{"alipay_trade_query_response":{"code":"10000","msg":"Success","buyer_logon_id":"sdg***@sandbox.com","buyer_pay_amount":"0.00","buyer_user_id":"****************","buyer_user_type":"PRIVATE","invoice_amount":"0.00","out_trade_no":"1805937403109888001","point_amount":"0.00","receipt_amount":"0.00","send_pay_date":"2024-06-26 20:13:37","total_amount":"30.00","trade_no":"2024062622001430010503518307","trade_status":"TRADE_SUCCESS"}
        // ,"sign":"OqGBXHbLGppLhPBcxfo6ruWg1VpANpAxxH/1824B8L40oC4XwTaixptHzYoAjHgx1TEMiw2zP2lfKc4zKi8MRlmlVur1aRlTY8ES2rRXf8QdL9/hGpVAwt7iosM7T+CgE6VGBymDBTTGxlyo/9PgK1dXGrRtQpcyRLhNsKaQhf43UUTYCTSMU5ZTgSR8CM1Cl302WpFDh4OHWhsp+sfhmQh78Q5dNWodXRruAkbFM1qQb/w0nEflpSokqkNJcQutPnxXaJHLd2ii2dJwhAK5bhiOm/q9vCg9p8h9wUhmTAH8cOW/4VBzNIg+KLNy0U3+Vvg4B2rYEgD21iN/8mlHlQ=="}
        String result = AlipayTradePagePayUtils.tradeQuery(outTradeNo, tradeNo);
        try {
            return JsonUtils.getObjectMapper().readValue(result, ObjectNode.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean payCallback(HttpServletRequest request) {
        String outTradeNo = request.getParameter("out_trade_no");
        String tradeNo = request.getParameter("trade_no");
        boolean signVerified = this.rsaCheckV1(request);
        if (signVerified) {
            try {
                ObjectNode params = tradeQuery(outTradeNo, tradeNo);

                ObjectNode alipayTradeQueryResponse = (ObjectNode) params.get("alipay_trade_query_response");
                String code = alipayTradeQueryResponse.get("code").asText();
                return "10000".equals(code);
            } catch (AlipayApiException e) {
                throw new RuntimeException(e);
            }
        }
        return false;
    }

    @Override
    public boolean rsaCheckV1(HttpServletRequest request) {
        Enumeration<String> names = request.getParameterNames();
        Map<String, String> paramsMap = new HashMap<>();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            String value = request.getParameter(name);
            paramsMap.put(name, value);
        }

        AlipayConfigProperties alipayConfigProperties = ApplicationContextHolder.getBean(AlipayConfigProperties.class);
        //调用SDK验证签名
        try {
            return AlipaySignature.rsaCheckV1(paramsMap,
                    alipayConfigProperties.getAlipayPublicKey(), alipayConfigProperties.getCharset(), alipayConfigProperties.getSignType());
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public void payNotify(HttpServletRequest request) {
        boolean signVerified = this.rsaCheckV1(request);
         /*
        gmt_create:2024-06-27 13:06:16
        charset:UTF-8
        gmt_payment:2024-06-27 13:06:22
        notify_time:2024-06-27 13:06:19
        subject:JAPAN.TKY.BGP.Basic
        sign:OVG2mZ2EpSC4bNihzOYU8b+0/dwJtrYONzmQPWqFvoquGlo8b9ZXnbXisiA3FQPBtLBP/oI7zay2Qm5eB0KrXl0bY7itviUUt/sDfgxP7zJ9XV/OH1YT9BHC0FVcontw/zHbZxO5ln+gLr9xoFG2YiakOwc6n2amecjhAzF1deo87uM7VBvx6i/H5gE+yq9exe620qjBJ8WgIqP/kCIXDrri38Gs1k3nVT69mSC98to7n17MCY7BMTx5IxR0CEu0wbbQyeDOvmtCUuDYqObgAZtOpxCPCUzY56xLcHYYhsWwU8fQ/lHk4XBHTrjeYgUHgkcdHt6okBFbLgfdAbgLnA==
        buyer_id:****************
        invoice_amount:30.00
        version:1.0
        notify_id:2024062701222130622030010503632456
        fund_bill_list:[{"amount":"30.00","fundChannel":"ALIPAYACCOUNT"}]
        notify_type:trade_status_sync
        out_trade_no:1806192282596331522
        total_amount:30.00
        trade_status:TRADE_SUCCESS
        trade_no:2024062722001430010503528173
        auth_app_id:****************
        receipt_amount:30.00
        point_amount:0.00
        buyer_pay_amount:30.00
        app_id:****************
        sign_type:RSA2
        seller_id:****************
        * */
        if (signVerified) {
            String outTradeNo = request.getParameter("out_trade_no");
            String tradeNo = request.getParameter("trade_no");
            String tradeStatus = request.getParameter("trade_status");
            String gmtPayment = request.getParameter("gmt_payment");

            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                OrdersPo ordersPo = ordersMapper.selectById(outTradeNo);
                ordersPo.setStatus(OrderStatusEnum.ORDER_STATUS_PAID.getCode());
                ordersMapper.updateById(ordersPo);

                PaymentsPo paymentsPo = paymentsService.getOne(new LambdaQueryWrapper<PaymentsPo>().eq(PaymentsPo::getOrderId, outTradeNo));
                paymentsPo.setPaymentDate(gmtPayment);
                paymentsPo.setTradeNo(tradeNo);
                paymentsPo.setStatus(PayStatusEnum.PAY_STATUS_PAID.getCode());
                paymentsService.updateById(paymentsPo);
            }
        }
    }
}
