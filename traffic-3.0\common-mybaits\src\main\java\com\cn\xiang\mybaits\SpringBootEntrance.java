package com.cn.xiang.mybaits;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * <AUTHOR>
 * @date 2024/2/5 19:53
 */
@SpringBootApplication
@MapperScan("com.cn.xiang.mybaits.mapper")
public class SpringBootEntrance {

    public static void run(String[] args) {
        SpringApplicationBuilder builder = new SpringApplicationBuilder();
        builder.bannerMode(Banner.Mode.OFF);
        builder.sources(SpringBootEntrance.class).run(args);
    }
}
