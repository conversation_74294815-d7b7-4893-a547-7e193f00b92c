import {createSlice} from "@reduxjs/toolkit";

let initialState = {
    modules: [],
    cmptRefMap: {}
};

const moduleContainerSlice = createSlice({
    name: 'moduleContainerSlice',
    initialState,
    reducers: {
        /**
         * 打开模型
         * @param state
         * @param action
         */
        pushModule: (state, action) => {
            //修改所有组件不可见
            let modules = [].concat(state.modules);
            for (let i = 0; i < modules.length; i++) {
                modules[i].isVisable = false;
            }
            //添加新的组件
            modules.push({
                isVisable: 'true',
                component: action.payload.Cmpt,
                moduleId: action.payload.moduleId
            });
            state.modules = modules;
        },
        /**
         * 移除模型
         * @param state
         * @param action
         */
        removeModule: (state, action) => {
            let modules = [].concat(state.modules);
            for (let i = 0; i < modules.length; i++) {
                if (action.payload.moduleId === modules[i].moduleId) {
                    modules.splice(i, 1);

                    //如果移除的模型是正在展示的，则显示最后打开的模型
                    if (modules.length > 0 && state.modules[i].isVisable) {
                        //移除模型后，显示最后打开的模型
                        let lastestModule = modules[modules.length - 1];
                        if (lastestModule) {
                            lastestModule.isVisable = true;
                        }
                    }
                    break;
                }
            }
            state.modules = modules;
        },
        /**
         * 显示模型
         * @param state
         * @param action
         */
        displayModule: (state, action) => {
            let modules = [].concat(state.modules);

            let found = false;
            for (let i = 0; i < modules.length; i++) {
                if (modules[i].moduleId === action.payload.moduleId) {
                    found = true;
                }
            }

            if (found) {
                for (let i = 0; i < modules.length; i++) {
                    if (modules[i].moduleId === action.payload.moduleId) {
                        modules[i].isVisable = true;
                    } else {
                        modules[i].isVisable = false;
                    }
                }
                state.modules = modules;
            }
        },
        registerCmptRefMap: (state, action) => {
            let cmptRefMap = state.cmptRefMap || {};
            if (!(action.payload.moduleId in cmptRefMap)) {
                cmptRefMap = Object.assign(
                    {},
                    state.cmptRefMap,
                    {
                        [action.payload.moduleId]: {
                            component: action.payload.cmptRef,
                            state: action.payload.state
                        }
                    }
                );
            }
            //已经有该模型数据，更新component与state
            else if (action.payload.state !== cmptRefMap[action.payload.moduleId].state) {
                cmptRefMap[action.payload.moduleId].component = action.payload.cmptRef;
                cmptRefMap[action.payload.moduleId].state = action.payload.state;
            }
            state.cmptRefMap = cmptRefMap;
        },
        unregisterCmptRefMap: (state, action) => {
            let cmptRefMap = Object.assign({}, state.cmptRefMap);
            delete cmptRefMap[action.payload.moduleId];
            state.cmptRefMap = cmptRefMap;
        },

    },
});

let result = {
    reducer: {
        moduleContainer: moduleContainerSlice.reducer
    },
    actions: moduleContainerSlice.actions
}

export default result;