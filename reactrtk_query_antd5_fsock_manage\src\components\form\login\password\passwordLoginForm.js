import React, {Component} from "react";
import {Form, Input, Button, Checkbox} from 'antd';
import {
    LockOutlined,
    UserOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
    TaobaoCircleOutlined,
    <PERSON>payCircleOutlined,
    WechatOutlined
} from '@ant-design/icons';
import './passwordLoginForm.less';
import ThirdPartLogin from "../thirdPartLogin/thirdPartLogin";

const LIST_ITEMS = [
    {
        Cmpt: TaobaoCircleOutlined,
        key: 'Taobao',
        props: {
            onClick: (e) => {
                console.log('click Taobao');
            }
        }
    },
    {
        Cmpt: AlipayCircleOutlined,
        key: 'Alipay',
        props: {
            onClick: (e) => {
                console.log('click Alipay');
            }
        }
    },
    {
        Cmpt: WechatOutlined,
        key: 'Wechat',
        props: {
            onClick: (e) => {
                console.log('click Wechat');
            }
        }
    },
];

class PasswordLoginForm extends Component {
    constructor(props) {
        super(props);

        this.passwordInputRef = React.createRef();
        this.formRef = React.createRef();
        this.state = {
            isPasswordVisible: false
        }
    }

    /**
     *点击小眼睛图标，显示隐藏密码
     * @param e
     */
    togglePasswordSuffixIcon = (e) => {
        this.setState((preState) => {
            if (preState.isPasswordVisible) {
                this.passwordInputRef.current.input.type = 'password'
                return {
                    isPasswordVisible: false
                };
            } else {
                this.passwordInputRef.current.input.type = 'text'
                return {
                    isPasswordVisible: true
                }
            }
        })
    }

    onLoginFormFailed = (error) => {
        console.log(error)
    }

    render() {
        return (
            <Form
                ref={this.formRef}
                name="login-form-password"
                className="login-form-password"
                initialValues={{remember: true}}
                onFinish={this.props.onLoginFormSuccess.bind(this)}
                onFinishFailed={this.onLoginFormFailed}
                autoComplete="off"
                scrollToFirstError={true}
            >
                <Form.Item
                    name="username"
                    initialValue={'admin'}
                    rules={[{required: true, message: 'Please input your username!'}]}
                >
                    <Input prefix={<UserOutlined className="site-form-item-icon"/>} placeholder="username"/>
                </Form.Item>
                <Form.Item
                    name="password"
                    initialValue={'admin'}
                    rules={[{required: true, message: 'Please input your password!'}]}
                >
                    <Input ref={this.passwordInputRef} prefix={<LockOutlined className="site-form-item-icon"/>}
                           type="password"
                           placeholder="password" suffix={(() => {
                        return this.state.isPasswordVisible ? <EyeOutlined onClick={this.togglePasswordSuffixIcon}/> :
                            <EyeInvisibleOutlined onClick={this.togglePasswordSuffixIcon}/>
                    })()}/>
                </Form.Item>
                <Form.Item>
                    <Form.Item name="remember" valuePropName="checked" noStyle>
                        <Checkbox>自动登录</Checkbox>
                    </Form.Item>

                    <a className="login-form-forgot" href="https://www.baidu.com">
                        忘记密码？
                    </a>
                </Form.Item>

                <Form.Item>
                    <Button type="primary" htmlType="submit" className="login-form-button">
                        登录
                    </Button>
                    {/*或者 <a href="">注册</a>*/}
                </Form.Item>
                <ThirdPartLogin labelText={'其他登录方式：'} listItems={LIST_ITEMS}/>
            </Form>
        );
    }
}

export default PasswordLoginForm;
