<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cn.xiang</groupId>
        <artifactId>fsockManage-common</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>fsockManage-mybaits</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>fsockManage-mybaits</name>
    <description>fsockManage-mybaits</description>
    <packaging>jar</packaging>

    <properties>
        <mybatis-plus-spring-boot3-starter.version>********</mybatis-plus-spring-boot3-starter.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus-spring-boot3-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
            <version>${mybatis-plus-spring-boot3-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
