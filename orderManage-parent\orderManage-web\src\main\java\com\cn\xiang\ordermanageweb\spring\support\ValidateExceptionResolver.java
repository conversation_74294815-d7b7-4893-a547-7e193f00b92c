package com.cn.xiang.ordermanageweb.spring.support;

import com.cn.xiang.ordermanageweb.utils.JSONResultMessage;
import com.cn.xiang.ordermanageweb.utils.JsonUtils;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.core.type.classreading.SimpleMetadataReaderFactory;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;

/**
 * 拦截ConstraintViolationException、BindException参数校验异常，并把参数校验异常信息转为json格式返回
 *
 * <AUTHOR>
 * @date 2018年6月9日 下午8:28:05
 * @version 1.0
 *
 */
@Order(value = -1)
public class ValidateExceptionResolver implements HandlerExceptionResolver {
    private static final Logger LOGGER = LoggerFactory
            .getLogger(ValidateExceptionResolver.class);
    private static final String PARAM_NAME = "paramName";
    private static final String DESC = "desc";
    private static final String VALIDATE_ERROR_CODE = "000001";

    @Override
    public ModelAndView resolveException(HttpServletRequest request,
                                         HttpServletResponse response, Object handler, Exception ex) {
        try {
            // 方法参数校验异常
            if (ex instanceof ConstraintViolationException) {
                ConstraintViolationException exception = (ConstraintViolationException) ex;

                ArrayNode errorMsgs = JsonUtils.createJsonArrayNode();
                for (ConstraintViolation<?> cv : exception.getConstraintViolations()) {
                    Object rootBean = cv.getRootBean();

                    try {
                        // 从抛出异常的类中获取Validated注解
                        Validated validatedAnno = rootBean.getClass()
                                .getAnnotation(Validated.class);

                        MetadataReaderFactory metadataReaderFactory = new SimpleMetadataReaderFactory();
                        MetadataReader metadataReader = metadataReaderFactory
                                .getMetadataReader(rootBean.getClass().getName());
                        TypeFilter tf = new AnnotationTypeFilter(Controller.class);

                        // 如果是标注了@Validated，同时也标注了Controller的校验方法参数时抛出的异常
                        if (validatedAnno != null
                                && tf.match(metadataReader, metadataReaderFactory)) {

                            String path = cv.getPropertyPath().toString();
                            String paramName = path.substring(path.lastIndexOf('.') + 1);

                            ObjectNode item = JsonUtils.createJsonObjectNode();
                            item.put(PARAM_NAME, paramName);
                            item.put(DESC, cv.getMessage());
                            errorMsgs.add(item);
                        }
                    } catch (IOException e) {
                        LOGGER.error(e.getMessage(), e);
                    }
                }
                if (!errorMsgs.isEmpty()) {
                    sendMessage(response, errorMsgs);
                    return new ModelAndView();
                }
                // pojo校验时抛出的绑定异常
            } else if (ex instanceof BindException) {
                BindException exception = (BindException) ex;

                ArrayNode errorMsgs = JsonUtils.createJsonArrayNode();
                if (exception.hasErrors()) {
                    for (FieldError fe : exception.getFieldErrors()) {
                        ObjectNode item = JsonUtils.createJsonObjectNode();
                        item.put(PARAM_NAME, fe.getField());
                        item.put(DESC, fe.getDefaultMessage());
                        errorMsgs.add(item);
                    }

                    sendMessage(response, errorMsgs);
                    return new ModelAndView();
                }
            }
        } catch (Exception e) {
            LOGGER.error("an exception occurs when dealing with exception", e);
        }
        /// 不处理异常
        return null;
    }

    /**
     * 向浏览器会写错误信息
     *
     * @param response
     * @param errorMsgs
     * @throws IOException
     */
    private void sendMessage(HttpServletResponse response, ArrayNode errorMsgs)
            throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        JSONResultMessage resultMessage = new JSONResultMessage();
        resultMessage.error(VALIDATE_ERROR_CODE, errorMsgs);
        response.getWriter().write(resultMessage.toString());
    }
}
