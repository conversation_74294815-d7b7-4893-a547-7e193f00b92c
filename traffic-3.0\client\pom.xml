<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>traffic-parent</artifactId>
        <groupId>com.xiang</groupId>
        <version>3.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>traffic-client</artifactId>

    <description>
        traffic客户端
    </description>

    <properties>
        <graalvm.version>23.1.0</graalvm.version>
        <native-maven-plugin.version>0.9.28</native-maven-plugin.version>
        <main.class>com.xiang.traffic.client.ClientBoot</main.class>
        <jdk.version>1.8</jdk.version>
        <gson.version>2.10.1</gson.version>
        <native.image.name>traffic-client</native.image.name>
    </properties>

    <profiles>
        <profile>
            <id>win32</id>
            <properties>
                <swt-artifact>org.eclipse.swt.win32.win32.x86</swt-artifact>
            </properties>
        </profile>
        <profile>
            <id>win64</id>
            <properties>
                <swt-artifact>org.eclipse.swt.win32.win32.x86_64</swt-artifact>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>macos</id>
            <properties>
                <swt-artifact>org.eclipse.swt.cocoa.macosx.x86_64</swt-artifact>
            </properties>
        </profile>
        <profile>
            <id>linux32</id>
            <properties>
                <swt-artifact>org.eclipse.swt.gtk.linux.x86</swt-artifact>
            </properties>
        </profile>
        <profile>
            <id>linux64</id>
            <properties>
                <swt-artifact>org.eclipse.swt.gtk.linux.x86_64</swt-artifact>
            </properties>
        </profile>
        <profile>
            <id>native</id>
            <properties>
                <native.image.skip>false</native.image.skip>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <version>${native-maven-plugin.version}</version>
                        <extensions>true</extensions>
                        <executions>
                            <execution>
                                <id>build-native</id>
                                <goals>
                                    <goal>compile-no-fork</goal>
                                </goals>
                                <phase>package</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <skip>${native.image.skip}</skip>
                            <imageName>${native.image.name}</imageName>
                            <mainClass>${main.class}</mainClass>
                            <buildArgs>
                                <buildArg>--no-fallback</buildArg>
                                <buildArg>--enable-http</buildArg>
                                <buildArg>--enable-https</buildArg>
                                <buildArg>--enable-all-security-services</buildArg>
                                <buildArg>--allow-incomplete-classpath</buildArg>
                                <buildArg>--report-unsupported-elements-at-runtime</buildArg>
                                <buildArg>--initialize-at-build-time=org.slf4j</buildArg>
                                <buildArg>--initialize-at-run-time=io.netty,com.xiang.traffic.client.gui</buildArg>
                                <buildArg>-H:+ReportExceptionStackTraces</buildArg>
                                <buildArg>-H:+AddAllCharsets</buildArg>
                                <buildArg>-H:IncludeResources=.*\.properties$</buildArg>
                                <buildArg>-H:IncludeResources=.*\.yml$</buildArg>
                                <buildArg>-H:IncludeResources=.*\.yaml$</buildArg>
                                <buildArg>-H:IncludeResources=META-INF/.*</buildArg>
                                <buildArg>-H:ConfigurationFileDirectories=src/main/resources/META-INF/native-image</buildArg>
                            </buildArgs>
                            <verbose>true</verbose>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>com.xiang</groupId>
            <artifactId>traffic-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eclipse.swt</groupId>
            <artifactId>${swt-artifact}</artifactId>
            <version>4.3</version>
        </dependency>

        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>1.0.19</version>
        </dependency>

        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart-swt</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jfree</groupId>
                    <artifactId>jfreechart</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreesvg</artifactId>
            <version>3.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.9</version>
        </dependency>

        <dependency>
            <groupId>org.jmock</groupId>
            <artifactId>jmock-junit4</artifactId>
            <version>2.12.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.graalvm.sdk</groupId>
            <artifactId>graal-sdk</artifactId>
            <version>${graalvm.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- GraalVM Native Image 支持 -->
        <dependency>
            <groupId>org.graalvm.nativeimage</groupId>
            <artifactId>svm</artifactId>
            <version>${graalvm.version}</version>
            <scope>provided</scope>
        </dependency>

        <!--<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <!--这里要替换成jar包main方法所在类 -->
                            <mainClass>${main.class}</mainClass>
                        </manifest>
                    </archive>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id> <!-- this is used for inheritance merges -->
                        <phase>package</phase> <!-- 指定在打包节点执行jar包合并操作 -->
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>


</project>
