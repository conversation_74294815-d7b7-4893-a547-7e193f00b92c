package com.cn.xiang.ordermanageweb.po.typeHandler;

import com.cn.xiang.ordermanageweb.po.UserPo;
import com.cn.xiang.ordermanageweb.services.IUserService;
import com.cn.xiang.ordermanageweb.spring.ApplicationContextHolder;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/2/5 16:48
 */
public class UserPoTypeHandler implements TypeHandler<UserPo> {
    @Override
    public void setParameter(PreparedStatement ps, int i, UserPo parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getId());
    }

    @Override
    public UserPo getResult(ResultSet rs, String columnName) throws SQLException {
        String userId = rs.getString(columnName);
        IUserService userService = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IUserService.class);
        return userService.getById1(userId);
    }

    @Override
    public UserPo getResult(ResultSet rs, int columnIndex) throws SQLException {
        String userId = rs.getString(columnIndex);
        IUserService userService = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IUserService.class);
        return userService.getById1(userId);
    }

    @Override
    public UserPo getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return null;
    }
}
