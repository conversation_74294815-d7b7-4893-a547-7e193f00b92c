package com.cn.xiang.fsockmanageweb.config;

import com.cn.xiang.fsockmanageweb.spring.code.handlerMapping.LogMappingInfoRequestMappingHandlerMapping;
import com.cn.xiang.fsockmanageweb.spring.support.ValidateExceptionResolver;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcRegistrations;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

@Configuration
public class MvcConfig implements WebMvcConfigurer {

    /**
     * 用于注册LogMappingInfoRequestMappingHandlerMapping
     *
     * @return
     */
    @Bean
    public WebMvcRegistrations createWebMvcRegistrations() {
        return new WebMvcRegistrations() {
            @Override
            public RequestMappingHandlerMapping getRequestMappingHandlerMapping() {
                return new LogMappingInfoRequestMappingHandlerMapping();
            }

            @Override
            public RequestMappingHandlerAdapter getRequestMappingHandlerAdapter() {
                return WebMvcRegistrations.super.getRequestMappingHandlerAdapter();
            }

            @Override
            public ExceptionHandlerExceptionResolver getExceptionHandlerExceptionResolver() {
                return WebMvcRegistrations.super.getExceptionHandlerExceptionResolver();
            }
        };
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 相当于在controller中不写任何逻辑，直接跳转视图
        registry.addViewController("/api_v1/testData/leftMenuData").setViewName("/testData/leftMenuData.json");
        registry.addViewController("/api_v1/testData/topMenuData").setViewName("/testData/topMenuData.json");

        // 登陆登出页
//        registry.addViewController("/loginPage").setViewName("login.html");
//        registry.addViewController("/logoutPage").setViewName("logout.html");

        registry.addViewController("/api_v1/testData/table1Data").setViewName("/testData/table1Data.json");
        registry.addViewController("/api_v1/testData/lineChartData")
                .setViewName("/testData/lineChartData.json");
        registry.addViewController("/api_v1/testData/lineChartPushData")
                .setViewName("/testData/lineChartPushData.json");
    }

    @Bean(name = {"validateExceptionResolver"})
    public ValidateExceptionResolver validateExceptionResolver() {
        return new ValidateExceptionResolver();
    }

//    @Bean
//    public ErrorViewResolver errorViewResolver() {
//        return new ErrorView404Resolver();
//    }
}
