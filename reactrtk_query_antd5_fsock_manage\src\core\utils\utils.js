/**
 * 工具方法
 * @type {{setInterval}}
 */
import Constants from "./constants";

const Utils = (() => {
    /**
     *
     * @param callback 回调函数
     * @param milliseconds  间隔时间(s)
     * @param timer = {ref : ''} ref用于存储setTimeOut()返回的对象，
     * @param immediately 是否马上调用一次
     */
    const setInterval = (callback, milliseconds, timer, immediately = false) => {
        const interval = () => {
            //callback()返回true才继续
            let isContinue = callback();
            if (isContinue) {
                timer ? (timer.ref = setTimeout(interval, milliseconds)) : setTimeout(interval, milliseconds);
            }
        };
        timer ? (timer.ref = setTimeout(interval, milliseconds)) : setTimeout(interval, milliseconds);

        if (immediately) {
            callback();
        }
    };

    /**
     *是否数据已经加载完
     * @param state
     * @param subreddit
     * @returns {boolean}
     */
    const isFetched = (state, subreddit) => {
        return state.fetchBySubreddit[subreddit] && !state.fetchBySubreddit[subreddit].isFetching;
    }

    /**
     * 对象深拷贝
     * @param obj
     * @returns {*[]}
     */
    const deepClone = (obj = undefined) => {
        if (typeof obj !== "object") {
            return obj;
        }
        let targetObj = obj.constructor === Array ? [] : {};
        Reflect.ownKeys(obj).forEach(key => {
            if (obj[key] && typeof obj[key] === "object") {
                targetObj[key] = deepClone(obj[key]);
            } else {
                targetObj[key] = obj[key];
            }
        });
        return targetObj;
    }

    /**
     * 获取promise状态
     * core/axios/axiosWrapper.js中有使用
     * 例子：
     *  Utils.getStatePromise(promise).then((state) => {
            if (state === Constants.PROMISE_STATE.PENDING) {
                //开启遮罩
                global.store.dispatch(actions.loadingMask.showMask(true));
            }
        },(state)=>{
            //console.log(state);
        })
     * @param promise
     * @returns {Promise<string>}
     */
    const getStatePromise = (promise) => {
        const target = {}
        return Promise.race([promise, target]).then(value => (value === target) ? Constants.PROMISE_STATE.PENDING : Constants.PROMISE_STATE.FULFILLED, () => Constants.PROMISE_STATE.REJECTED,)
    }

    /**
     * 根据端点名称选择store中的queries最新的数据
     * @param endpointName
     * @param state
     * @returns {{data}}
     */
    const selectLatestDataByEndpoint = (endpointName, api, state) => {
        if (!isEmptyObject(state[api.reducerPath].queries)) {
            let cacheKeyArr = [];
            for (let cacheKey in state[api.reducerPath].queries) {
                let item = state[api.reducerPath].queries[cacheKey];
                if (item['endpointName'] === endpointName && item.status === "fulfilled") {
                    cacheKeyArr.push({
                        cacheKey: cacheKey, fulfilledTimeStamp: item['fulfilledTimeStamp']
                    })
                }
            }

            if (cacheKeyArr.length > 0) {
                //找到最新的请求数据
                let item = cacheKeyArr.reduce((preValue, currentValue, currentIndex, array) => {
                    if (currentValue['fulfilledTimeStamp'] > preValue['fulfilledTimeStamp']) return currentValue;
                    return preValue;
                })

                let cacheKey = item.cacheKey;
                //cacheKey eg: getLeftMenuData({"pId":"nav2"})
                if (cacheKey) {
                    //serializeParam eg: {"pId":"nav2"}
                    let serializeParam = cacheKey.substring(endpointName.length, cacheKey.length).replace('(', '').replace(')', '');

                    if (serializeParam !== 'undefined') {
                        let param = JSON.parse(serializeParam);
                        return api.endpoints[endpointName].select(param)(state);
                    } else {
                        return api.endpoints[endpointName].select()(state);
                    }
                }
            }
        }
        return {
            isSuccess: false
        }
    }

    /**
     * 根据参数选择数据
     * @param endpointName
     * @param param
     * @param state
     * @returns {*}
     * @deprecated 由selectLatestDataByEndpoint方法替代
     */
    const selectDataByParam = (endpointName, api, state, param) => {
        return param ? api.endpoints[endpointName].select(param)(state) : api.endpoints[endpointName].select()(state);
    }

    /**
     * 发送类似Ajax请求，不会在store中存储结果，
     * 并且设置了forceRefetch属性，在store有数据的情况下都重发请求。
     * 用于后台数据更新或者不希望数据在store中存储
     * @param apiEndpoint
     * @param params
     * @param dispatch
     * @returns {Promise<void>}
     */
    const sendJqueryAjaxStyleRequest = async (apiEndpoint, params, dispatch) => {
        let promise;
        if (params) {
            promise = dispatch(apiEndpoint.initiate(params, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
        } else {
            promise = dispatch(apiEndpoint.initiate(undefined, {
                forceRefetch: true
            }));
        }
        // const {data, isLoading, isSuccess} = await promise;
        const response = await promise;
        //清除store中缓存
        promise.unsubscribe();

        return (!response.isLoading && response.isSuccess) ? response.data : undefined;
    }

    /**
     * 判断是否为空对象
     * @param obj
     * @returns {boolean}
     */
    const isEmptyObject = (obj) => {
        return obj && Object.keys(obj).length === 0;
    }

    const BaseUrl = (() => {
        let baseUrl;
        return {
            getBaseUrl: () => {
                return baseUrl;
            },
            setBaseUrl: (url) => {
                baseUrl = url;
            }
        }
    })();

    return {
        setInterval,
        isFetched,
        deepClone,
        getStatePromise,
        selectLatestDataByEndpoint,
        selectDataByParam,
        sendJqueryAjaxStyleRequest,
        isEmptyObject,
        BaseUrl
    }
})();

export default Utils;
