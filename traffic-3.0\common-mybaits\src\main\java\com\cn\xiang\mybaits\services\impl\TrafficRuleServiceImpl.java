package com.cn.xiang.mybaits.services.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.mybaits.mapper.ITrafficRuleMapper;
import com.cn.xiang.mybaits.po.TrafficRulePo;
import com.cn.xiang.mybaits.services.ITrafficRuleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class TrafficRuleServiceImpl extends ServiceImpl<ITrafficRuleMapper, TrafficRulePo> implements ITrafficRuleService {
    public TrafficRulePo findTrafficRuleByGroupName(String groupName) {
        return baseMapper.findTrafficRuleByGroupName(groupName);
    }
}
