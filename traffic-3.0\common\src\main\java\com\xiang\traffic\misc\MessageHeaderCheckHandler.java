package com.xiang.traffic.misc;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * 用于检测消息头是否合法，辨别出非flyingsocks端并强制关闭连接
 *
 * <AUTHOR>
 * @date 2024/6/6 15:17
 */
@ChannelHandler.Sharable
public final class MessageHeaderCheckHandler extends ChannelInboundHandlerAdapter {

    private static final Logger log = LoggerFactory.getLogger(MessageHeaderCheckHandler.class);

    private final byte[] header;

    private final int headerLength;

    public MessageHeaderCheckHandler(byte[] header) {
        this.header = Objects.requireNonNull(header);
        this.headerLength = header.length;
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof ByteBuf) {
            ByteBuf buf = (ByteBuf) msg;

            if (buf.readableBytes() < headerLength) {
                ReferenceCountUtil.release(msg);
                ctx.close();
                return;
            }

            int index = buf.readerIndex();
            boolean pass = true;
            for (int i = 0; i < headerLength; i++) {
                if (buf.getByte(index + i) != header[i]) {
                    pass = false;
                }
            }

            if (!pass) {
                log.warn("Message header check failure");
                ReferenceCountUtil.release(msg);
                ctx.close();
                return;
            }
        }

        ctx.fireChannelRead(msg);
    }


    @Override
    public boolean isSharable() {
        return true;
    }
}
