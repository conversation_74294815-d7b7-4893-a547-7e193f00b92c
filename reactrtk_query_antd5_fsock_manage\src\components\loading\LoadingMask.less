@keyframes lds-spinner {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@-webkit-keyframes lds-spinner {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 1;
  }
  to {
    opacity: 0
  }
}

.loading-mask-ctn {
  .loading-mask-ant-modal-mask {
    position: fixed;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    inset-inline-start: 0;
    z-index: 1000;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
  }

  .loading-mask-ant-modal-wrap {
    position: fixed;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    inset-inline-start: 0;
    overflow: auto;
    outline: 0;
    -webkit-overflow-scrolling: touch;
    z-index: 1000;
    inset: 0;
  }

  .loading-ctn {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    justify-content: center;

    .lds-spinner {
      position: relative;
      width: 200px;
      height: 200px;
      -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
      transform: translate(-100px, -100px) scale(1) translate(100px, 100px);

      > div {
        left: 94px;
        top: 48px;
        position: absolute;
        -webkit-animation: lds-spinner linear 1s infinite;
        animation: lds-spinner linear 1s infinite;
        /*background: #28c246;*/
        background: #1890ff;
        width: 12px;
        height: 24px;
        border-radius: 40%;
        -webkit-transform-origin: 6px 52px;
        transform-origin: 6px 52px;
      }

      > div:nth-child(1) {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
        -webkit-animation-delay: -0.916666666666667s;
        animation-delay: -0.916666666666667s;
      }

      > div:nth-child(2) {
        -webkit-transform: rotate(30deg);
        transform: rotate(30deg);
        -webkit-animation-delay: -0.833333333333331s;
        animation-delay: -0.833333333333331s;
      }

      > div:nth-child(3) {
        -webkit-transform: rotate(60deg);
        transform: rotate(60deg);
        -webkit-animation-delay: -0.75s;
        animation-delay: -0.75s;
      }

      > div:nth-child(4) {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
        -webkit-animation-delay: -0.666666666666667s;
        animation-delay: -0.666666666666667s;
      }

      > div:nth-child(5) {
        -webkit-transform: rotate(120deg);
        transform: rotate(120deg);
        -webkit-animation-delay: -0.583333333333331s;
        animation-delay: -0.583333333333331s;
      }

      > div:nth-child(6) {
        -webkit-transform: rotate(150deg);
        transform: rotate(150deg);
        -webkit-animation-delay: -0.5s;
        animation-delay: -0.5s;
      }

      > div:nth-child(7) {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
        -webkit-animation-delay: -0.416666666666667s;
        animation-delay: -0.416666666666667s;
      }

      > div:nth-child(8) {
        -webkit-transform: rotate(210deg);
        transform: rotate(210deg);
        -webkit-animation-delay: -0.333333333333331s;
        animation-delay: -0.333333333333331s;
      }

      > div:nth-child(9) {
        -webkit-transform: rotate(240deg);
        transform: rotate(240deg);
        -webkit-animation-delay: -0.25s;
        animation-delay: -0.25s;
      }

      > div:nth-child(10) {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
        -webkit-animation-delay: -0.166666666666667s;
        animation-delay: -0.166666666666667s;
      }

      > div:nth-child(11) {
        -webkit-transform: rotate(300deg);
        transform: rotate(300deg);
        -webkit-animation-delay: -0.083333333333331s;
        animation-delay: -0.083333333333331s;
      }
    }

    .text-ctn {
      font-size: 5em;
      /*color: #129E3F;*/
      color: #1890ff;
      position: relative;
      top: -50px;

      > span:nth-child(1) {
        opacity: 0;
        /*调用名称为fade-in的动画，全程动画显示时间1s，进入方式为linear，延时0.09090s进入，重复播放*/
        animation: fade-in 1s linear 0.09090s infinite;
        -webkit-animation: fade-in 1s linear 0.09090s infinite;
        -moz-animation: fade-in 1s linear 0.09090s infinite;
        -o-animation: fade-in 1s linear 0.09090s infinite;
        -ms-animation: fade-in 1s linear 0.09090s infinite;
      }

      > span:nth-child(2) {
        opacity: 0;
        animation: fade-in 1s linear 0.18181s infinite;
        -webkit-animation: fade-in 1s linear 0.18181s infinite;
        -moz-animation: fade-in 1s linear 0.18181s infinite;
        -o-animation: fade-in 1s linear 0.18181s infinite;
        -ms-animation: fade-in 1s linear 0.18181s infinite;
      }

      > span:nth-child(3) {
        opacity: 0;
        animation: fade-in 1s linear 0.27272s infinite;
        -webkit-animation: fade-in 1s linear 0.27272s infinite;
        -moz-animation: fade-in 1s linear 0.27272s infinite;
        -o-animation: fade-in 1s linear 0.27272s infinite;
        -ms-animation: fade-in 1s linear 0.27272s infinite;
      }

      > span:nth-child(4) {
        opacity: 0;
        animation: fade-in 1s linear 0.36363s infinite;
        -webkit-animation: fade-in 1s linear 0.36363s infinite;
        -moz-animation: fade-in 1s linear 0.36363s infinite;
        -o-animation: fade-in 1s linear 0.36363s infinite;
        -ms-animation: fade-in 1s linear 0.36363s infinite;
      }

      > span:nth-child(5) {
        opacity: 0;
        animation: fade-in 1s linear 0.45454s infinite;
        -webkit-animation: fade-in 1s linear 0.45454s infinite;
        -moz-animation: fade-in 1s linear 0.45454s infinite;
        -o-animation: fade-in 1s linear 0.45454s infinite;
        -ms-animation: fade-in 1s linear 0.45454s infinite;
      }

      > span:nth-child(6) {
        opacity: 0;
        animation: fade-in 1s linear 0.54545s infinite;
        -webkit-animation: fade-in 1s linear 0.54545s infinite;
        -moz-animation: fade-in 1s linear 0.54545s infinite;
        -o-animation: fade-in 1s linear 0.54545s infinite;
        -ms-animation: fade-in 1s linear 0.54545s infinite;
      }

      > span:nth-child(7) {
        opacity: 0;
        animation: fade-in 1s linear 0.63636s infinite;
        -webkit-animation: fade-in 1s linear 0.63636s infinite;
        -moz-animation: fade-in 1s linear 0.63636s infinite;
        -o-animation: fade-in 1s linear 0.63636s infinite;
        -ms-animation: fade-in 1s linear 0.63636s infinite;
      }

      > span:nth-child(8) {
        opacity: 0;
        animation: fade-in 1s linear 0.72727s infinite;
        -webkit-animation: fade-in 1s linear 0.72727s infinite;
        -moz-animation: fade-in 1s linear 0.72727s infinite;
        -o-animation: fade-in 1s linear 0.72727s infinite;
        -ms-animation: fade-in 1s linear 0.72727s infinite;
      }

      > span:nth-child(9) {
        opacity: 0;
        animation: fade-in 1s linear 0.81818s infinite;
        -webkit-animation: fade-in 1s linear 0.81818s infinite;
        -moz-animation: fade-in 1s linear 0.81818s infinite;
        -o-animation: fade-in 1s linear 0.81818s infinite;
        -ms-animation: fade-in 1s linear 0.81818s infinite;
      }

      > span:nth-child(10) {
        opacity: 0;
        animation: fade-in 1s linear 0.90909s infinite;
        -webkit-animation: fade-in 1s linear 0.90909s infinite;
        -moz-animation: fade-in 1s linear 0.90909s infinite;
        -o-animation: fade-in 1s linear 0.90909s infinite;
        -ms-animation: fade-in 1s linear 0.90909s infinite;
      }
    }
  }
}








