package com.cn.xiang.fsockmanageweb.utils.enum_;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:02
 */
public enum DeployStatusEnum {
    NEW("NEW", "新建"),
    UPLOADED_ZIP("UPLOADED_ZIP", "已上传zip"),
    MODIFY_SERVER_CONFIG("MODIFY_SERVER_CONFIG", "已修改服务器配置"),
    INSTALL_DOCKER("INSTALLED_DOCKER", "已安装docker"),
    SERVER_STARTED("SERVER_STARTED", "服务器已启动"),
    SERVER_STOPED("SERVER_STOPED", "服务器已停止");

    private String code;
    private String desc;

    DeployStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
