package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.task.ScheduleManager;
import com.cn.xiang.fsockmanageweb.task.utils.enum_.ScheduleStatus;
import com.cn.xiang.fsockmanageweb.task.vo.ScheduleJob;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


@RestController
@RequestMapping("/api_v1/job")
public class JobController extends ControllerSupport {

    /**
     * 用于测试调度
     * @return
     */
    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object job() {

        ScheduleJob scheduleJob = new ScheduleJob();
        scheduleJob.setJobName("job1");
        scheduleJob.setBeanName("testJob");
        scheduleJob.setMethodName("execute");
        scheduleJob.setId("job1Id");
        scheduleJob.setCronExpression("0/1 * * * * ?");
        scheduleJob.setParams("job1 params");
        scheduleJob.setStatus(ScheduleStatus.RUNNING.getCode());
        scheduleJob.setCreateTime(new Date(System.currentTimeMillis()));
        scheduleJob.setUpdateTime(new Date(System.currentTimeMillis()));

        ScheduleManager scheduleManager = ApplicationContextHolder.getBean(ScheduleManager.class);
        scheduleManager.createScheduleJob(scheduleJob);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

}
