/**
 *模型配置
 */
let moduleConfig = [
    {
        moduleId: '/indexModule5',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/indexModules/index5" */ "../../../redux/store/modules/indexModule5/container");
        }
    },
    {
        moduleId: '/productsModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/indexModules/productsModule" */ "../../../redux/store/modules/productsModule/container");
        }
    },
    {
        moduleId: '/orderModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/indexModules/orderModule" */ "../../../redux/store/modules/orderModule/container");
        }
    },
    {
        moduleId: '/orderListModule',
        importCallback: () => {
            return import(/* webpackChunkName: "modules/indexModules/orderListModule" */ "../../../redux/store/modules/orderListModule/container");
        }
    },
];

export default moduleConfig;
