package com.cn.xiang.fsockmanageweb.services;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.UserPo;
import com.cn.xiang.fsockmanageweb.vo.PageVo;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
public interface IUserService extends IService<UserPo> {

    /*    public UserPo findUserByGroupNameAndUsername(String groupName, String username);
        public List<UserPo> findUsersByGroupName(String groupName);*/
    public UserPo findByUsername(String username);

    public ObjectNode findPages(PageVo pageVo);

    public void assignUserGroup(String userId, String groupId);

    public void assignUserTrafficRule(String userId, String trafficRuleId);

    boolean isUserExist(String username);

    UserPo saveWithDefaultTrafficRule(UserPo userPo);

    ObjectNode findDeliverPages(PageVo pageVo);

    void saveUserWithGroupName(UserPo userPo, String groupName);

    void updateWithGroupName(UserPo userPo, String groupName);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DS("default")
    UserPo getById1(String userId);

    void checkUserExpireTime(String params);
}
