import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Space, Table} from "antd";
import UserModifyModal from "../../../../components/user/modal/UserModifyModal";
import UserAddModal from "../../../../components/user/modal/UserAddModal";
import UserDelModal from "../../../../components/user/modal/UserDelModal";
import GroupAddModal from "../../../../components/group/modal/GroupAddModal";
import TrafficRuleAddModal from "../../../../components/trafficRule/modal/trafficRuleAddModal";
import TrafficRuleDelModal from "../../../../components/trafficRule/modal/trafficRuleDelModal";
import TrafficRuleModifyModal from "../../../../components/trafficRule/modal/trafficRuleModifyModal";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: false
});

class TrafficRuleModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true,
            selectedRowKeys: [],
        }
        this.addModalRef = React.createRef();
        this.modifyModalRef = React.createRef();
        this.delModalRef = React.createRef();
        this.tableRef = React.createRef();
    }

    selectedRows;
    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            btnDisabled: false,
            selectedRowKeys: selectedRowKeys
        })
    }

    fillForm = () => {
        let modalInst = this.modifyModalRef.current;
        let formInst = modalInst.formRef.current;
        let row = this.selectedRows[0];

        formInst.setFieldsValue({
            id: row.id,
            name: row.name,
            dailyLimit: row.dailyLimit,
            weeklyLimit: row.weeklyLimit,
            monthlyLimit: row.monthlyLimit,
            writeLimit: row.writeLimit,
            readLimit: row.readLimit,
            enable: row.enable
        });
    }

    addBtnOnClick = (event) => {
        //打开模态框
        this.addModalRef.current.setState({
            visible: true
        });
    };

    modifyBtnOnClick = (event) => {
        //打开模态框
        this.modifyModalRef.current.setState({
            visible: true
        }, () => {
            //setState第二参数，在组件更新完调用
            this.fillForm();
        });
    };

    delBtnOnClick = (event) => {
        //打开模态框
        this.delModalRef.current.setState({
            visible: true
        });
    };

    render() {
        return (
            <div>
                <Space style={{marginBottom: 16}}>
                    <Button type="primary" onClick={this.addBtnOnClick}>
                        添加
                    </Button>
                    <Button type="primary" disabled={this.state.btnDisabled} onClick={this.modifyBtnOnClick}>
                        修改
                    </Button>
                    <Button type="primary" disabled={this.state.btnDisabled} onClick={this.delBtnOnClick}>
                        删除
                    </Button>
                </Space>
                <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={{
                    type: 'radio',
                    selectedRowKeys: this.state.selectedRowKeys,
                    onChange: this.onChange
                }}>
                    <Column align={"center"} title="流量规则名" dataIndex="name"/>
                    <Column align={"center"} title="日流量" dataIndex="dailyLimit"/>
                    <Column align={"center"} title="周流量" dataIndex="weeklyLimit"/>
                    <Column align={"center"} title="月流量" dataIndex="monthlyLimit"/>
                    <Column align={"center"} title="写流量限制" dataIndex="writeLimit"/>
                    <Column align={"center"} title="读流量限制" dataIndex="readLimit"/>
                    <Column align={"center"} title="是否可用" dataIndex="enable" render={
                        (status) => {
                            return status ? '是' : '否';
                        }
                    }/>
                </TableWrapper>
                <TrafficRuleAddModal ref={this.addModalRef} userModalRef={this} handleAddOk={() => {
                    return this.props.handleAddOk.call(this);
                }}/>
                <TrafficRuleModifyModal ref={this.modifyModalRef} userModalRef={this} handleModifyOk={() => {
                    return this.props.handleModifyOk.call(this);
                }}/>
                <TrafficRuleDelModal ref={this.delModalRef} handleDelOk={() => {
                    return this.props.handleDelOk.call(this);
                }}/>
            </div>
        )
    }
}

export default TrafficRuleModule;
