package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.po.OrdersPo;
import com.cn.xiang.fsockmanageweb.services.IDeliverService;
import com.cn.xiang.fsockmanageweb.services.IOrdersService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/api_v1/orders")
public class OrderController extends ControllerSupport {
    @Autowired
    private IOrdersService ordersService;
    @Autowired
    private IDeliverService deliverService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        List<Map<String, String>> list = ordersService.listOrders();

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "deliver", method = RequestMethod.POST)
    public Object deliver(String userId, String orderId) {
        JSONResultMessage message = this.getJSONResultMessage();
        if (deliverService.deliver(userId, orderId)) {
            message.success();
            return message;
        }
        message.error("发货失败");
        return message;
    }

    @RequestMapping(value = "deliverDetail", method = RequestMethod.GET)
    public Object deliverDetail(String orderId) {
        Map<String, String> deliverData = deliverService.deliverDetail(orderId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("deliverData", deliverData);
        return message;
    }

    @RequestMapping(value = "deliverConfirm", method = RequestMethod.POST)
    public Object deliverConfirm(String orderId) {
        deliverService.deliverConfirm(orderId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }
}
