package com.xiang.traffic.client.proxy.server;

import com.xiang.traffic.AbstractSession;
import io.netty.channel.socket.SocketChannel;

public class ProxyServerSession extends AbstractSession {

    private boolean ready = false;

    ProxyServerSession(SocketChannel serverChannel) {
        super(serverChannel);
    }

    void setReady(boolean ready) {
        this.ready = ready;
    }

    public boolean isReady() {
        return ready;
    }
}
