package com.xiang.traffic.misc;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.DatagramChannel;
import io.netty.channel.socket.SocketChannel;
import io.netty.util.concurrent.GenericFutureListener;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:16
 */
public final class BootstrapTemplate {

    private final Bootstrap bootstrap;

    public BootstrapTemplate(Bootstrap template) {
        this.bootstrap = Objects.requireNonNull(template);
    }

    public <T extends Channel> Bootstrap newInstance(Consumer<T> channelInitializer) {
        Bootstrap b = bootstrap.clone();
        b.handler(new ChannelInitializer<T>() {
            @Override
            protected void initChannel(T ch) {
                channelInitializer.accept(ch);
            }
        });

        return b;
    }


    public ChannelFuture doConnect(String host, int port, Consumer<SocketChannel> channelInitializer,
                                   GenericFutureListener<ChannelFuture> future) {
        Bootstrap b = newInstance(channelInitializer);
        return b.connect(host, port).addListener(future);
    }

    public ChannelFuture doConnect(String host, int port, ChannelHandler handler,
                                   GenericFutureListener<ChannelFuture> future) {
        Bootstrap b = bootstrap.clone().handler(handler);
        return b.connect(host, port).addListener(future);
    }


    public ChannelFuture doBind(int port, Consumer<DatagramChannel> channelInitializer,
                                GenericFutureListener<ChannelFuture> future) {
        Bootstrap b = newInstance(channelInitializer);
        return b.bind(port).addListener(future);
    }

    public ChannelFuture doBind(int port, ChannelHandler handler,
                                GenericFutureListener<ChannelFuture> future) {
        Bootstrap b = bootstrap.clone().handler(handler);
        return b.bind(port).addListener(future);
    }
}
