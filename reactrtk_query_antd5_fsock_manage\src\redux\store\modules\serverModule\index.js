import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Form, Input, Space, Table} from "antd";
import ServerAddModal from "../../../../components/server/modal/ServerAddModal";
import ServerModifyModal from "../../../../components/server/modal/ServerModifyModal";
import ServerDelModal from "../../../../components/server/modal/ServerDelModal";
import Link from "../../components/link/container";
import './index.less';

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource', itemKeyName: 'key', isForwardedRef: false
});

class ServerModule extends Component {
    constructor(props) {
        super(props);

        this.state = {
            btnDisabled: true, selectedRowKeys: []
        }

        this.tableRef = React.createRef();
        this.addModalRef = React.createRef();
        this.modifyModalRef = React.createRef();
        this.delModalRef = React.createRef();
        this.searchInputFormRef = React.createRef();
        this.searchInputRef = React.createRef();
    }

    selectedRows;

    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            btnDisabled: false, selectedRowKeys: selectedRowKeys
        })
    }

    fillForm = () => {
        let modalInst = this.modifyModalRef.current;
        let formInst = modalInst.formRef.current;
        let row = this.selectedRows[0];

        formInst.setFieldsValue({
            id: row.id,
            ip: row.ip,
            port: row.port,
            certPort: row.certPort,
            shellPort: row.shellPort,
            username: row.username,
            password: row.password,
            groupName: row.groupName,
            maxUserCount: row.maxUserCount,
        });
    }

    addBtnOnClick = (event) => {
        //打开模态框
        this.addModalRef.current.setState({
            visible: true
        });
    };

    modifyBtnOnClick = (event) => {
        //打开模态框
        this.modifyModalRef.current.setState({
            visible: true
        }, () => {
            //setState第二参数，在组件更新完调用
            this.fillForm();
        });
    };

    delBtnOnClick = (event) => {
        //打开模态框
        this.delModalRef.current.setState({
            visible: true
        });
    };

    render() {
        return (<div>
            <Space style={{marginBottom: 16}} id={'server-module-ctn'}>
                <Button type="primary" onClick={this.addBtnOnClick}>
                    添加
                </Button>
                <Button type="primary" disabled={this.state.btnDisabled} onClick={this.modifyBtnOnClick}>
                    修改
                </Button>
                <Button type="primary" disabled={this.state.btnDisabled} onClick={this.delBtnOnClick}>
                    删除
                </Button>
                <Form ref={this.searchInputFormRef}
                      style={{width: '250px'}}
                      labelCol={{span: 0}}
                      wrapperCol={{span: 30}}
                      name="searchInputForm">
                    <Form.Item name='search'>
                        <Input.Search ref={this.searchInputRef} placeholder={'ip/port/username/pswd/group'}
                                      defaultValue={''}
                                      onSearch={this.props.inputOnSearch.bind(this)}/>
                    </Form.Item>
                </Form>
            </Space>
            <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource}
                          pagination={{
                              showTotal: total => `共${total}条记录 `, showSizeChanger: true,
                          }}
                          rowSelection={{
                              type: 'radio', selectedRowKeys: this.state.selectedRowKeys, onChange: this.onChange
                          }}>
                <Column align={"center"} title="ip地址" dataIndex="ip"/>
                <Column align={"center"} title="端口" dataIndex="port"/>
                <Column align={"center"} title="cert端口" dataIndex="certPort"/>
                <Column align={"center"} title="shell端口" dataIndex="shellPort"/>
                <Column align={"center"} title="服务器用户名" dataIndex="username"/>
                <Column align={"center"} title="密码" dataIndex="password"/>
                <Column align={"center"} title="服务器组名" dataIndex="groupName"/>
                <Column align={"center"} title="用户数量" dataIndex="userCount"
                        render={(_, record) => {
                            let groupName = record.groupName;
                            let userCount = record.userCount;
                            return <Link key={record.id}
                                         href={"#userModule"}
                                         data={{
                                             'search': groupName
                                         }}
                                         menuName={'用户管理'}
                            >{userCount}</Link>
                        }}
                />
                <Column align={"center"} title="服务器部署状态" dataIndex="deployStatus" render={(_, record) => {
                    let deployStatus = record.deployStatus;
                    switch (deployStatus) {
                        case 'NEW':
                            return '新建';
                        case 'UPLOADED_ZIP':
                            return '已上传zip';
                        case 'MODIFY_SERVER_CONFIG':
                            return '已修改服务器配置';
                        case 'INSTALLED_DOCKER':
                            return '已安装docker';
                        case 'SERVER_STARTED':
                            return '已启动';
                        case 'SERVER_STOPED':
                            return '已停止';
                    }
                }}/>
                <Column align={"center"} title="最大用户量" dataIndex="maxUserCount"/>
                <Column align={"center"} title="操作" render={(_, record) => {
                    let serverId = record.id;
                    let ip = record.ip;

                    return <Space size="middle">
                        <Link key={record.id}
                              href={"#packageResourcesModule#serverAssignPackageResourcesModule"}
                              data={{
                                  'serverId': serverId
                              }}
                              menuName={ip + '服务器上传zip资源包'}
                        >上传zip</Link>

                        <a data-serverid={serverId}
                           onClick={this.props.modifyServerConfigBtnOnClick.bind(this)}>修改服务器配置</a>
                        <a data-serverid={serverId}
                           onClick={this.props.installDockerBtnOnClick.bind(this)}>安装docker环境</a>
                        <a data-serverid={serverId} onClick={this.props.startServerBtnOnClick.bind(this)}>启动服务器</a>
                        <a data-serverid={serverId} onClick={this.props.stopServerBtnOnClick.bind(this)}>停止服务器</a>
                    </Space>
                }}/>
            </TableWrapper>

            <ServerAddModal ref={this.addModalRef} userModalRef={this} handleAddOk={() => {
                return this.props.handleAddOk.call(this);
            }}/>
            <ServerModifyModal ref={this.modifyModalRef} userModalRef={this} handleModifyOk={() => {
                return this.props.handleModifyOk.call(this);
            }}/>
            <ServerDelModal ref={this.delModalRef} handleDelOk={() => {
                return this.props.handleDelOk.call(this);
            }}/>
        </div>)
    }
}

export default ServerModule;
