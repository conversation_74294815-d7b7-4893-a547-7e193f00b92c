<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.fsockmanageweb.mapper.IGroupMapper">
    <!--这部分代码没有使用 -->
    <resultMap id="groupResultMap" type="com.cn.xiang.fsockmanageweb.po.GroupPo">
        <!-- <id column="id" property="id"/>
         <result column="name" property="name"/>
         <result column="created_by" property="createdBy"/>
         <result column="created_time" property="createdTime"/>
         <result column="updated_by" property="updatedBy"/>
         <result column="updated_time" property="updatedTime"/>-->

        <!--多对一配置，还是使用类型转换好点，因为xml配置的话，查询和更新都需要重新写-->
        <!--<association property="trafficRulePo" column="traffic_rule_id"
                     select="com.cn.xiang.fsockmanageweb.mapper.ITrafficRuleMapper.selectById"/>
        <association property="trafficCapacityPo" column="traffic_capacity_id"
                     select="com.cn.xiang.fsockmanageweb.mapper.ITrafficCapacityMapper.selectById"/>-->
    </resultMap>

    <select id="findGroupByName" parameterType="String" resultMap="groupResultMap">
        select * from tb_group g where g.name = #{groupName} and g.deleted=0
    </select>
    <!--<select id="listBySearch" parameterType="String" resultMap="groupResultMap">
        select * from tb_group g where g.name like concat('%',#{groupName},'%') and g.deleted=0
    </select>-->
</mapper>
