package com.cn.xiang.fsockmanageweb.task.utils;

import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import com.cn.xiang.fsockmanageweb.task.vo.ScheduleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2024/7/3 15:15
 */
public class JobInvokeUtil {
    private final static Logger LOGGER = LoggerFactory.getLogger(JobInvokeUtil.class);

    public static void invokeMethod(ScheduleJob scheduleJob) {
        // 获取到bean
        Object target = ApplicationContextHolder.getBean(scheduleJob.getBeanName());
        try {
            if (StringUtils.hasText(scheduleJob.getParams())) {
                Method method = target.getClass().getDeclaredMethod(scheduleJob.getMethodName(), String.class);
                ReflectionUtils.makeAccessible(method);
                // 调用bean中的方法
                method.invoke(target, scheduleJob.getParams());
            } else {
                Method method = target.getClass().getDeclaredMethod(scheduleJob.getMethodName());
                ReflectionUtils.makeAccessible(method);
                // 调用bean中的方法
                method.invoke(target);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException("执行定时任务失败", e);
        }
    }
}
