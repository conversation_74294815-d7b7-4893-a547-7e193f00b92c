package com.cn.xiang.fsockmanageweb.po.typeHandler;

import com.cn.xiang.fsockmanageweb.po.OrdersPo;
import com.cn.xiang.fsockmanageweb.services.IOrdersService;
import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/2/5 16:48
 */
public class OrderPoTypeHandler implements TypeHandler<OrdersPo> {
    @Override
    public void setParameter(PreparedStatement ps, int i, OrdersPo parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getId());
    }

    @Override
    public OrdersPo getResult(ResultSet rs, String columnName) throws SQLException {
        String orderId = rs.getString(columnName);
        IOrdersService ordersService = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IOrdersService.class);
        return ordersService.getById1(orderId);
    }

    @Override
    public OrdersPo getResult(ResultSet rs, int columnIndex) throws SQLException {
        String orderId = rs.getString(columnIndex);
        IOrdersService ordersService = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IOrdersService.class);
        return ordersService.getById1(orderId);
    }

    @Override
    public OrdersPo getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return null;
    }
}
