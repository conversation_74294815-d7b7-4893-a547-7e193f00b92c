import React, {Component} from 'react';
import {Form, Input, Modal, Upload, Button} from 'antd'
import {UploadOutlined} from '@ant-design/icons'
import Utils from "../../../core/utils/utils";

/**
 * 包资源添加弹窗
 */
class PackageResourcesAddModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false,
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };

    fileOnChange = ({file, fileList, e}) => {
        if (file.status === 'done') {
            let id = file.response.content.id;
            this.formRef.current.setFieldsValue({id: id});
        }
    }

    fileBeforeUpload = (file, fileList) => {
        let fileName = file.name;
        if (fileName.lastIndexOf('.zip') !== -1) {
            return true;
        }

        window.global.Notification.open({
            type: window.global.Notification.TYPE.ERROR,
            message: "只支持zip格式的包资源文件",
            delay: 3,
        });

        return Upload.LIST_IGNORE;
    }


    render() {
        return <Modal
            title="添加"
            open={this.state.visible}
            onOk={this.props.handleAddOk}
            onCancel={this.handleCancel}
            okText={'添加'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    label="id"
                    name="id"
                    rules={[{required: true, message: 'Please input your id!'}]}
                    hidden={true}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    initialValue={'1.0.0'}
                    label="版本"
                    name="version"
                    rules={[{required: true, message: 'Please input your version!'}]}
                >
                    <Input placeholder={'1.0.0'}/>
                </Form.Item>
                <Form.Item
                    label="文件名（可选）"
                    name="fileName"
                    rules={[{required: false}]}
                >
                    <Input placeholder={'xxx.zip'}/>
                </Form.Item>
                <Form.Item
                    label="上传"
                    name="file"
                    rules={[{required: true, message: 'Please upload file!'}]}
                >
                    <Upload name="file" maxCount={1} onChange={this.fileOnChange} beforeUpload={this.fileBeforeUpload}
                            method={'POST'}
                            action={(() => {
                                return Utils.BaseUrl.getBaseUrl() + '/packageResources/upload';
                            })()} listType="picture">
                        <Button icon={<UploadOutlined/>}>上传</Button>
                    </Upload>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default PackageResourcesAddModal;
