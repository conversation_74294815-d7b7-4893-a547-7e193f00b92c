package com.cn.xiang.ordermanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.ordermanageweb.mapper.IUserMapper;
import com.cn.xiang.ordermanageweb.po.UserPo;
import com.cn.xiang.ordermanageweb.services.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class UserServiceImpl extends ServiceImpl<IUserMapper, UserPo> implements IUserService {

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DS("flyingsocks")
    public UserPo getById1(String userId) {
        return this.getById(userId);
    }
}
