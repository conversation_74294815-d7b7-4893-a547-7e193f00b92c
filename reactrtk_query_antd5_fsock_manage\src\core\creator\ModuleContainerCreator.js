import {connect} from 'react-redux'
import reactLifecycleHoc from "../components/hocComponent/ReactLifecycleWrapper";

/**
 *
 * @param PageComponent 页面组件
 * @param mapStateToProps
 * @param mapDispatchToProps
 * @returns {*}
 */
const create = function (PageComponent, mapStateToProps, mapDispatchToProps) {
    //在redux connect前包裹mounted生命周期
    let Container = reactLifecycleHoc(PageComponent);

    Container = connect(
        mapStateToProps,
        mapDispatchToProps,
        null,
        {forwardRef: true}
    )(Container);

    return Container;
}

let result = {
    create
};
export default result;