const url = 'packageResources';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getPackageResourcesList: builder.query({
            query: (name) => url,
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let list = content.list;
                for (let i = 0; i < list.length; i++) {
                    list[i].key = list[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),

        saveUpload: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/saveUpload',
                    method: 'POST',
                    body: queryArg,
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                return response;
            },
            onQueryStarted: onQueryStarted
        }),

        delPackageResources: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url,
                    method: 'DELETE',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
