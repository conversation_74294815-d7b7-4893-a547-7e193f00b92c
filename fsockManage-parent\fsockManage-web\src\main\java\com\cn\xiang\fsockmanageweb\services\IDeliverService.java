package com.cn.xiang.fsockmanageweb.services;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.DeliverPo;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/27 16:24
 */
public interface IDeliverService extends IService<DeliverPo> {
    @DS("order")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void save1(DeliverPo deliverPo);

    @DS("order")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    DeliverPo getOne1(LambdaQueryWrapper<DeliverPo> eq);

    boolean deliver(String userId, String orderId);

    Map<String, String> deliverDetail(String orderId);

    void deliverConfirm(String orderId);
}
