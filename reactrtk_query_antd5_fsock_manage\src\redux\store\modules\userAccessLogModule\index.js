import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Table} from "antd";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: false
});

class UserAccessLogModule extends Component {
    constructor(props) {
        super(props);
        this.tableRef = React.createRef();
    }

    render() {
        return (
            <div>
                <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={this.rowSelection}
                              pagination={{
                                  total: this.props.total,
                                  pageSizeOptions: [10, 20, 50, 100],
                                  onChange: this.props.loadData,
                                  showTotal: total => `共${total}条记录 `,
                                  defaultPageSize: 10,
                                  defaultCurrent: 1,
                                  position: ['bottomRight'],
                                  size: 'small',
                                  showSizeChanger: true,
                                  showQuickJumper: true,
                              }}
                              scroll={{y: '650px'}}>
                    <Column align={"center"} title="用户名" dataIndex="username"/>
                    <Column align={"center"} title="ip地址" dataIndex="address"/>
                    <Column align={"center"} title="上行流量" dataIndex={"uploadTrafficUsage"}
                            sorter={(a, b) =>
                                (a.uploadTrafficUsage - b.uploadTrafficUsage)
                            }/>
                    <Column align={"center"} title="上行流量（MB）" dataIndex="uploadTrafficUsageHuman"/>
                    <Column align={"center"} title="下行流量" dataIndex="downloadTrafficUsage"
                            sorter={(a, b) =>
                                (a.downloadTrafficUsage - b.downloadTrafficUsage)
                            }/>
                    <Column align={"center"} title="下行流量（MB）" dataIndex="downloadTrafficUsageHuman"/>
                    <Column align={"center"} title="连接时间" dataIndex="connectTime"
                            sorter={(a, b) =>
                                (a.connectTime > b.connectTime)
                            }/>
                    <Column align={"center"} title="断连时间" dataIndex="disconnectTime"
                            sorter={(a, b) =>
                                (a.disconnectTime > b.disconnectTime)
                            }/>
                </TableWrapper>
            </div>
        )
    }
}

export default UserAccessLogModule;
