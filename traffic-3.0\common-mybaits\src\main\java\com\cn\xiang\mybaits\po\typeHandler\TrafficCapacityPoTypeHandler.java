package com.cn.xiang.mybaits.po.typeHandler;

import com.cn.xiang.mybaits.mapper.ITrafficCapacityMapper;
import com.cn.xiang.mybaits.po.TrafficCapacityPo;
import com.cn.xiang.mybaits.utils.ApplicationContextHolder;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/5/23 19:25
 */
public class TrafficCapacityPoTypeHandler implements TypeHandler<TrafficCapacityPo> {
    @Override
    public void setParameter(PreparedStatement ps, int i, TrafficCapacityPo parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getId());
    }

    @Override
    public TrafficCapacityPo getResult(ResultSet rs, String columnName) throws SQLException {
        String trafficRuleId = rs.getString(columnName);
        ITrafficCapacityMapper trafficRuleMapper = ApplicationContextHolder.getInstance().getApplicationContext().getBean(ITrafficCapacityMapper.class);
        return trafficRuleMapper.selectById(trafficRuleId);
    }

    @Override
    public TrafficCapacityPo getResult(ResultSet rs, int columnIndex) throws SQLException {
        String trafficRuleId = rs.getString(columnIndex);
        ITrafficCapacityMapper trafficRuleMapper = ApplicationContextHolder.getInstance().getApplicationContext().getBean(ITrafficCapacityMapper.class);
        return trafficRuleMapper.selectById(trafficRuleId);
    }

    @Override
    public TrafficCapacityPo getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return null;
    }
}
