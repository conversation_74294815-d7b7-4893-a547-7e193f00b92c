import {configureStore} from '@reduxjs/toolkit'
//默认已经包含thunkMiddleware
import thunkMiddleware from "redux-thunk";
import reducersMap from "./reducers";
// import {createLogger} from "redux-logger"
import {baseApi} from "./store/request/services/base/base";

let store = null;
// const loggerMiddleware = createLogger({});
if (process.env.NODE_ENV !== 'production') {
    store = configureStore({
        reducer: reducersMap,
        middleware: (getDefaultMiddleware) => {
            return getDefaultMiddleware({
                serializableCheck: false,
            })
                // .concat(loggerMiddleware)
                .concat(baseApi.middleware);
        }
    });
} else {
    store = configureStore({
        reducer: reducersMap,
        middleware: (getDefaultMiddleware) => {
            return getDefaultMiddleware({
                serializableCheck: false,
            }).concat(baseApi.middleware);
        }
    });
}

export default store;
