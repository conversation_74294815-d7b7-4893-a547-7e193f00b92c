package com.xiang.traffic.protocol;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * +------+------+-----+-----------------+
 * * |HEADER| SUCC | LEN |    EXTRA_DATA   |
 * * +------+------+-----+-----------------+
 *
 * <AUTHOR>
 * @date 2024/6/6 15:17
 */
public class AuthResponseMessage extends Message {

    private static final byte[] HEADER = new byte[]{0x6C, 0x7A, 0x66};

    private static final Charset DEFAULT_ENCODING = StandardCharsets.UTF_8;

    public static final int LENGTH_OFFSET = HEADER.length + 1;

    public static final int LENGTH_SIZE = 2;  //sizeof(short)

    public static final int LENGTH_ADJUSTMENT = 0;

    /**
     * 是否通过认证
     */
    private boolean success;

    /**
     * 附加信息
     */
    private Map<String, Object> extraData;


    public AuthResponseMessage(boolean success) {
        this.success = success;
    }

    public AuthResponseMessage(ByteBuf buf) throws SerializationException {
        super(buf);
    }

    public boolean isSuccess() {
        return success;
    }

    public Map<String, Object> getExtraData() {
        if (this.extraData == null) {
            this.extraData = new HashMap<>();
        }
        return this.extraData;
    }

    @Override
    public ByteBuf serialize(ByteBufAllocator allocator) throws SerializationException {
        Map<String, Object> extraData = this.extraData;
        boolean success = this.success;

        int len = HEADER.length + 1;
        byte[] data = null;
        if (extraData != null) {
            @SuppressWarnings({"rawtypes", "unchecked"})
            JSONObject obj = new JSONObject(extraData);
            data = obj.toJSONString().getBytes(DEFAULT_ENCODING);
        }

        if (data != null) {
            if (data.length > Short.MAX_VALUE) {
                throw new SerializationException(AuthResponseMessage.class, "Extra data length > 32767");
            }
            len += data.length;
        }

        ByteBuf result = allocator.directBuffer(len);
        result.writeBytes(HEADER);
        result.writeBoolean(success);
        if (data != null) {
            result.writeShort((short) data.length);
            result.writeBytes(data);
        } else {
            result.writeShort(0);
        }

        return result;
    }

    @Override
    protected void deserialize(ByteBuf buf) throws SerializationException {
        for (byte b : HEADER) {
            if (buf.readByte() != b) {
                throw new SerializationException(AuthResponseMessage.class, "Wrong header");
            }
        }

        boolean success = buf.readBoolean();
        short extraDataLen = buf.readShort();

        if (extraDataLen > 0) {
            byte[] dst = new byte[extraDataLen];
            buf.readBytes(dst);
            JSONObject obj = JSON.parseObject(new String(dst, DEFAULT_ENCODING));
            this.extraData = (Map<String, Object>) obj.toJavaObject(Map.class);
        }

        this.success = success;
    }


    public static byte[] getMessageHeader() {
        return Arrays.copyOf(HEADER, HEADER.length);
    }
}
