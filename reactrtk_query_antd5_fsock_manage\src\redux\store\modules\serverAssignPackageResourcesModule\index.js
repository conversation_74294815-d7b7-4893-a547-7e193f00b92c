import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Space, Table} from "antd";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: false
});

class ServerAssignPackageResourcesModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true,
            selectedRowKeys: []
        }
        this.tableRef = React.createRef();
    }
    selectedRows;
    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            btnDisabled: false,
            selectedRowKeys: selectedRowKeys
        })
    }

    render() {
        return (
            <div>
                <Space style={{marginBottom: 16}}>
                    <Button type="primary" disabled={this.state.btnDisabled} onClick={this.props.uploadBtnOnClick.bind(this)}>
                        上传
                    </Button>
                    <Button type="primary" disabled={this.state.btnDisabled} onClick={this.props.autoStartBtnOnClick.bind(this)}>
                        全自动部署（慢）
                    </Button>
                </Space>
                <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={{
                    type: 'radio',
                    selectedRowKeys: this.state.selectedRowKeys,
                    onChange: this.onChange
                }}>
                    <Column align={"center"} title="文件名" dataIndex="fileName"/>
                    <Column align={"center"} title="版本" dataIndex="version"/>
                    <Column align={"center"} title="文件路径" dataIndex="filePath"/>
                </TableWrapper>
            </div>
        )
    }
}

export default ServerAssignPackageResourcesModule;
