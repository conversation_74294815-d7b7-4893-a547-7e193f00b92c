#!/bin/bash

echo "========================================"
echo "Traffic Client GraalVM Native Build Script for Linux"
echo "========================================"

# 检查是否安装了GraalVM
if ! command -v native-image &> /dev/null; then
    echo "ERROR: native-image command not found!"
    echo "Please install GraalVM and add native-image to your PATH."
    echo "Download from: https://github.com/graalvm/graalvm-releases"
    exit 1
fi

# 显示GraalVM版本信息
echo "Checking GraalVM version..."
native-image --version

# 清理之前的构建
echo "Cleaning previous builds..."
rm -f target/traffic-client
rm -f target/traffic-client-*-jar-with-dependencies.jar

# 使用Maven构建JAR包
echo "Building JAR with dependencies..."
mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "ERROR: Maven build failed!"
    exit 1
fi

# 查找生成的JAR文件
JAR_FILE=$(find target -name "traffic-client-*-jar-with-dependencies.jar" | head -n 1)

if [ ! -f "$JAR_FILE" ]; then
    echo "ERROR: JAR file not found!"
    exit 1
fi

echo "Found JAR file: $JAR_FILE"

# 使用Maven Native插件构建Native Image
echo "Building native image using Maven plugin..."
mvn native:compile -Pnative
if [ $? -ne 0 ]; then
    echo "WARNING: Maven native plugin failed, trying direct native-image command..."
    
    # 备用方案：直接使用native-image命令
    echo "Building native image directly..."
    native-image \
        --no-fallback \
        --enable-http \
        --enable-https \
        --enable-all-security-services \
        --allow-incomplete-classpath \
        --report-unsupported-elements-at-runtime \
        --initialize-at-build-time=org.slf4j \
        --initialize-at-run-time=io.netty,com.xiang.traffic.client.gui \
        -H:+ReportExceptionStackTraces \
        -H:+AddAllCharsets \
        -H:IncludeResources=".*\.properties$" \
        -H:IncludeResources=".*\.yml$" \
        -H:IncludeResources=".*\.yaml$" \
        -H:IncludeResources="META-INF/.*" \
        -H:ConfigurationFileDirectories=src/main/resources/META-INF/native-image \
        -H:Name=traffic-client \
        -jar "$JAR_FILE"
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Native image build failed!"
        exit 1
    fi
fi

# 检查生成的可执行文件
if [ -f "target/traffic-client" ]; then
    echo "SUCCESS: Native executable created at target/traffic-client"
    
    # 显示文件大小和权限
    ls -lh target/traffic-client
    
    # 确保可执行权限
    chmod +x target/traffic-client
    
    echo ""
    echo "You can now run the native executable:"
    echo "  ./target/traffic-client"
elif [ -f "traffic-client" ]; then
    echo "SUCCESS: Native executable created at traffic-client"
    
    # 移动到target目录
    mkdir -p target
    mv traffic-client target/
    
    # 显示文件大小和权限
    ls -lh target/traffic-client
    
    # 确保可执行权限
    chmod +x target/traffic-client
    
    echo ""
    echo "You can now run the native executable:"
    echo "  ./target/traffic-client"
else
    echo "ERROR: Native executable not found!"
    exit 1
fi

echo ""
echo "========================================"
echo "Build completed successfully!"
echo "========================================"
