import React, {Component} from 'react'
import {Tabs} from 'antd'
import './moduleTabs.less'
import {CloseCircleOutlined, CloseOutlined, CloseSquareOutlined, LeftOutlined, RightOutlined} from "@ant-design/icons";
import Contextmenu from "../../../components/contextmenu/Contextmenu";


class ModuleTabs extends Component {
    constructor(props) {
        super(props);

        this.state = {
            left: 0,
            top: 0,
            contextMenuVisible: false,
        }
        this.contextMenuTargetTabActiveKey = '';
        this.onContextmenu = this.onContextmenu.bind(this);

        this.contextmenuList = [{
            key: '1',
            icon: <CloseCircleOutlined/>,
            label: '关闭当前',
            onClick: this.props.onCurrentTabsCloseClick.bind(this)
        },
            {
                key: '2',
                icon: <CloseOutlined/>,
                label: '关闭其他',
                onClick: this.props.onOtherTabsCloseClick.bind(this)
            },
            {
                key: '3',
                icon: <LeftOutlined/>,
                label: '关闭左侧',
                onClick: this.props.onLeftTabsCloseClick.bind(this)
            },
            {
                key: '4',
                icon: <RightOutlined/>,
                label: '关闭右侧',
                onClick: this.props.onRightTabsCloseClick.bind(this)
            },
            {
                key: '5',
                icon: <CloseSquareOutlined/>,
                label: '关闭全部',
                onClick: this.props.onAllTabsCloseClick.bind(this)
            }
        ];
    }

    closeContextMenu = (e) => {
        this.setState({
            contextMenuVisible: false
        });
    }

    onContextmenu = (e => {
        e.preventDefault();
        let target = e.target;
        while (!target.classList.contains('ant-tabs-tab')) {
            target = target.parentNode;
        }

        const menuMinWidth = 105
        const offsetLeft = target.getBoundingClientRect().left // container margin left
        const offsetWidth = target.offsetWidth // container width
        const maxLeft = offsetWidth - menuMinWidth // left boundary
        const l = e.clientX - offsetLeft + 15 // 15: margin right

        if (l > maxLeft) {
            this.setState({
                left: maxLeft + offsetLeft
            });
        } else {
            this.setState({
                left: l + offsetLeft
            });
        }

        this.setState({
            top: e.clientY,
            contextMenuVisible: true,
        });

        this.contextMenuTargetTabActiveKey = target.dataset.nodeKey
    });

    onEdit = (targetKey, action) => {
        this.props[action](targetKey);
    };

    componentDidUpdate(prevProps, prevState, snapshot) {
        //ui更新后为tabs添加右键菜单事件
        let tabsEle = document.querySelectorAll('.module-tabs-ctn .ant-tabs-tab');
        tabsEle.forEach((item, index) => {
            item.removeEventListener('contextmenu', this.onContextmenu);
            item.addEventListener('contextmenu', this.onContextmenu);
        });

        // 监听this.state.contextMenuVisible
        if (prevState.contextMenuVisible !== this.state.contextMenuVisible) {
            //true->false
            if (prevState.contextMenuVisible) {
                document.body.removeEventListener('click', this.closeContextMenu)
            } else {
                document.body.addEventListener('click', this.closeContextMenu)
            }
        }
    }


    render() {
        let items = [];
        this.props.panes.forEach((pane) => {
            let item = {
                key: pane.key,
                label: pane.title,
            };
            items.push(item);
        });

        return (
            <div className={"module-tabs-ctn"}>
                <Tabs
                    size={'large'}
                    hideAdd
                    onChange={this.props.onChange}
                    activeKey={this.props.activeKey}
                    type="editable-card"
                    onEdit={this.onEdit}
                    items={items}
                >
                </Tabs>
                <Contextmenu dataList={this.contextmenuList} top={this.state.top} left={this.state.left}
                             visible={this.state.contextMenuVisible}
                             onMouseLeave={this.closeContextMenu}></Contextmenu>
            </div>
        )
    }
}

export default ModuleTabs;
