import React, {Component} from 'react';
import {<PERSON><PERSON>, Card, Divider, Form, Radio} from 'antd';
import './index.less';
import {CaretRightOutlined} from "@ant-design/icons";

function OrderType() {
}

OrderType.monthly = 'monthly';
OrderType.threeMonth = 'threeMonth';

class OrderModule extends Component {
    constructor(props) {
        super(props);

        this.state = {
            totalPrice: 0,
            orderType: OrderType.monthly,
        };

        this.OrderType = OrderType;
        this.formRef = React.createRef();
    }

    onChange = (e) => {
        let formVal = this.formRef.current.getFieldsValue();
        let orderType = formVal.orderType;
        this.setState({
            orderType,
            totalPrice: this.props.product.price * (orderType === OrderType.monthly ? 1 : 3),
        });
    };

    render() {
        let product = this.props.product || {};
        return (
            <div className={"order-module-ctn"}>
                <div className={"order-module-left-ctn"}>
                    <div className={"order-module-title"}>配置订单</div>
                    <Card title={product.name} bordered={false} style={{width: 900}}>
                        <div>{product.price}元/月</div>
                        <div>{product.monthlyTraffic} GB月流量</div>
                        <div>{product.bandwidth}M 带宽</div>
                        <div className={"available-ctn"}>{product.stock} Available</div>
                    </Card>

                    <div className={"order-type-ctn"}>
                        <div className={"order-type-title"}>订单类型</div>
                        <div className={"order-type-content-ctn"}>
                            <Form
                                labelCol={{
                                    span: 0,
                                }}
                                wrapperCol={{
                                    span: 14,
                                }}
                                ref={this.formRef}
                                layout="horizontal"
                            > <Form.Item name="orderType" initialValue={OrderType.monthly}>
                                <Radio.Group name={"orderTypeRadio"} defaultValue={OrderType.monthly}
                                             onChange={this.onChange}>
                                    <div className={"order-type-choose-ctn"}>
                                        <div className={"order-type-choose-content"}>
                                            <Radio value={OrderType.monthly}>一个月</Radio>
                                            <div className={"price-ctn"}>${product.price}</div>
                                        </div>
                                    </div>
                                    <div className={"order-type-choose-ctn"}>
                                        <div className={"order-type-choose-content"}>
                                            <Radio value={OrderType.threeMonth}>三个月</Radio>
                                            <div className={"price-ctn"}>${product.price * 3}</div>
                                        </div>
                                    </div>
                                </Radio.Group>
                            </Form.Item>
                            </Form>
                        </div>
                    </div>
                </div>
                <div className={"order-module-right-ctn"}>
                    <div className={"order-detail-ctn"}>
                        <div className={"order-detail-title"}>订单</div>
                        <div className={"order-detail-content"}>
                            <div className={"detail-item"}>
                                <div>{product.name}:</div>
                                <div>${product.price}</div>
                            </div>
                        </div>
                        <Divider/>
                        <div className={"order-detail-type-ctn"}>
                            <div className={"order-detail-type-title"}>订单类型</div>
                            <div className={"order-detail-type-item detail-item"}>
                                <div>{this.state.orderType === OrderType.monthly ? '一个月' : '三个月'}</div>
                                <div>${this.state.orderType === OrderType.monthly ? product.price : product.price * 3}</div>
                            </div>
                        </div>
                        <Divider/>
                        <div className={"order-detail-total-ctn"}>
                            <div className={"order-detail-total-title"}>总价</div>
                            <div className={"order-detail-total-price"}>${this.state.totalPrice}</div>
                        </div>
                        <div className={"order-detail-btn-ctn"}>
                            <Button onClick={this.props.confirmOnClick.bind(this)}>确认<CaretRightOutlined/></Button>
                        </div>
                        <div className={"order-redirect-page"}></div>
                    </div>
                </div>
            </div>
        )
    }
}

export default OrderModule;
