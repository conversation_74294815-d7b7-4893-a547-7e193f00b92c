package com.cn.xiang.fsockmanageweb.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;

/**
 * <AUTHOR>
 * @date 2024/2/5 16:23
 */
@TableName(value = "tb_traffic_capacity")
public class TrafficCapacityPo extends BaseEntity {
    private long trafficCapacity;
    private String trafficCapacityHuman;

    public long getTrafficCapacity() {
        return trafficCapacity;
    }

    public void setTrafficCapacity(long trafficCapacity) {
        this.trafficCapacity = trafficCapacity;
    }

    public String getTrafficCapacityHuman() {
        return trafficCapacityHuman;
    }

    public void setTrafficCapacityHuman(String trafficCapacityHuman) {
        this.trafficCapacityHuman = trafficCapacityHuman;
    }
}
