package com.xiang.traffic.misc;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelOutboundHandlerAdapter;
import io.netty.channel.ChannelPromise;

import java.util.Arrays;
import java.util.Objects;

/**
 * 在发送消息时自动添加分隔符
 *
 * <AUTHOR>
 * @date 2024/6/6 15:16
 */
public class DelimiterOutboundHandler extends ChannelOutboundHandlerAdapter {

    /**
     * 分隔符ByteBuf
     */
    private final ByteBuf delimiter;

    /**
     * 强制使用VoidPromise
     */
    private final boolean enforceVoidPromise;


    public DelimiterOutboundHandler(byte[] delimiter) {
        this(delimiter, false);
    }


    public DelimiterOutboundHandler(byte[] delimiter, boolean enforceVoidPromise) {
        Objects.requireNonNull(delimiter);
        byte[] arr = Arrays.copyOf(delimiter, delimiter.length);
        this.delimiter = Unpooled.wrappedBuffer(arr).asReadOnly();
        this.enforceVoidPromise = enforceVoidPromise;
    }


    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        if (msg instanceof ByteBuf) {
            delimiter.readerIndex(0);
            if (enforceVoidPromise) {
                promise = ctx.voidPromise();
            }
            ctx.write(msg, promise);
            ctx.write(delimiter.retain(), promise);
        } else {
            ctx.write(msg, promise);
        }
    }
}
