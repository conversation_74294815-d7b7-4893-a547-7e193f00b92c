package com.cn.xiang.fsockmanageweb.services;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.PackageResourcesPo;
import com.cn.xiang.fsockmanageweb.vo.PackageResourcesVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @date 2024/2/5 15:37
 */
public interface IPackageResourcesService extends IService<PackageResourcesPo> {
    public String upload(MultipartFile file);

    public void saveUpload(PackageResourcesVo packageResourcesVo);

    public void removeUploadPackageResources(String id);

    void downloadPackageResources(String id, HttpServletResponse httpServletResponse);
}
