-cp
E:\\ideaWorkspace\\workspace__\\ideaWK\\springbootWk\\fsock_manage\\traffic-3.0\\client\\target\\traffic-client-3.1-SNAPSHOT.jar;E:\\ideaWorkspace\\workspace__\\ideaWK\\springbootWk\\fsock_manage\\traffic-3.0\\common\\target\\traffic-common-3.1-SNAPSHOT.jar;E:\\.m2\\repository\\io\\netty\\netty-all\\4.1.106.Final\\netty-all-4.1.106.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-buffer\\4.1.101.Final\\netty-buffer-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec\\4.1.101.Final\\netty-codec-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-dns\\4.1.101.Final\\netty-codec-dns-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-haproxy\\4.1.101.Final\\netty-codec-haproxy-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-http\\4.1.101.Final\\netty-codec-http-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-http2\\4.1.101.Final\\netty-codec-http2-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-memcache\\4.1.101.Final\\netty-codec-memcache-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-mqtt\\4.1.101.Final\\netty-codec-mqtt-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-redis\\4.1.101.Final\\netty-codec-redis-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-smtp\\4.1.101.Final\\netty-codec-smtp-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-socks\\4.1.101.Final\\netty-codec-socks-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-stomp\\4.1.101.Final\\netty-codec-stomp-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-xml\\4.1.101.Final\\netty-codec-xml-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-common\\4.1.101.Final\\netty-common-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-handler\\4.1.101.Final\\netty-handler-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-unix-common\\4.1.101.Final\\netty-transport-native-unix-common-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-handler-proxy\\4.1.101.Final\\netty-handler-proxy-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-handler-ssl-ocsp\\4.1.101.Final\\netty-handler-ssl-ocsp-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver\\4.1.101.Final\\netty-resolver-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns\\4.1.101.Final\\netty-resolver-dns-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport\\4.1.101.Final\\netty-transport-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-rxtx\\4.1.101.Final\\netty-transport-rxtx-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-sctp\\4.1.101.Final\\netty-transport-sctp-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-udt\\4.1.101.Final\\netty-transport-udt-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-classes-epoll\\4.1.101.Final\\netty-transport-classes-epoll-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-classes-kqueue\\4.1.101.Final\\netty-transport-classes-kqueue-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns-classes-macos\\4.1.101.Final\\netty-resolver-dns-classes-macos-4.1.101.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-epoll\\4.1.101.Final\\netty-transport-native-epoll-4.1.101.Final-linux-x86_64.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-epoll\\4.1.101.Final\\netty-transport-native-epoll-4.1.101.Final-linux-aarch_64.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-epoll\\4.1.106.Final\\netty-transport-native-epoll-4.1.106.Final-linux-riscv64.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-kqueue\\4.1.101.Final\\netty-transport-native-kqueue-4.1.101.Final-osx-x86_64.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-kqueue\\4.1.101.Final\\netty-transport-native-kqueue-4.1.101.Final-osx-aarch_64.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns-native-macos\\4.1.101.Final\\netty-resolver-dns-native-macos-4.1.101.Final-osx-x86_64.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns-native-macos\\4.1.101.Final\\netty-resolver-dns-native-macos-4.1.101.Final-osx-aarch_64.jar;E:\\.m2\\repository\\org\\slf4j\\slf4j-api\\2.0.11\\slf4j-api-2.0.11.jar;E:\\.m2\\repository\\org\\slf4j\\slf4j-reload4j\\2.0.11\\slf4j-reload4j-2.0.11.jar;E:\\.m2\\repository\\ch\\qos\\reload4j\\reload4j\\1.2.22\\reload4j-1.2.22.jar;E:\\.m2\\repository\\com\\alibaba\\fastjson\\2.0.45\\fastjson-2.0.45.jar;E:\\.m2\\repository\\com\\alibaba\\fastjson2\\fastjson2-extension\\2.0.45\\fastjson2-extension-2.0.45.jar;E:\\.m2\\repository\\com\\alibaba\\fastjson2\\fastjson2\\2.0.45\\fastjson2-2.0.45.jar;E:\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.14.0\\commons-lang3-3.14.0.jar;E:\\.m2\\repository\\commons-validator\\commons-validator\\1.8.0\\commons-validator-1.8.0.jar;E:\\.m2\\repository\\commons-beanutils\\commons-beanutils\\1.9.4\\commons-beanutils-1.9.4.jar;E:\\.m2\\repository\\commons-digester\\commons-digester\\2.1\\commons-digester-2.1.jar;E:\\.m2\\repository\\commons-logging\\commons-logging\\1.3.0\\commons-logging-1.3.0.jar;E:\\.m2\\repository\\commons-collections\\commons-collections\\3.2.2\\commons-collections-3.2.2.jar;E:\\.m2\\repository\\org\\eclipse\\swt\\org.eclipse.swt.win32.win32.x86_64\\4.3\\org.eclipse.swt.win32.win32.x86_64-4.3.jar;E:\\.m2\\repository\\org\\jfree\\jfreechart\\1.0.19\\jfreechart-1.0.19.jar;E:\\.m2\\repository\\org\\jfree\\jcommon\\1.0.23\\jcommon-1.0.23.jar;E:\\.m2\\repository\\org\\jfree\\jfreechart-swt\\1.0\\jfreechart-swt-1.0.jar;E:\\.m2\\repository\\org\\jfree\\swtgraphics2d\\1.0\\swtgraphics2d-1.0.jar;E:\\.m2\\repository\\org\\eclipse\\swt\\org.eclipse.swt.cocoa.macosx.x86_64\\4.3\\org.eclipse.swt.cocoa.macosx.x86_64-4.3.jar;E:\\.m2\\repository\\org\\jfree\\jfreesvg\\3.4\\jfreesvg-3.4.jar;E:\\.m2\\repository\\org\\apache\\commons\\commons-text\\1.9\\commons-text-1.9.jar
--no-fallback
--verbose
-o
E:\\ideaWorkspace\\workspace__\\ideaWK\\springbootWk\\fsock_manage\\traffic-3.0\\client\\target\\traffic-client
--no-fallback
--enable-http
--enable-https
--enable-all-security-services
--allow-incomplete-classpath
--report-unsupported-elements-at-runtime
--initialize-at-build-time=org.slf4j
--initialize-at-run-time=io.netty,com.xiang.traffic.client.gui
-H:+ReportExceptionStackTraces
-H:+AddAllCharsets
-H:IncludeResources=.*\\.properties$
-H:IncludeResources=.*\\.yml$
-H:IncludeResources=.*\\.yaml$
-H:IncludeResources=META-INF/.*
-H:ConfigurationFileDirectories=src/main/resources/META-INF/native-image
