package com.cn.xiang.ordermanageweb.po.typeHandler;

import com.cn.xiang.ordermanageweb.po.ServerPo;
import com.cn.xiang.ordermanageweb.services.IServerService;
import com.cn.xiang.ordermanageweb.spring.ApplicationContextHolder;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/2/5 16:48
 */
public class ServerPoTypeHandler implements TypeHandler<ServerPo> {
    @Override
    public void setParameter(PreparedStatement ps, int i, ServerPo parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getId());
    }

    @Override
    public ServerPo getResult(ResultSet rs, String columnName) throws SQLException {
        String trafficRuleId = rs.getString(columnName);
        IServerService serverService = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IServerService.class);
        return serverService.getById1(trafficRuleId);
    }

    @Override
    public ServerPo getResult(ResultSet rs, int columnIndex) throws SQLException {
        String trafficRuleId = rs.getString(columnIndex);
        IServerService serverService = ApplicationContextHolder.getInstance().getApplicationContext().getBean(IServerService.class);
        return serverService.getById1(trafficRuleId);
    }

    @Override
    public ServerPo getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return null;
    }
}
