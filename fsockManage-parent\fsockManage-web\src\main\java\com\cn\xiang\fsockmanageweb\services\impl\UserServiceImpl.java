package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.mapper.IServerMapper;
import com.cn.xiang.fsockmanageweb.po.GroupPo;
import com.cn.xiang.fsockmanageweb.po.ServerPo;
import com.cn.xiang.fsockmanageweb.po.TrafficRulePo;
import com.cn.xiang.fsockmanageweb.po.UserPo;
import com.cn.xiang.fsockmanageweb.services.IServerService;
import com.cn.xiang.fsockmanageweb.utils.DateUtils;
import com.cn.xiang.fsockmanageweb.utils.JsonUtils;
import com.cn.xiang.fsockmanageweb.mapper.IUserAccessLogMapper;
import com.cn.xiang.fsockmanageweb.mapper.IUserMapper;
import com.cn.xiang.fsockmanageweb.services.IGroupService;
import com.cn.xiang.fsockmanageweb.services.ITrafficRuleService;
import com.cn.xiang.fsockmanageweb.services.IUserService;
import com.cn.xiang.fsockmanageweb.vo.PageVo;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class UserServiceImpl extends ServiceImpl<IUserMapper, UserPo> implements IUserService {
    @Autowired
    private IGroupService groupService;
    @Autowired
    private IUserAccessLogMapper userAccessLogMapper;
    @Autowired
    private ITrafficRuleService trafficRuleService;
    @Autowired
    private IServerMapper serverMapper;
    @Autowired
    private IServerService serverService;

    /* @Override
     public UserPo findUserByGroupNameAndUsername(String groupName, String username) {
         return baseMapper.findUserByGroupNameAndUsername(groupName, username);
     }

     @Override
     public List<UserPo> findUsersByGroupName(String groupName){
         return baseMapper.findUsersByGroupName(groupName);
     }*/
    public UserPo findByUsername(String username) {
        LambdaQueryWrapper<UserPo> wrapper = new LambdaQueryWrapper();
        wrapper.eq(UserPo::getUsername, username);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public ObjectNode findPages(PageVo pageVo) {
        List<Map<String, String>> records = baseMapper.findPages(pageVo);

        long total = baseMapper.findTotalBySearch(pageVo);

        //填充用户当月使用流量
        findUserTrafficUsage(records);

        ObjectNode objNode = JsonUtils.createJsonObjectNode();
        objNode.set("records", JsonUtils.beanToJsonNode(records));
        objNode.set("total", JsonUtils.beanToJsonNode(total));
        return objNode;
    }

    /**
     * 填充用户当月使用流量
     *
     * @param records
     */
    public void findUserTrafficUsage(List<Map<String, String>> records) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Map<String, String> map : records) {
            String userId = map.get("id");
            String expiration = map.get("expiration");//2024-06-27 16:50:31
            if (StringUtils.hasText(expiration)) {
                try {
                    //过期日期，可能是未来几个月的日期
                    Date expirationDate = simpleDateFormat.parse(expiration);
                    Calendar expirationCalendar = Calendar.getInstance();
                    expirationCalendar.setTime(expirationDate);
                    int day = expirationCalendar.get(Calendar.DAY_OF_MONTH);
                    int hour = expirationCalendar.get(Calendar.HOUR_OF_DAY);
                    int minute = expirationCalendar.get(Calendar.MINUTE);
                    int second = expirationCalendar.get(Calendar.SECOND);

                    //当前日期
                    Date now = new Date();
                    Calendar nowCalendar = Calendar.getInstance();
                    nowCalendar.setTime(now);
                    int nowYear = nowCalendar.get(Calendar.YEAR);
                    int nowMonth = nowCalendar.get(Calendar.MONTH);

                    //当月过期日期
                    Calendar currentMothCalendar = Calendar.getInstance();
                    currentMothCalendar.set(nowYear, nowMonth, day, hour, minute, second);

                    String startTimeStr;
                    String endTimeStr;
                    if (nowCalendar.before(currentMothCalendar)) {
                        //startTime前一个月
                        nowCalendar.add(Calendar.MONTH, -1);
                        Date startTime = new Date(nowYear - 1900, nowCalendar.get(Calendar.MONTH), day, hour, minute, second);
                        startTimeStr = simpleDateFormat.format(startTime);

                        Date endTime = new Date(nowYear - 1900, nowMonth, day, hour, minute, second);
                        endTimeStr = simpleDateFormat.format(endTime);
                    } else {
                        Date startTime = new Date(nowYear - 1900, nowMonth, day, hour, minute, second);
                        startTimeStr = simpleDateFormat.format(startTime);

                        //endTime下一个月
                        nowCalendar.add(Calendar.MONTH, 1);
                        Date endTime = new Date(nowYear - 1900, nowCalendar.get(Calendar.MONTH), day, hour, minute, second);
                        endTimeStr = simpleDateFormat.format(endTime);
                    }

                    String userTrafficUsageStr = userAccessLogMapper.findUserTrafficUsage(userId, startTimeStr, endTimeStr);
                    if (!StringUtils.hasText(userTrafficUsageStr)) {
                        userTrafficUsageStr = "0";
                    }
                    map.put("userTrafficUsage", userTrafficUsageStr);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    @Override
    public void assignUserGroup(String userId, String groupId) {
        GroupPo groupPo = groupService.getById(groupId);
        long userCount = serverMapper.listUserCountByGroupName(groupPo.getName());
        ServerPo serverPo = serverService.getOne(new LambdaQueryWrapper<ServerPo>().eq(ServerPo::getGroupName, groupPo.getName()));
        if (serverPo == null) {
            throw new RuntimeException("该组没关联服务器");
        }

        if (userCount >= serverPo.getMaxUserCount()) {
            throw new RuntimeException("用户数量超过服务器最大用户数量");
        }

        UserPo userPo = baseMapper.selectById(userId);
        userPo.setGroupId(groupId);
        baseMapper.updateById(userPo);
    }

    @Override
    public void assignUserTrafficRule(String userId, String trafficRuleId) {
        UserPo userPo = baseMapper.selectById(userId);
        TrafficRulePo trafficRulePo = new TrafficRulePo();
        trafficRulePo.setId(trafficRuleId);
        userPo.setTrafficRulePo(trafficRulePo);
        baseMapper.updateById(userPo);
    }

    @Override
    public boolean isUserExist(String username) {
        LambdaQueryWrapper<UserPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPo::getUsername, username);
        UserPo userPo = baseMapper.selectOne(wrapper);
        return userPo != null;
    }

    @Override
    public UserPo saveWithDefaultTrafficRule(UserPo userPo) {
        LambdaQueryWrapper<TrafficRulePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(TrafficRulePo::getName, "no limit");
        TrafficRulePo trafficRulePo = trafficRuleService.getOne(wrapper);

        userPo.setTrafficRulePo(trafficRulePo);
        save(userPo);
        return userPo;
    }

    @Override
    public ObjectNode findDeliverPages(PageVo pageVo) {
        List<Map<String, String>> records = baseMapper.findDeliverPages(pageVo);

        long total = baseMapper.findTotalBySearch(pageVo);

        //填充用户当月使用流量
        findUserTrafficUsage(records);

        ObjectNode objNode = JsonUtils.createJsonObjectNode();
        objNode.set("records", JsonUtils.beanToJsonNode(records));
        objNode.set("total", JsonUtils.beanToJsonNode(total));
        return objNode;
    }

    @Override
    public void saveUserWithGroupName(UserPo userPo, String groupName) {
        GroupPo groupPo = groupService.getOne(new LambdaQueryWrapper<GroupPo>().eq(GroupPo::getName, groupName));
        if (groupPo != null) {
            UserPo userPo1 = this.saveWithDefaultTrafficRule(userPo);
            this.assignUserGroup(userPo1.getId(), groupPo.getId());
        }
    }

    @Override
    public void updateWithGroupName(UserPo userPo, String groupName) {
        GroupPo groupPo = groupService.getOne(new LambdaQueryWrapper<GroupPo>().eq(GroupPo::getName, groupName));
        //如果group有，则关联，否则不管
        if (groupPo != null) {
            userPo.setGroupId(groupPo.getId());
        }
        this.updateById(userPo);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DS("default")
    public UserPo getById1(String userId) {
        return this.getById(userId);
    }


    @Override
    public void checkUserExpireTime(String params) {
        List<UserPo> userPoList = this.list(new LambdaQueryWrapper<UserPo>().eq(UserPo::isEnabled, true));
        for (UserPo userPo : userPoList) {
            String expiration = userPo.getExpiration();
            //被标注不会过期的用户，不过期
            boolean noExpiration = userPo.isNoExpiration();
            if (expiration != null && !noExpiration) {
                LocalDateTime expirationLocalDateTime = DateUtils.parseDate(expiration);
                LocalDateTime now = LocalDateTime.now();
                if (expirationLocalDateTime.isBefore(now)) {
                    userPo.setEnabled(false);
                    updateById(userPo);
                }
            }
        }

        //过期超过一个月，移除用户
        userPoList = this.list(new LambdaQueryWrapper<UserPo>().eq(UserPo::isEnabled, false));
        for (UserPo userPo : userPoList) {
            String expiration = userPo.getExpiration();
            //被标注不会过期的用户，不过期
            boolean noExpiration = userPo.isNoExpiration();
            if (expiration != null && !noExpiration) {
                LocalDateTime expirationLocalDateTime = DateUtils.parseDate(expiration);
                LocalDateTime now = LocalDateTime.now();

                if (expirationLocalDateTime.plusMonths(1).isBefore(now)) {
                    this.removeById(userPo.getId());
                }
            }
        }
    }
}
