package com.cn.xiang.ordermanageweb.utils.enum_;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:02
 */
public enum OrderStatusEnum {
    ORDER_STATUS_CREATED("created", "创建"),
    ORDER_STATUS_PAID("paid", "已支付"),
    ORDER_STATUS_DELIVERED("delivered", "已发货"),
    ORDER_STATUS_COMPLETED("completed", "已完成"),
    ORDER_STATUS_CANCELED("canceled", "已取消");

    private String code;
    private String desc;

    OrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
