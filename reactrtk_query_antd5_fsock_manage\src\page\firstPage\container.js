import Utils from '../../core/utils/utils';
import {baseApi} from "../../redux/store/request/services/base/base"
import {connect} from "react-redux";
import PasswordLoginForm from "./firstPage";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getSysUserIsLogin', baseApi, state);
    const {isSuccess} = result;
    if (isSuccess) {
        return {};
    }
    return ownProps;
};


const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        isSysUserLogin: function () {
            let this_ = this;
            const promise = dispatch(baseApi.endpoints.getSysUserIsLogin.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                subscribe: false,
                forceRefetch: true
            }));
            promise.then((res) => {
                this_.showIndexPage(res.data.isLogined);
            });
        },
    };
};

let Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(PasswordLoginForm);
export default Container;
