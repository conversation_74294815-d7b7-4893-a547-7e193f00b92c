import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import GroupModule from "./index";
import SimpleRouter from "../../../../core/router/smpleRouter";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getTrafficRuleListData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData: data.list
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();
let userId;

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            userId = moduleParam.userId;
            //模型挂载生命周期
            const promise = dispatch(baseApi.endpoints.getTrafficRuleListData.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));

            promise.then((res) => {
                //根据参数恢复选中状态
                if (moduleParam.trafficRuleId) {
                    this.setState({
                        selectedRowKeys: [moduleParam.trafficRuleId],
                        btnDisabled: false
                    });
                } else {
                    //如果没有关联的
                    this.setState({
                        selectedRowKeys: [moduleParam.trafficRuleId],
                        btnDisabled: true
                    });
                }
            });

            apiUnsubscribeSet.add(promise.unsubscribe);

        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData !== this.props.tableData) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData);
            }
        },
        assignBtnOnClick: function () {
            let selectedRowKey = this.state.selectedRowKeys[0];

            const promise = dispatch(baseApi.endpoints.postAssignUserTrafficRule.initiate({
                userId: userId,
                trafficRuleId: selectedRowKey
            }, {
                //subscribe属性可以配置不把数据存储到store
                subscribe: false,
                forceRefetch: true
            }));
            promise.then((res) => {
                window.global.Notification.open({
                    type: window.global.Notification.TYPE.SUCCESS,
                    message: "分配流量规则成功.",
                    delay: 3,
                });

                //关闭模型
                SimpleRouter.close('/userModule/userTrafficRuleAssign');
            });
        },
    };
};

let Container = ModuleContainerCreator.create(GroupModule, mapStateToProps, mapDispatchToProps);
export default Container;
