package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.mapper.IPackageResourcesMapper;
import com.cn.xiang.fsockmanageweb.po.PackageResourcesPo;
import com.cn.xiang.fsockmanageweb.vo.PackageResourcesVo;
import com.cn.xiang.fsockmanageweb.services.IPackageResourcesService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class PackageResourcesServiceImpl extends ServiceImpl<IPackageResourcesMapper, PackageResourcesPo> implements IPackageResourcesService {

    @Override
    public String upload(MultipartFile file) {
        //文件名添加时间戳
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        fileName = fileName.substring(0, fileName.lastIndexOf("."));
        String timestamp = String.valueOf(System.currentTimeMillis());
        fileName = fileName + "_" + timestamp + suffix;

        //准备tmp目录
        ApplicationHome home = new ApplicationHome(getClass());
        File tmpFile = new File(home.getDir() + File.separator + "tmp" + File.separator);
        if (!tmpFile.exists()) {
            tmpFile.mkdirs();
        }

        try {
            //保存文件
            String targetFile = tmpFile + File.separator + fileName;
            file.transferTo(new File(targetFile));

            PackageResourcesPo packageResourcesPo = new PackageResourcesPo();
            packageResourcesPo.setFileName(fileName);
            packageResourcesPo.setFilePath(targetFile);
            packageResourcesPo.setUploaded(false);
            this.baseMapper.insert(packageResourcesPo);

            return packageResourcesPo.getId();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void saveUpload(PackageResourcesVo packageResourcesVo) {
        PackageResourcesPo packageResourcesPo = this.baseMapper.selectById(packageResourcesVo.getId());
        String fileName = packageResourcesVo.getFileName();
        if (StringUtils.hasText(fileName)) {
            fileName = packageResourcesVo.getFileName().lastIndexOf(".zip") != -1
                    ? packageResourcesVo.getFileName()
                    : packageResourcesVo.getFileName() + ".zip";
        } else {
            fileName = packageResourcesPo.getFileName();
        }

        packageResourcesPo.setFileName(fileName);
        packageResourcesPo.setVersion(packageResourcesVo.getVersion());

        try {
            //准备目标目录
            ApplicationHome home = new ApplicationHome(getClass());
            File targetFileDir = new File(home.getDir() + File.separator + "packageResources" + File.separator);
            if (!targetFileDir.exists()) {
                targetFileDir.mkdirs();
            }
            String targetFile = targetFileDir + File.separator + packageResourcesPo.getFileName();

            FileInputStream fis = new FileInputStream(packageResourcesPo.getFilePath());
            FileOutputStream fos = new FileOutputStream(targetFile);
            try {
                IOUtils.copy(fis, fos);
            } finally {
                IOUtils.closeQuietly(fis);
                IOUtils.closeQuietly(fos);
            }
            //移除tmp文件
            File tmpFile = new File(packageResourcesPo.getFilePath());
            if (tmpFile.exists()) {
                tmpFile.delete();
            }

            packageResourcesPo.setFilePath(targetFile);
            packageResourcesPo.setUploaded(true);
            this.baseMapper.updateById(packageResourcesPo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void removeUploadPackageResources(String id) {
        PackageResourcesPo packageResourcesPo = this.baseMapper.selectById(id);

        //删除资源文件
        File file = new File(packageResourcesPo.getFilePath());
        if (file.exists()) {
            file.delete();
        }

        //移除记录
        this.baseMapper.deleteById(id);
    }

    @Override
    public void downloadPackageResources(String packageResourceId, HttpServletResponse response) {
        PackageResourcesPo packageResourcesPo = getById(packageResourceId);
        try {
            byte[] bytes = Files.readAllBytes(Paths.get(packageResourcesPo.getFilePath()));
            String fileName = URLEncoder.encode(packageResourcesPo.getFileName(), "UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setHeader("Content-Length", String.valueOf(bytes.length));
            response.getOutputStream().write(bytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
