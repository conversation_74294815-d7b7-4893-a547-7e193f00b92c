import React, {Component} from 'react';
import {Form, Input, Modal, Switch} from 'antd'

/**
 * 组添加弹窗
 */
class GroupAddModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };


    render() {
        return <Modal
            title="添加"
            open={this.state.visible}
            onOk={this.props.handleAddOk}
            onCancel={this.handleCancel}
            okText={'添加'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    label="组名"
                    name="name"
                    rules={[{required: true, message: 'Please input your group name!'}]}
                >
                    <Input/>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default GroupAddModal;
