import React, {Component, Fragment} from 'react';
import ModuleTabs from "../../redux/store/components/moduleTabs/container";
import ModulesContainer from "../../redux/store/components/modulesContainer/container";
import {Layout} from "antd";

const {Content} = Layout;

class ModulePage extends Component {
    constructor(props) {
        super(props);

        this.state = {
            activeKey: '',
            panes: [],
            modules: []
        }
    }

    render() {
        return (
            <Fragment>
                <ModuleTabs acticeKey={this.state.activeKey} panes={this.state.panes}/>
                <Content>
                    <ModulesContainer modules={this.state.modules}/>
                </Content>
            </Fragment>
        );
    }
}

export default ModulePage;
