import React, {Component} from 'react';
import {Modal, Row, Col} from 'antd'
import './index.less'
import Link from "../../redux/store/components/link/container";

/**
 * 发货细节弹窗
 */
class DeliverModal extends Component {
    constructor(props) {
        super(props);

        this.state = {
            visible: false,
        }
    }

    showModal = (visible = false) => {
        this.setState({
            visible: visible
        })
    }

    render() {
        let deliverData = this.props.deliverData || {};

        return <Modal
            className={"deliver-modal-ctn"}
            title="订单详情"
            open={this.state.visible}
            maskClosable={true}
            closable={false}
            footer={null}
            width={'600px'}
            onCancel={() => {
                this.showModal(false);
            }}
            centered={true}
        >
            <Row>
                <Col span={6}>
                    产品名：
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.productName}
                </Col>
                <Col span={6}>
                    单价:
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.price}
                </Col>
            </Row>
            <Row>
                <Col span={6}>
                    数量：
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.quantity}
                </Col>
                <Col span={6}>
                    总价:
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.totalPrice}
                </Col>
            </Row>
            <Row>
                <Col span={6}>
                    购买用户名:
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.buyer}
                </Col>
                <Col span={6}>
                    用户过期时间:
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.userExpiration}
                </Col>
            </Row>
            <Row>
                <Col span={6}>
                    用户名:
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.username}
                </Col>
                <Col span={6}>
                    用户密码：
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.password}
                </Col>
            </Row>
            <Row>
                <Col span={6}>
                    服务器ip：
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.serverIp}
                </Col>
                <Col span={6}>
                    端口:
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.serverPort}
                </Col>
            </Row>
            <Row>
                <Col span={6}>
                    证书端口:
                </Col>
                <Col className={"deliver-modal-col-value"} span={6}>
                    {deliverData.serverCertPort}
                </Col>
            </Row>
        </Modal>
    }
}

export default DeliverModal;
