package com.cn.xiang.ordermanageweb.spring.support;

import com.cn.xiang.ordermanageweb.spring.ServletContextUtil;
import com.cn.xiang.ordermanageweb.utils.JSONResultMessage;
import jakarta.servlet.ServletContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.springframework.web.servlet.support.RequestContext;


/**
 * 主要提供消息格式对象JSONResultMessage和servletContext相关对象的获取
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018年6月8日 上午11:38:10
 */
public class ControllerSupport {

    /**
     * 获取json消息对象
     *
     * @return
     */
    public JSONResultMessage getJSONResultMessage() {
        return new JSONResultMessage();
    }

    /**
     * 获取request
     *
     * @return
     */
    public HttpServletRequest getHttpServletRequest() {
        return ServletContextUtil.getHttpServletRequest();
    }

    /**
     * 获取session
     *
     * @return
     */
    public HttpSession getHttpSession() {
        return ServletContextUtil.getHttpSession();
    }

    /**
     * 获取servletcontext
     *
     * @return
     */
    public ServletContext getServletContext() {
        return ServletContextUtil.getServletContext();
    }

    /**
     * 获取response
     *
     * @return
     */
    public HttpServletResponse getHttpServletResponse() {
        return ServletContextUtil.getHttpServletResponse();
    }

    /**
     * 获取requestContext
     *
     * @return
     */
    public RequestContext getRequestContext() {
        return new RequestContext(this.getHttpServletRequest());
    }
}
