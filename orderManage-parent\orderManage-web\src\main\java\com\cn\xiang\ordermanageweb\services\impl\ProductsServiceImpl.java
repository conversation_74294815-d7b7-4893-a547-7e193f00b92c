package com.cn.xiang.ordermanageweb.services.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.ordermanageweb.mapper.IMenuMapper;
import com.cn.xiang.ordermanageweb.mapper.IProductsMapper;
import com.cn.xiang.ordermanageweb.po.MenuPo;
import com.cn.xiang.ordermanageweb.po.ProductsPo;
import com.cn.xiang.ordermanageweb.services.IMenuService;
import com.cn.xiang.ordermanageweb.services.IProductsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class ProductsServiceImpl extends ServiceImpl<IProductsMapper, ProductsPo> implements IProductsService {
}
