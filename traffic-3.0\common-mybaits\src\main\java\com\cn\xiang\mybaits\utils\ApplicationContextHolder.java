package com.cn.xiang.mybaits.utils;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * applicationContext持有对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018年6月6日 上午11:43:10
 */
public class ApplicationContextHolder implements ApplicationContextAware {
    private static volatile ApplicationContextHolder instance;
    private static final Object SINGLETON_LOCK = new Object();
    private ApplicationContext applicationContext;

    /**
     * 获取实例
     *
     * @return
     */
    public static ApplicationContextHolder getInstance() {
        if (instance == null) {
            synchronized (SINGLETON_LOCK) {
                if (instance == null) {
                    instance = new ApplicationContextHolder();
                }
            }
        }
        return instance;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 获取applicationContext
     *
     * @return
     */
    public ApplicationContext getApplicationContext() {
        return this.applicationContext;
    }

    /**
     * 从spring容器中获取bean
     *
     * @param clazz
     * @return
     */
    public static <T> T getBean(Class<T> clazz) {
        return ApplicationContextHolder.getInstance().getApplicationContext()
                .getBean(clazz);
    }

    /**
     * 根据bean名称获取bean
     *
     * @param beanName
     * @param clazz
     * @return
     */
    public static <T> T getBean(String beanName, Class<T> clazz) {
        return ApplicationContextHolder.getInstance().getApplicationContext()
                .getBean(beanName, clazz);
    }

    /**
     * 根据bean名称获取bean
     *
     * @param beanName
     * @return
     */
    public static Object getBean(String beanName) {
        return ApplicationContextHolder.getInstance().getApplicationContext()
                .getBean(beanName);
    }
}
