[{"name": "com.xiang.traffic.client.ClientBoot", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "main", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "com.xiang.traffic.client.StandardClient", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.xiang.traffic.client.Client", "methods": [{"name": "exitWithNotify", "parameterTypes": ["int", "java.lang.String", "java.lang.Object[]"]}]}, {"name": "com.alibaba.fastjson.JSON", "methods": [{"name": "toJSONString", "parameterTypes": ["java.lang.Object"]}, {"name": "parseObject", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.alibaba.fastjson.JSONObject", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["java.util.Map"]}, {"name": "toJSONString", "parameterTypes": []}, {"name": "for<PERSON>ach", "parameterTypes": ["java.util.function.BiConsumer"]}]}, {"name": "java.util.Properties", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "load", "parameterTypes": ["java.io.InputStream"]}, {"name": "store", "parameterTypes": ["java.io.Writer", "java.lang.String"]}, {"name": "getProperty", "parameterTypes": ["java.lang.String"]}, {"name": "getProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "setProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "for<PERSON>ach", "parameterTypes": ["java.util.function.BiConsumer"]}]}, {"name": "java.util.ResourceBundle", "methods": [{"name": "getBundle", "parameterTypes": ["java.lang.String", "java.util.Locale"]}]}, {"name": "java.lang.System", "methods": [{"name": "getProperties", "parameterTypes": []}, {"name": "getenv", "parameterTypes": []}, {"name": "setProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "gc", "parameterTypes": []}, {"name": "exit", "parameterTypes": ["int"]}]}, {"name": "java.lang.Runtime", "methods": [{"name": "getRuntime", "parameterTypes": []}, {"name": "addShutdownHook", "parameterTypes": ["java.lang.Thread"]}, {"name": "exec", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.Thread", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Runnable", "java.lang.String"]}, {"name": "currentThread", "parameterTypes": []}]}, {"name": "java.awt.Desktop", "methods": [{"name": "getDesktop", "parameterTypes": []}, {"name": "isSupported", "parameterTypes": ["java.awt.Desktop$Action"]}, {"name": "browse", "parameterTypes": ["java.net.URI"]}]}, {"name": "java.util.Base64$Encoder", "methods": [{"name": "encode", "parameterTypes": ["byte[]"]}]}, {"name": "java.util.Base64$Decoder", "methods": [{"name": "decode", "parameterTypes": ["byte[]"]}]}, {"name": "java.util.Base64", "methods": [{"name": "get<PERSON>ncoder", "parameterTypes": []}, {"name": "getDecoder", "parameterTypes": []}]}, {"name": "io.netty.bootstrap.Bootstrap", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.bootstrap.ServerBootstrap", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.channel.nio.NioEventLoopGroup", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.channel.socket.nio.NioSocketChannel", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.channel.socket.nio.NioServerSocketChannel", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.channel.socket.nio.NioDatagramChannel", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.channel.epoll.EpollEventLoopGroup", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.channel.epoll.EpollSocketChannel", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "io.netty.channel.epoll.EpollServerSocketChannel", "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "org.slf4j.LoggerFactory", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.Class"]}]}, {"name": "org.slf4j.<PERSON>", "allDeclaredMethods": true}]