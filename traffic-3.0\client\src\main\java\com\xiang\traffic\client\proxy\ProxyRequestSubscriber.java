package com.xiang.traffic.client.proxy;

import java.util.EnumSet;
import java.util.Set;

import static com.xiang.traffic.client.proxy.ProxyRequest.Protocol;
import static java.util.Collections.*;

/**
 * 代理请求消息订阅者
 *
 * <AUTHOR>
 * @date 2024/6/6 15:15
 */
public interface ProxyRequestSubscriber {

    /**
     * 包括{@link Protocol#TCP}和{@link Protocol#UDP}协议
     */
    Set<Protocol> ANY_PROTOCOL = unmodifiableSet(EnumSet.allOf(Protocol.class));

    /**
     * 仅限{@link Protocol#TCP}协议
     */
    Set<Protocol> ONLY_TCP = singleton(Protocol.TCP);

    /**
     * 仅限{@link Protocol#UDP}协议
     */
    Set<Protocol> ONLY_UDP = singleton(Protocol.UDP);

    /**
     * 接收消息
     *
     * @param request ProxyRequest请求
     */
    void receive(ProxyRequest request);

    /**
     * @return 是否接收需要代理的消息(即经过PAC判定需要代理的消息)
     */
    default boolean receiveNeedProxy() {
        return false;
    }

    /**
     * @return 是否接收无需代理的消息(即经过PAC判定无需代理的消息)
     */
    default boolean receiveNeedlessProxy() {
        return false;
    }

    /**
     * @return 可以接收的代理协议Set
     */
    default Set<Protocol> requestProtocol() {
        return emptySet();
    }
}
