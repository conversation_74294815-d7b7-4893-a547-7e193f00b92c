package com.cn.xiang.ordermanageweb.services;

import com.alipay.api.AlipayApiException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @date 2024/6/26 19:40
 */
public interface IAlipayService {
    String generatePay(String orderId) throws AlipayApiException;

    ObjectNode tradeQuery(String outTradeNo, String tradeNo) throws AlipayApiException;


    boolean payCallback(HttpServletRequest request);

    boolean rsaCheckV1(HttpServletRequest request);

    void payNotify(HttpServletRequest request);
}
