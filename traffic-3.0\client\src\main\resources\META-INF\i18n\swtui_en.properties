swtui.tray.item.open_main_screen_ui = Open main screen
swtui.tray.item.server_config_ui = Configure server...
swtui.tray.item.socks5_config_ui = Configure local socks5 proxy...
swtui.tray.item.http_config_ui = Configure local http proxy...
swtui.tray.item.exit = Exit
swtui.tray.item.proxy_mode = Proxy mode
swtui.tray.item.proxy_mode.no_proxy = None
swtui.tray.item.proxy_mode.gfwlist = GFW List
swtui.tray.item.proxy_mode.ipwhitelist = IP Whitelist
swtui.tray.item.proxy_mode.global = Global
swtui.tray.item.help = Help/About
swtui.tray.item.help.open_config_dir = Open config folder
swtui.tray.item.help.open_log_dir = Open log folder
swtui.tray.item.help.clean_log = Clean log file
swtui.tray.item.help.open_github = GitHub Page
swtui.tray.item.help.open_issue = Issue payback

swtui.main.title = Main Screen
swtui.main.serverlist.label = Server
swtui.main.status.not_connect = Not connect
swtui.main.status.new = Initializing...
swtui.main.status.ssl_initial = Prepare SSL cert connection...
swtui.main.status.ssl_connecting = Ready connect to SSL cert service...
swtui.main.status.ssl_connect_timeout = SSL connection timeout, please checkout server configuration.
swtui.main.status.ssl_connect_auth_failure = Auth failure, please checkout server configuration.
swtui.main.status.ssl_connect = Downloading SSL cert file...
swtui.main.status.ssl_connect_done = Download SSL cert file complete.
swtui.main.status.ssl_connect_error = Error during SSL certificate download.
swtui.main.status.proxy_initial = Prepare connect to proxy service...
swtui.main.status.proxy_connecting = Connecting to proxy service...
swtui.main.status.proxy_connect_timeout = Proxy service connection timed out.
swtui.main.status.proxy_connect = Successfully established a connection with the proxy service.
swtui.main.status.proxy_connect_auth_failure = Authentication failed, please check whether the authentication information is correct
swtui.main.status.proxy_connect_error = An error occurred connecting to the proxy service
swtui.main.status.proxy_disconnect = Temporarily disconnect from the server, try to reconnect...
swtui.main.status.proxy_unused = Proxy server connection has been disconnected
swtui.main.button.connect = Connect
swtui.main.button.disconnect = Disconnect
swtui.main.notice.title = Notice
swtui.main.notice.server_not_select = Please select a server

swtui.serverconfig.title = Server configure
swtui.serverconfig.list.title = Server List
swtui.serverconfig.list.first = Click me to add
swtui.serverconfig.form.label.address = Host
swtui.serverconfig.form.label.port = Port
swtui.serverconfig.form.label.ssl_port = SSL Port
swtui.serverconfig.form.label.encrypt_type = Encrypt
swtui.serverconfig.form.label.auth_type = Auth
swtui.serverconfig.form.label.username = Username
swtui.serverconfig.form.label.password = Password
swtui.serverconfig.form.encrypt.none = None
swtui.serverconfig.form.encrypt.ssl = TLS v1.2
swtui.serverconfig.form.encrypt.ssl_ca = TLS v1.2 (CA)
swtui.serverconfig.form.auth.normal = Normal
swtui.serverconfig.form.auth.user = User
swtui.serverconfig.form.save = Save
swtui.serverconfig.form.delete = Delete
swtui.serverconfig.notice.error.title = Error
swtui.serverconfig.notice.error.host_error = Incorrect server hostname: it needs to be an IPv4 address or a legal hostname/domain name
swtui.serverconfig.notice.error.port_error = Incorrect port number (A number between 1 and 65535)
swtui.serverconfig.notice.error.ssl_port_error = Incorrect cert port number (A number between 1 and 65535)
swtui.serverconfig.notice.info.title = Notice
swtui.serverconfig.notice.info.server_exists = already included {0}:{1} server configuration
swtui.serverconfig.notice.info.no_server_select = Please select the server configuration to be deleted
swtui.serverconfig.notice.success.title = Success
swtui.serverconfig.notice.success.server_added = Server {0}:{1} configuration added successfully
swtui.serverconfig.notice.success.server_updated = Server {0}:{1} configuration updated successfully

swtui.socks5.title = Socks5 Proxy Configure
swtui.socks5.form.label.validate = Validate
swtui.socks5.form.label.username = Username
swtui.socks5.form.label.password = Password
swtui.socks5.form.label.port = Proxy Port
swtui.socks5.form.button.open = Open
swtui.socks5.form.button.close = Close
swtui.socks5.form.button.enter = Confirm
swtui.socks5.form.button.cancel = Cancel
swtui.socks5.notice.title=Notice
swtui.socks5.notice.port_error = Port illegal
swtui.socks5.notice.update_success = Update success, you need rebot program so that can be done.
swtui.socks5.notice.unchanged = Update success

swtui.http.title = HTTP proxy setup
swtui.http.form.label.switch = Switch
swtui.http.form.label.port = Port
swtui.http.form.label.validate = Validate
swtui.http.form.label.username = Username
swtui.http.form.label.password = Password
swtui.http.form.button.switch_open = Enable
swtui.http.form.button.switch_close = Close
swtui.http.form.button.validate_open = Enable
swtui.http.form.button.validate_close = Close
swtui.http.form.button.enter = Confirm
swtui.http.form.button.cancel = Cancel
swtui.http.notice.error.title = Error
swtui.http.notice.error.port_error = Incorrect port number (A number between 1 and 65535)
swtui.http.notice.error.auth_error = When authentication is turned on, the username and password must not be empty.
swtui.http.notice.update_success = Update successful, some settings need to restart the program to take effect.
swtui.http.form.button.wsp_open=Enable
swtui.http.form.button.wsp_close=Close
swtui.tray.item.wsp_proxy=Windows System Proxy
swtui.main.status.proxy_request_disconnect=User logged in elsewhere, disconnected!
swtui.main.status.user_traffic_exhausted=User traffic exhausted, disconnected!
