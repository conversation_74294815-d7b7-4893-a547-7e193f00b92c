import React, {Component} from "react";
import './modulesContainer.less';

class ModulesContainer extends Component {
    /**
     * opacity动画完成后隐藏module
     * @param e
     */
    onTransitionEnd = (e) => {
        let propertyName = e.propertyName;
        let ele = e.target;
        if (propertyName === 'opacity') {
            if (ele.classList.contains('module-to-hidden')) {
                ele.classList.add('module-hidden');
            }
        }
    }

    /**
     * 组件更新后为显示的module添加module-to-visible样式
     */
    updateVisibleModule = () => {
        let elements = document.querySelectorAll('.modulesContainer>.modulesCtn-item');
        elements.forEach((e) => {
            if (!e.classList.contains('module-to-hidden')) {
                //TODO 原因暂时未明，加个延时就能解决页面转为显示时，module-to-visible动画没执行，直接以最终结果显示。
                setTimeout(() => {
                    e.classList.add('module-to-visible');
                }, 10)
            }
        })
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        this.updateVisibleModule();
    }

    render() {
        let moduleArr = [];
        for (let module of this.props.modules) {
            let Cmpt = module.component;
            let moduleId = module.moduleId;
            if (!module.isVisable) {
                moduleArr.push(
                    <div className={'modulesCtn-item module-to-hidden'} id={module.moduleId} key={module.moduleId}
                         onTransitionEnd={this.onTransitionEnd}>
                        <Cmpt ref={(element) => {
                            //使用ref回调函数方式，组件更新时element可能会为null。
                            element && this.props.setCmptRef(moduleId, element);
                        }}/>
                    </div>);
            } else {
                moduleArr.push(
                    <div className={'modulesCtn-item'} id={module.moduleId} key={module.moduleId}
                         onTransitionEnd={this.onTransitionEnd}>
                        <Cmpt ref={(element) => {
                            element && this.props.setCmptRef(moduleId, element);
                        }}/>
                    </div>);
            }
        }
        return (
            <div className={'modulesContainer'}>
                {moduleArr}
            </div>
        )
    }
}

export default ModulesContainer;