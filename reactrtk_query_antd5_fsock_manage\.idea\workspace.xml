<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="210bab9f-acfe-44e8-9544-bfaaee604abe" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2LucoBJZ7pooFf8kPRkWmyKwxG3" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "WebServerToolWindowFactoryState": "false",
    "go.import.settings.migrated": "true",
    "last_opened_file_path": "E:/ideaWorkspace/workspace__/ideaWK/springbootWk/reactrtk_query_antd5_fsock_manage",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_interpreter_path": "node",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "settings.nodejs",
    "ts.external.directory.path": "E:\\IntelliJ IDEA 2022.3.1\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\ideaWorkspace\workspace__\ideaWK\frontendIdeaWk\react_new_update_redux\react_new_update_redux_tookit_rtk_query_antd5" />
      <recent name="C:\Users\<USER>\Desktop\react\react\react_new_update_redux_tookit_antd5" />
      <recent name="C:\Users\<USER>\Desktop\react\react\react_new_update_redux_tookit_antd5\src\redux\store\modules" />
      <recent name="C:\Users\<USER>\Desktop\react\react\react_new_update_redux_tookit_antd5\src\redux\store\components" />
      <recent name="C:\Users\<USER>\Desktop\react\react\react_new_update_redux_tookit_antd5\public" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\react\react\react_new_update_redux_tookit_antd5\src\redux" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="210bab9f-acfe-44e8-9544-bfaaee604abe" name="Changes" comment="" />
      <created>1676724580979</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1676724580979</updated>
      <workItem from="1676724582223" duration="5942000" />
      <workItem from="1676731775844" duration="7559000" />
      <workItem from="1676774770007" duration="3466000" />
      <workItem from="1676878598463" duration="977000" />
      <workItem from="1676879608793" duration="548000" />
      <workItem from="1676880179994" duration="4348000" />
      <workItem from="1677252789416" duration="1347000" />
      <workItem from="1677287477667" duration="1519000" />
      <workItem from="1677318717724" duration="291000" />
      <workItem from="1677510489066" duration="33000" />
      <workItem from="1677554532476" duration="2698000" />
      <workItem from="1680001719079" duration="1761000" />
      <workItem from="1680005715387" duration="1404000" />
      <workItem from="1680053954654" duration="32000" />
      <workItem from="1693539659542" duration="274000" />
      <workItem from="1703838075638" duration="3063000" />
      <workItem from="1704204026273" duration="1844000" />
      <workItem from="1704207234269" duration="5155000" />
      <workItem from="1704245646907" duration="15897000" />
      <workItem from="1704285022459" duration="26000" />
      <workItem from="1705846799414" duration="4578000" />
      <workItem from="1705851843886" duration="2494000" />
      <workItem from="1709693347291" duration="1485000" />
      <workItem from="1709694921638" duration="163000" />
      <workItem from="1709699969853" duration="8000" />
      <workItem from="1712491578653" duration="56000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="includedJSSourcePackages">
      <set>
        <option value="antd" />
      </set>
    </option>
  </component>
  <component name="VgoProject">
    <integration-enabled>false</integration-enabled>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>