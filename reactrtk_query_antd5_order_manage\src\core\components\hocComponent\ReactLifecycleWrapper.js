import React from 'react';
import global from '../../../core/utils/global'
import LIFECYCLE_METHOD_ENUM from "../../utils/LifecycleMethodEnum";

/**
 * 包裹组件，在componentDidMount（）生命周期方法中触发props.moduleMounted
 * @param WrappedComponent
 * @returns {React.ForwardRefExoticComponent<React.PropsWithoutRef<{}> & React.RefAttributes<any>> | *}
 */
const reactLifecycleHoc = (WrappedComponent) => {
    class ReactLifecycleWrapper extends React.Component {
        cmptRef;
        setCmptRef = (element) => {
            //引用到模型组件对象
            this.cmptRef = element;

            //执行回调，把模型组件对象从hoc里层向外层传递出去，redux Container ref->reactLifecycleHoc->modulesCoutainer.js render中的ref=(Cmpt)=>{}
            if (this.props.forwardedRef) {
                typeof this.props.forwardedRef === 'function' ? this.props.forwardedRef(element) : this.props.forwardedRef = element;
            }
        };

        /**
         * 根据module实例找到moduleId
         * @param cmpt
         * @returns {string}
         */
        findModuleIdByCmpt(cmpt) {
            let state = global.store.getState();
            let cmptRefMap = state.moduleContainer.cmptRefMap;
            //根据对象值查找到prop
            let moduleId;
            for (let prop in cmptRefMap) {
                if (cmptRefMap[prop].component === cmpt) {
                    moduleId = prop;
                }
            }
            return moduleId;
        }

        /**
         * 执行生命周期方法
         * @param lifeCycleMethod
         * @param argumentList
         */
        execLifeCycleMethod(lifeCycleMethod, argumentList) {
            if (this.props[lifeCycleMethod]) {
                let moduleId = this.findModuleIdByCmpt(this.cmptRef);

                let moduleParam = global.ModuleParam.getModuleParam(moduleId);

                let arguments_ = [];
                arguments_.push(moduleParam);
                argumentList && (arguments_ = arguments_.concat(argumentList));

                return this.props[lifeCycleMethod].apply(this.cmptRef, arguments_);
            }
        }

        componentDidMount() {
            // if (this.props.moduleMounted) {
            //获取到该模型的moduleId

            //移除值为null的属性
            // let state = global.store.getState();
            // let cmptRefMap = Object.assign({}, state.moduleContainer.cmptRefMap);
            // let cmptRefMap = {...state.moduleContainer.cmptRefMap};
            // for (let moduleId in cmptRefMap) {
            //     if (!cmptRefMap[moduleId]) {
            //         delete cmptRefMap[moduleId];
            //     }
            // }

            // let state = global.store.getState();
            // let cmptRefMap = state.moduleContainer.cmptRefMap;
            // //根据对象值查找到prop
            // let moduleId;
            // for (let prop in cmptRefMap) {
            //     if (cmptRefMap[prop] === this.cmptRef) {
            //         moduleId = prop;
            //     }
            // }
            // let moduleId = this.findModuleIdByCmpt(this.cmptRef);
            //
            // let moduleParam = global.ModuleParam.getModuleParam(moduleId);
            // this.props.moduleMounted.call(this.cmptRef, moduleParam);
            // }
            this.execLifeCycleMethod(LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED)
        }

        componentDidUpdate(prevProps, prevState, snapshot) {
            this.execLifeCycleMethod(LIFECYCLE_METHOD_ENUM.MODULE_UPDATED, [prevProps, prevState, snapshot])
        }

        componentDidCatch(error, errorInfo) {
            this.execLifeCycleMethod(LIFECYCLE_METHOD_ENUM.MODULE_CATCHED, [error, errorInfo])
        }

        shouldComponentUpdate(nextProps, nextState, nextContext) {
            if (this.props[LIFECYCLE_METHOD_ENUM.MODULE_SHOULD_UPDATE]) return this.execLifeCycleMethod(LIFECYCLE_METHOD_ENUM.MODULE_SHOULD_UPDATE, [nextProps, nextState, nextContext]);
            return true;
        }

        render() {
            return <WrappedComponent ref={this.setCmptRef} {...this.props}/>
        }
    }

    return React.forwardRef((props, ref) => (<ReactLifecycleWrapper {...props} forwardedRef={ref}/>));
};

export default reactLifecycleHoc;
