package com.cn.xiang.ordermanageweb.spring.mvc;

import com.alipay.api.AlipayApiException;
import com.cn.xiang.ordermanageweb.services.IAlipayService;
import com.cn.xiang.ordermanageweb.services.IOrdersService;
import com.cn.xiang.ordermanageweb.spring.support.ControllerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @date 2024/6/26 12:26
 */
@Controller
@RequestMapping("/api_v1/toPay")
public class PayForwardController extends ControllerSupport {
    @Autowired
    private IAlipayService alipayService;

    @RequestMapping()
    public ModelAndView forward() {
        String payRedirectPage = (String) this.getHttpSession().getAttribute("payRedirectPage");

        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("payRedirectPage", payRedirectPage);
        modelAndView.setViewName("toPay.html");
        return modelAndView;
    }

    @RequestMapping("/beforeForward")
    public String beforeForward(String orderId) {
        String payRedirectPage = null;
        try {
            payRedirectPage = alipayService.generatePay(orderId);
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
        this.getHttpSession().setAttribute("payRedirectPage", payRedirectPage);

        return "forward:/api_v1/toPay";
    }
}
