import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.less';
import * as serviceWorker from './serviceWorker';
import global from './core/utils/global';
import {Provider} from 'react-redux'
import store from './redux/storeConfig';
import FirstPage from "./page/firstPage/container";
import {ConfigProvider} from 'antd';

window.global = global;

//在Provider外层添加<React.StrictMode>开启严格模式
// ReactDOM.createRoot(
//     document.getElementById('root')
// ).render(
//     <Provider store={store}>
//         <IndexPage/>
//     </Provider>
// );

ReactDOM.createRoot(
    document.getElementById('root')
).render(
    <ConfigProvider
        theme={{
            token: {
                colorPrimary: '#1DA57A',
                borderRadius: 1
            },
        }}
    >
        <Provider store={store}>
            <FirstPage/>
        </Provider>
    </ConfigProvider>
);

// ReactDOM.render(
//     <Provider store={store}>
//         <IndexPage/>
//     </Provider>,
//     document.getElementById('root')
// );

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
