const url = 'sysUser';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        postSysUserLogin: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('username', queryArg.username);
                formData.set('password', queryArg.password);

                return {
                    url: url + '/login',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                return response;
            },
            onQueryStarted: onQueryStarted
        }),
        getSysUserIsLogin: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/isLogined',
                    method: 'GET',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        postSysUserLogout: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/logout',
                    method: 'POST',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
