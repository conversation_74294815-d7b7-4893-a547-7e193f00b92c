import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import PackageResourcesModule from "./index";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getPackageResourcesList', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData1: data.list
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();
let serverId;

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            serverId = moduleParam.serverId;
            //模型挂载生命周期
            const {unsubscribe} = dispatch(baseApi.endpoints.getPackageResourcesList.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);

        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData1 !== this.props.tableData1) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData1);
            }
        },
        uploadBtnOnClick: function (e) {
            let packageResourceId = this.state.selectedRowKeys[0];

            dispatch(baseApi.endpoints.uploadZipToServer.initiate({
                id: serverId,
                packageResourceId: packageResourceId
            }, {
                subscribe: false, forceRefetch: true,
            })).then((response) => {
                if (response.data.status) {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "上传zip成功.",
                        delay: 3,
                    });
                } else {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: "上传zip失败." + response.data.errorMsg,
                        delay: 3,
                    });
                }
            });
        },
        autoStartBtnOnClick: function () {
            let packageResourceId = this.state.selectedRowKeys[0];
            dispatch(baseApi.endpoints.autoStart.initiate({
                id: serverId,
                packageResourceId: packageResourceId
            }, {
                subscribe: false, forceRefetch: true,
            })).then((response) => {
                if (response.data.status) {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "触发全自动部署成功.",
                        delay: 3,
                    });
                } else {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: "全自动部署失败." + response.data.errorMsg,
                        delay: 3,
                    });
                }
            });
        }
    };
};

let Container = ModuleContainerCreator.create(PackageResourcesModule, mapStateToProps, mapDispatchToProps);
export default Container;
