package com.cn.xiang.ordermanageweb.spring.mvc;

import com.cn.xiang.ordermanageweb.services.IAlipayService;
import com.cn.xiang.ordermanageweb.spring.support.ControllerSupport;
import com.cn.xiang.ordermanageweb.utils.JSONResultMessage;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;


@Controller
@RequestMapping("/api_v1/alipay")
public class AlipayController extends ControllerSupport {
    @Autowired
    private IAlipayService alipayService;

    @RequestMapping(value = "callback", method = RequestMethod.GET)
    public Object callback(HttpServletRequest request) {

        //只负责页面调整，不修改订单和支付记录，在notify接口中进行修改
        boolean signVerified = alipayService.payCallback(request);
        if (signVerified) {
            ModelAndView modelAndView = new ModelAndView();
            modelAndView.setViewName("paySuccess.html");
            modelAndView.addObject("success", signVerified);
            return modelAndView;
        } else {
            throw new RuntimeException("签名验证失败");
        }
    }

    @RequestMapping(value = "notify", method = RequestMethod.POST)
    @ResponseBody
    public Object notify(HttpServletRequest request) {
        alipayService.payNotify(request);

        JSONResultMessage resultMessage = this.getJSONResultMessage();
        resultMessage.success();
        return resultMessage;
    }
}
