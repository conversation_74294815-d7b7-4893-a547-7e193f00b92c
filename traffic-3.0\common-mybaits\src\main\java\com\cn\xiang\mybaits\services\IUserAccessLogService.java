package com.cn.xiang.mybaits.services;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.mybaits.po.UserAccessLogPo;

/**
 * <AUTHOR>
 * @date 2024/2/5 23:12
 */
public interface IUserAccessLogService extends IService<UserAccessLogPo> {
    public String userEnter(String userId, String address);

    void userLeave(String accessLogId, long uploadTrafficUsage, long downloadTrafficUsage);

    public long getUserTrafficUsage(String username);
}
