import axios from '../../../core/axios/axios';
import actions from '../../actions';
import global from '../../../core/utils/global'
import requestActionReducerObj from "./reducer"
import Constants from "../../../core/utils/constants";

const requestActions = requestActionReducerObj.actions;

const beginFetch = (subreddit) => {
    return requestActions.beginFetch({
        subreddit: subreddit
    })
};

const receiveFetch = (subreddit, json) => {
    return requestActions.receiveFetch({
        subreddit,
        posts: json,
        receivedAt: Date.now()
    });
};

const receiveCallbackFetch = (subreddit) => {
    return requestActions.receiveCallbackFetch({
        subreddit,
        receivedAt: Date.now()
    });
};

const invalidateSubreddit = (subreddit) => {
    return requestActions.invalidateSubreddit({
        subreddit
    });
};

const selectedSubreddit = (subreddit) => {
    return requestActions.selectSubreddit(subreddit);
};
/**
 *发送请求
 * @param subreddit
 * @param config
 * @param fetchMode
 * @param isShowMask
 * @returns {(function(*, *): void)|*}
 */
const fetch = (subreddit, config, fetchMode = Constants.FETCH_MODE.CALLBACK_STORE, isShowMask = true) => {
    return (dispatch, getState) => {
        dispatch(beginFetch(subreddit));
        dispatch(selectedSubreddit(subreddit));

        // 如果0.5秒没完成数据加载，则显示遮罩，避免遮罩一闪而过
        if (isShowMask) {
            setTimeout(() => {
                let state = getState();
                let isFetching = state.fetchBySubreddit[subreddit].isFetching;
                if (isFetching) {
                    dispatch(actions.loadingMask.showMask(true));
                }
            }, 500);
        }

        axios(config)
            .then(
                response => {
                    return response;
                },
                error => console.log('An error occurred during request', error)
            )
            .then(response => {
                    if (response.status) {
                        /*
                        一般使用successCallback时，会在callback中调用组件action，填充数据，那么组件本身会维护一份data；
                        请求组件receiveCallbackFetch（）时会向store.fetchBySubreddit存储请求data；
                        所以会有两份请求数据存储在store中。
                            fetchMode主要是用于避免上面的情況，
                                CALLBACK_ONLY: 只调用successCallback，不会向store.fetchBySubreddit存储请求数据
                                STORE_ONLY: 只向store.fetchBySubreddit存储请求数据，不调用successCallback
                                STORE_CALLBACK: 调用successCallback，同时向store.fetchBySubreddit存储请求数据
                         */
                        switch (fetchMode) {
                            case Constants.FETCH_MODE.CALLBACK_ONLY:
                                //执行回调（主要为了解决像添加数据到表格的请求，添加成功后一般只返回一个status=true，没有数据更新，同时需要打开提示框通知用户添加成功的场景）
                                if (config.successCallback) {
                                    dispatch(receiveCallbackFetch(subreddit));

                                    config.successCallback(response);
                                }
                                break;
                            case Constants.FETCH_MODE.STORE_ONLY:
                                dispatch(receiveFetch(subreddit, response.content));
                                break;
                            default :
                                dispatch(receiveFetch(subreddit, response.content));

                                //执行回调（主要为了解决像添加数据到表格的请求，添加成功后一般只返回一个status=true，没有数据更新，同时需要打开提示框通知用户添加成功的场景）
                                if (config.successCallback) {
                                    dispatch(receiveCallbackFetch(subreddit));
                                    config.successCallback(response);
                                }
                        }
                    } else {
                        global.Notification.open({
                            type: global.Notification.TYPE.ERROR,
                            message: 'request error',
                            description: response.errorMsg
                        });
                        dispatch(invalidateSubreddit(subreddit));
                    }
                    //关闭遮罩
                    dispatch(actions.loadingMask.showMask(false));
                }
            )
    }
}

let result = {
    request: {
        // beginFetch,
        // receiveFetch,
        // invalidateSubreddit,
        fetch,
    }
};

export default result;
