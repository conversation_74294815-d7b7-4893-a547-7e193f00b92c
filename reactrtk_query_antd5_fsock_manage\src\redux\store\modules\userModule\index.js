import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Space, Table, Input, Form} from "antd";
import UserModifyModal from "../../../../components/user/modal/UserModifyModal";
import UserAddModal from "../../../../components/user/modal/UserAddModal";
import UserDelModal from "../../../../components/user/modal/UserDelModal";
import Link from "../../../../redux/store/components/link/container";
import dayjs from "dayjs";
import './index.less'

const {Column} = Table;
const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource', itemKeyName: 'key', isForwardedRef: false
});

class UserModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true,
            selectedRowKeys: [],
            page: 1,
            pageSize: 10,
        }
        this.addModalRef = React.createRef();
        this.modifyModalRef = React.createRef();
        this.delModalRef = React.createRef();
        this.tableRef = React.createRef();
        this.searchInput = React.createRef();
        this.searchInputFormRef = React.createRef();
    }

    selectedRows;
    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            btnDisabled: false,
            selectedRowKeys: selectedRowKeys,
        })
    }

    fillForm = () => {
        let modalInst = this.modifyModalRef.current;
        let formInst = modalInst.formRef.current;
        let row = this.selectedRows[0];

        formInst.setFieldsValue({
            id: row.id,
            username: row.username,
            password: row.password,
            enabled: row.enabled,
            expiration: row.expiration ? dayjs(row.expiration, DATE_FORMAT) : this.getNextMothDate(),
            noExpiration: row.noExpiration,
            groupName: row.groupName
        });
    }

    getNextMothDate = () => {
        let date = new Date();
        //date.getMonth() + 2
        let moth = date.getMonth() + 2 < 10 ? '0' + (date.getMonth() + 2) : date.getMonth() + 2;
        let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        let hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
        let minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
        let second = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
        let dateStr = date.getFullYear() + '-' + moth + '-' + day + ' ' + hour + ':' + minute + ':' + second;
        return dayjs(dateStr, DATE_FORMAT)
    }

    addBtnOnClick = (event) => {
        //打开模态框
        this.addModalRef.current.setState({
            visible: true
        }, () => {
            let modalInst = this.addModalRef.current;
            let formInst = modalInst.formRef.current;
            formInst.resetFields();
            formInst.setFieldsValue({
                expiration: this.getNextMothDate()
            });
        });
    };

    modifyBtnOnClick = (event) => {
        //打开模态框
        this.modifyModalRef.current.setState({
            visible: true
        }, () => {
            //setState第二参数，在组件更新完调用
            this.fillForm();
        });
    };

    delBtnOnClick = (event) => {
        //打开模态框
        this.delModalRef.current.setState({
            visible: true
        });
    };

    render() {
        return (<div>
                <Space style={{marginBottom: 16}} id={'user-module-ctn'}>
                    <Button type="primary" onClick={this.addBtnOnClick}>
                        添加
                    </Button>
                    <Button type="primary" disabled={this.state.btnDisabled} onClick={this.modifyBtnOnClick}>
                        修改
                    </Button>
                    <Button type="primary" disabled={this.state.btnDisabled} onClick={this.delBtnOnClick}>
                        删除
                    </Button>
                    <Form ref={this.searchInputFormRef}
                          style={{width: '230px'}}
                          labelCol={{span: 0}}
                          wrapperCol={{span: 30}}
                          name="searchInputForm"
                    >
                        <Form.Item name='search'>
                            <Input.Search ref={this.searchInput} placeholder={'username/group/trafficRule'}
                                          defaultValue={''}
                                          onSearch={this.props.inputOnSearch.bind(this)}/>
                        </Form.Item>
                    </Form>
                </Space>
                <TableWrapper
                    ref={this.tableRef}
                    dataSource={this.props.dataSource}
                    rowSelection={{
                        type: 'radio',
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onChange
                    }}
                    pagination={{
                        total: this.props.total,
                        pageSizeOptions: [10, 20, 50, 100],
                        onChange: this.props.loadData.bind(this),
                        showTotal: total => `共${total}条记录 `,
                        defaultPageSize: 10,
                        defaultCurrent: 1,
                        current: this.state.page,
                        pageSize: this.state.pageSize,
                        position: ['bottomRight'],
                        size: 'small',
                        showSizeChanger: true,
                        showQuickJumper: true,
                    }}
                    scroll={{y: '650px'}}>
                    <Column align={"center"} title="用户名" dataIndex="username"/>
                    <Column align={"center"} title="密码" dataIndex="password"/>
                    <Column align={"center"} title="有效期" dataIndex="expiration"/>
                    <Column align={"center"} title="当月已用流量" dataIndex="userTrafficUsage"
                            render={(text, record, index) => {
                                let userTrafficUsage = record['userTrafficUsage'];
                                let userTrafficUsageGb = userTrafficUsage / 1000 / 1000 / 1000;
                                userTrafficUsageGb = Math.round(userTrafficUsageGb * 1000) / 1000;
                                return userTrafficUsageGb + ' GB';
                            }}/>
                    <Column align={"center"} title="是否可用" dataIndex="enabled" render={(status) => {
                        return status ? '是' : '否';
                    }}/>
                    <Column align={"center"} title="是否已售出" dataIndex="delivered" render={(delivered) => {
                        return delivered ? '是' : '否';
                    }}/>
                    {/* <Column align={"center"} title="流量规则" dataIndex={['trafficRulePo', 'name']}
                        render={(text, record, index) => {
                            let trafficRuleName = record['trafficRulePo']['name'];
                            if (!trafficRuleName) {
                                trafficRuleName = '--'
                            }

                            let trafficRuleId = record['trafficRulePo']['id'];
                            let userId = record['id'];
                            let username = record['username'];

                            return <Link key={record.id} href={"#userModule#userTrafficRuleAssign"}
                                         data={{
                                             'trafficRuleId': trafficRuleId, 'userId': userId
                                         }}
                                         menuName={username + '用户流量分配'}
                            >{trafficRuleName}</Link>
                        }}
                />*/}
                    <Column align={"center"} title="流量规则" dataIndex={'trafficRuleName'}
                            render={(text, record, index) => {
                                let trafficRuleName = record['trafficRuleName'];
                                if (!trafficRuleName) {
                                    trafficRuleName = '--'
                                }

                                let trafficRuleId = record['trafficRuleId'];
                                let userId = record['id'];
                                let username = record['username'];

                                return <Link key={record.id} href={"#userModule#userTrafficRuleAssignModule"}
                                             data={{
                                                 'trafficRuleId': trafficRuleId, 'userId': userId
                                             }}
                                             menuName={username + '用户流量分配'}
                                >{trafficRuleName}</Link>
                            }}
                    />
                    <Column align={"center"} title="组" dataIndex="groupName"
                            render={(text, record, index) => {
                                let groupName = record['groupName'];
                                if (!groupName) {
                                    groupName = '--'
                                }

                                let userId = record['id'];
                                let groupId = record['groupId'];
                                let username = record['username'];

                                return <Link key={record.id} href={"#userModule#userGroupAssignModule"}
                                             data={{
                                                 'userId': userId, 'groupId': groupId
                                             }}
                                             menuName={username + '组分配'}
                                >{groupName}</Link>
                            }}
                    />
                </TableWrapper>
                <UserAddModal ref={this.addModalRef} userModalRef={this} handleAddOk={() => {
                    return this.props.handleAddOk.call(this);
                }} handleAutoGenerateUserChange={(checked) => {
                    return this.props.handleAutoGenerateUserChange.call(this, checked);
                }}/>
                <UserModifyModal ref={this.modifyModalRef} userModalRef={this} handleModifyOk={() => {
                    return this.props.handleModifyOk.call(this);
                }}/>
                <UserDelModal ref={this.delModalRef} handleDelOk={() => {
                    return this.props.handleDelOk.call(this);
                }}/>
            </div>
        )
    }
}

export default UserModule;
