package com.cn.xiang.fsockmanageweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cn.xiang.fsockmanageweb.po.UserPo;
import com.cn.xiang.fsockmanageweb.vo.PageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2024/2/5 12:14
 */
@Mapper
public interface IUserMapper extends BaseMapper<UserPo> {

    public UserPo findUserById1(@Param("id") String id);

    /**
     * 根据组名和用户名，查找用户
     *
     * @param groupName
     * @param username
     * @return
     */
    public UserPo findUserByGroupNameAndUsername(@Param("groupName") String groupName, @Param("username") String username);

    /**
     * 根据组名查找用户列表
     *
     * @param groupName
     * @return
     */
    List<UserPo> findUsersByGroupName(@Param("groupName") String groupName);

    List<Map<String, String>> findPages(@Param("pageVo") PageVo pageVo);

    long findTotalBySearch(@Param("pageVo")PageVo pageVo);

    List<Map<String, String>> findDeliverPages(@Param("pageVo") PageVo pageVo);
}
