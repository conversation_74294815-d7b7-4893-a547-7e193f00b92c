package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.po.SysUserPo;
import com.cn.xiang.fsockmanageweb.services.ISysUserService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import com.cn.xiang.fsockmanageweb.vo.UserVo;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api_v1/sysUser")
public class SysUserController extends ControllerSupport {
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        List<SysUserPo> list = sysUserService.list();

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.POST)
    public Object add(@RequestBody @Validated(value = {UserVo.AddGroup.class}) UserVo userVo) {
        SysUserPo sysUserPo = new SysUserPo();
        BeanUtils.copyProperties(userVo, sysUserPo);
        sysUserService.save(sysUserPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Object modify(@RequestBody @Validated(value = {UserVo.ModifyGroup.class}) UserVo userVo) {
        SysUserPo sysUserPo = new SysUserPo();
        BeanUtils.copyProperties(userVo, sysUserPo);
        sysUserService.updateById(sysUserPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public Object del(@Validated @NotEmpty String id) {
        sysUserService.removeById(id);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "isLogined", method = RequestMethod.GET)
    public Object isLogined() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        JSONResultMessage message = this.getJSONResultMessage();
        if (authentication instanceof UsernamePasswordAuthenticationToken) {
            message.addParameter("isLogined", authentication.isAuthenticated());
            message.success();
        } else {
            message.addParameter("isLogined", false);
            message.success();
        }

        return message;
    }

    @RequestMapping(value = "testAdd", method = RequestMethod.GET)
    public Object testAdd() {
        SysUserPo sysUserPo = new SysUserPo();
        sysUserPo.setUsername("admin");
        sysUserPo.setPassword(passwordEncoder.encode("admin"));
        sysUserPo.setStatus(true);
        sysUserService.save(sysUserPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }
}
