/**
 * 采用react toolkit query发送请求
 * @deprecated
 */
import axios from 'axios';
import devUrlMapping from '../utils/config/devUrlMapping';

let isDevelopment = process.env.NODE_ENV === 'development';
if (isDevelopment) {
    // axios.defaults.baseURL = 'testData';
    axios.defaults.baseURL = '/fsockManageWeb/api_v1';
} else {
    // axios.defaults.baseURL = 'http://localhost:8080/dev';
    axios.defaults.baseURL = '/fsockManageWeb/api_v1';
}

// 添加请求拦截器
axios.interceptors.request.use(function (config) {
    config = Object.assign({}, config, {
        timeout: 5000,
        // 开发环境时映射访问路径到/src/public/testData/下的json文件
        url: isDevelopment ? (() => {
            if (devUrlMapping[config.url]) {
                return devUrlMapping[config.url];
            } else {
                throw new Error('url[' + config.url + '] mapping is not found  in devUrlMapping.js');
            }
        })() : config.url
    });
    // 在发送请求之前做些什么
    return config;
}, function (error) {
    // 对请求错误做些什么
    return Promise.reject(error);
});

// 添加响应拦截器
axios.interceptors.response.use(function (response) {
    return response.data;
}, function (error) {
    window.global.Notification.open({
        type: window.global.Notification.TYPE.ERROR,
        message: 'request error',
        description: 'detail: ' + error.message
    });
    // 响应错误
    return Promise.reject(error);
});

export default axios;


