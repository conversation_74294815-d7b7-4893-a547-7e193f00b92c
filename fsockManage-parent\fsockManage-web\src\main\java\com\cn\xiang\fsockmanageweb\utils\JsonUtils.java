package com.cn.xiang.fsockmanageweb.utils;

import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * json工具
 *
 * <AUTHOR>
 * @date 2022年06月06日 下午5:18:15
 * @version 1.0
 *
 */
public class JsonUtils {
    private JsonUtils() {
        throw new IllegalAccessError("can not be instantiated");
    }

    /**
     * 获取ObjcetMapper
     *
     * @return
     */
    public static ObjectMapper getObjectMapper() {
        return ApplicationContextHolder.getBean(ObjectMapper.class);
    }

    /**
     * 创建ArrayNode
     */
    public static ArrayNode createJsonArrayNode() {
        return getObjectMapper().createArrayNode();
    }

    /**
     * 创建ObjectNode
     *
     * @return
     */
    public static ObjectNode createJsonObjectNode() {
        return getObjectMapper().createObjectNode();
    }

    /**
     * Bean转jsonNode
     *
     * @param obj
     * @return
     */
    public static JsonNode beanToJsonNode(Object obj) {
        return getObjectMapper().valueToTree(obj);
    }
}
