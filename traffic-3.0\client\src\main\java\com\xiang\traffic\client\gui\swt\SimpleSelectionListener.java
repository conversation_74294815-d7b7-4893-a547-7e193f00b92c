package com.xiang.traffic.client.gui.swt;

import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;

/**
 * SWT选择事件监听器 用于适应Lambda表达式
 *
 * <AUTHOR>
 * @date 2024/6/6 15:02
 */
@FunctionalInterface
interface SimpleSelectionListener extends SelectionListener {

    /**
     * @see SelectionListener#widgetSelected(SelectionEvent)
     */
    @Override
    void widgetSelected(SelectionEvent e);

    /**
     * @see SelectionListener#widgetDefaultSelected(SelectionEvent)
     */
    default void widgetDefaultSelected(SelectionEvent e) {
        // NOOP
    }
}
