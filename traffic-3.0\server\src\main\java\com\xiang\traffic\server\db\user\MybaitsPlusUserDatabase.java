package com.xiang.traffic.server.db.user;

import com.cn.xiang.mybaits.po.GroupPo;
import com.cn.xiang.mybaits.po.TrafficCapacityPo;
import com.cn.xiang.mybaits.po.TrafficRulePo;
import com.cn.xiang.mybaits.po.UserPo;
import com.cn.xiang.mybaits.services.IGroupService;
import com.cn.xiang.mybaits.services.ITrafficCapacityService;
import com.cn.xiang.mybaits.services.IUserAccessLogService;
import com.cn.xiang.mybaits.services.IUserService;
import com.cn.xiang.mybaits.utils.ApplicationContextHolder;
import com.xiang.traffic.AbstractConfig;
import com.xiang.traffic.ConfigInitializationException;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.server.db.vo.DbUserVo;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/5 15:02
 */
public class MybaitsPlusUserDatabase extends AbstractConfig implements UserDatabase {
    private IUserService userService;
    private IGroupService groupService;

    private IUserAccessLogService userAccessLogService;

    private ITrafficCapacityService groupTrafficCapacityService;

    private static final String NAME = "mybaitPlusUserDatabase";

    public MybaitsPlusUserDatabase(ConfigManager<?> configManager) {
        super(configManager, NAME);
    }

    public void initInternal() throws ConfigInitializationException {
        ApplicationContext context = ApplicationContextHolder.getInstance().getApplicationContext();
        this.userService = context.getBean(IUserService.class);
        this.groupService = context.getBean(IGroupService.class);
        this.groupTrafficCapacityService = context.getBean(ITrafficCapacityService.class);
        this.userAccessLogService = context.getBean(IUserAccessLogService.class);
    }

    @Override
    public boolean doAuth(String group, String username, String password) {
        UserPo user = this.userService.findUserByGroupNameAndUsername(group, username);
        if (user == null) {
            return false;
        }

        return user.isEnabled() && user.getPassword().equals(password);
    }

    @Override
    public boolean register(String group, DbUserVo userVo) {
        GroupPo groupPo = this.groupService.findGroupByName(group);
        if (groupPo == null) {
            return false;
        }
        if (this.userService.findUserByGroupNameAndUsername(group, userVo.getUsername()) != null) {
            return false;
        }

        UserPo userPo = new UserPo();
        userPo.setUsername(userVo.getUsername());
        userPo.setPassword(userVo.getPass());
        userPo.setGroupId(groupPo.getId());
        this.userService.save(userPo);

        return true;
    }

    @Override
    public boolean delete(String group, String username) {
        UserPo userPo = this.userService.findUserByGroupNameAndUsername(group, username);
        if (userPo != null) {
            this.userService.removeById(userPo);
            return true;
        }
        return false;
    }

    @Override
    public boolean changePassword(String group, String username, String newPassword) {
        UserPo userPo = this.userService.findUserByGroupNameAndUsername(group, username);
        if (userPo != null) {
            userPo.setPassword(newPassword);
            this.userService.updateById(userPo);
            return true;
        }

        return false;
    }

    @Override
    public UserGroup getUserGroup(String groupName) {
        GroupPo groupPo = this.groupService.findGroupByName(groupName);
        if (groupPo != null) {
            UserGroup userGroup = new TextUserDatabase.UserGroupImpl(groupName);
            //填充默认限流参数
            TrafficRulePo trafficRulePo = groupPo.getTrafficRulePo();
            if (trafficRulePo != null && trafficRulePo.isEnable()) {
                userGroup.setDefaultWriteLimit(trafficRulePo.getWriteLimit());
                userGroup.setDefaultReadLimit(trafficRulePo.getReadLimit());
            } else {
                userGroup.setDefaultReadLimit(0);
                userGroup.setDefaultWriteLimit(0);
            }

            List<UserPo> userPoList = this.userService.findUsersByGroupName(groupName);
            Map<String, DbUserVo> userVoMap = new HashMap<>();
            for (UserPo userPo : userPoList) {
                DbUserVo userVo = new DbUserVo();
                userVo.setId(userPo.getId());
                userVo.setGroup(groupName);
                userVo.setUsername(userPo.getUsername());
                userVo.setPass(userPo.getPassword());
                userVo.setEnabled(userPo.isEnabled());

                //填充限流参数
                trafficRulePo = userPo.getTrafficRulePo();
                if (trafficRulePo != null && trafficRulePo.isEnable()) {
                    userVo.setReadLimit(trafficRulePo.getReadLimit());
                    userVo.setWriteLimit(trafficRulePo.getWriteLimit());
                } else {
                    userVo.setWriteLimit(0);
                    userVo.setReadLimit(0);
                }

                userVoMap.put(userPo.getUsername(), userVo);
            }
            userGroup.setUserMap(userVoMap);
            return userGroup;
        }
        return null;
    }


    @Override
    public long getUserTrafficCapacity(String username) {
        TrafficCapacityPo trafficCapacityPo = this.groupTrafficCapacityService.findGroupTrafficCapacityByUsername(username);
        if (trafficCapacityPo != null) {
            return trafficCapacityPo.getTrafficCapacity();
        }
        return 0;
    }

    @Override
    public long getUserTrafficUsage(String username) {
        return this.userAccessLogService.getUserTrafficUsage(username);
    }

    @Override
    public long getUserTrafficRemainingCapacity(String username) {
        return this.getUserTrafficCapacity(username) - this.getUserTrafficUsage(username);
    }

}
