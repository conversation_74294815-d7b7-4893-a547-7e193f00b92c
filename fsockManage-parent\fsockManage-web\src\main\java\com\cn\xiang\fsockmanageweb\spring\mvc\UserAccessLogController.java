package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cn.xiang.fsockmanageweb.po.UserAccessLogPo;
import com.cn.xiang.fsockmanageweb.services.IUserAccessLogService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import com.cn.xiang.fsockmanageweb.vo.PageVo;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/api_v1/userAccessLog")
public class UserAccessLogController extends ControllerSupport {
    @Autowired
    private IUserAccessLogService userAccessLogService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list(PageVo pageVo) {
        PageDTO<UserAccessLogPo> pageDTO = new PageDTO<>(pageVo.getPage(), pageVo.getPageSize());
        Page<ObjectNode> list = userAccessLogService.findPagesWithUsername(pageDTO);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }
}
