package com.cn.xiang.mybaits.services.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.mybaits.mapper.IGroupMapper;
import com.cn.xiang.mybaits.mapper.IUserMapper;
import com.cn.xiang.mybaits.po.GroupPo;
import com.cn.xiang.mybaits.po.UserPo;
import com.cn.xiang.mybaits.services.IGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class GroupServiceImpl extends ServiceImpl<IGroupMapper, GroupPo> implements IGroupService {
    @Autowired
    private IUserMapper userMapper;

    public GroupPo findGroupByName(String groupName) {
        return baseMapper.findGroupByName(groupName);
    }

    public GroupPo findGroupByUserName(String username) {
        LambdaQueryWrapper<UserPo> wrapper = new LambdaQueryWrapper<UserPo>();
        wrapper.eq(UserPo::getUsername, username);
        UserPo userPo = userMapper.selectOne(wrapper);

        if (userPo != null) {
            return this.getById(userPo.getGroupId());
        }
        return null;
    }
}
