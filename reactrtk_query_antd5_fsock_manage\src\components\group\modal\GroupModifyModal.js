import React, {Component} from 'react';
import {Form, Input, Modal, Switch} from 'antd'

/**
 * 组修改弹窗
 */
class GroupModifyModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };


    render() {
        return <Modal
            title="修改"
            open={this.state.visible}
            onOk={this.props.handleModifyOk}
            onCancel={this.handleCancel}
            okText={'修改'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    hidden={true}
                    label="id"
                    name="id"
                    rules={[{required: true, message: 'Please input your id!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="组名"
                    name="name"
                    rules={[{required: true, message: 'Please input your group name!'}]}
                >
                    <Input/>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default GroupModifyModal;
