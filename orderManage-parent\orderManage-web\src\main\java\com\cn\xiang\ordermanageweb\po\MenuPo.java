package com.cn.xiang.ordermanageweb.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;

/**
 * <AUTHOR>
 * @date 2024/4/10 21:46
 */
@TableName(value = "tb_menu")
public class MenuPo extends BaseEntity {
    private String pid;
    private String type;
    private String name;
    private String iconType;
    private String href;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconType() {
        return iconType;
    }

    public void setIconType(String iconType) {
        this.iconType = iconType;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }
}
