package com.cn.xiang.ordermanageweb.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;

/**
 * flyingsocks库中tb_user类
 * <AUTHOR>
 * @date 2024/2/5 12:15
 */
@TableName(value = "tb_user", autoResultMap = true)
public class UserPo extends BaseEntity {
    private String username;

    private String password;

    private String groupId;

    private boolean enabled;
    private boolean delivered;

    private String expiration;
    private boolean noExpiration;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getExpiration() {
        return expiration;
    }

    public void setExpiration(String expiration) {
        this.expiration = expiration;
    }

    public boolean isNoExpiration() {
        return noExpiration;
    }

    public void setNoExpiration(boolean noExpiration) {
        this.noExpiration = noExpiration;
    }

    public boolean isDelivered() {
        return delivered;
    }

    public void setDelivered(boolean delivered) {
        this.delivered = delivered;
    }
}
