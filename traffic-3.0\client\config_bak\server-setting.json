[{"port": 1090, "auth": "user", "encrypt": "SSL", "host": "**************", "state": false, "auth-arg": {"pass": "$sdf2lxdjfklsaj1123dfs", "user": "xiang"}, "cert-port": 7060}, {"port": 1090, "auth": "user", "encrypt": "SSL", "host": "**************", "state": false, "auth-arg": {"pass": "$sdf2lxdjfklsaj1123dfs", "user": "xiang"}, "cert-port": 7060}, {"port": 1090, "auth": "user", "encrypt": "SSL", "host": "*************", "state": false, "auth-arg": {"pass": "$sdf2lxdjfklsaj1123dfs", "user": "xiang"}, "cert-port": 7060}, {"port": 1090, "auth": "user", "encrypt": "SSL", "host": "***************", "state": false, "auth-arg": {"pass": "$sdf2lxdjfklsaj1123dfs", "user": "xiang"}, "cert-port": 7060}, {"port": 1091, "auth": "user", "encrypt": "SSL", "host": "**************", "state": false, "auth-arg": {"pass": "$sdf2lxdjfklsaj1123dfs", "user": "xiang"}, "cert-port": 7061}]