package com.xiang.traffic.server.core.client;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.misc.MessageHeaderCheckHandler;
import com.xiang.traffic.protocol.AuthRequestMessage;
import com.xiang.traffic.protocol.AuthResponseMessage;
import com.xiang.traffic.protocol.SerializationException;
import com.xiang.traffic.server.Server;
import com.xiang.traffic.server.ServerConfig;
import com.xiang.traffic.server.core.ClientSession;
import com.xiang.traffic.server.db.user.UserDatabase;
import com.xiang.traffic.server.db.vo.DbUserVo;
import com.xiang.traffic.server.enumeration.ClientAuthType;
import com.xiang.traffic.server.enumeration.LimitStatusEnum;
import com.xiang.traffic.server.utils.Constants;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:19
 */
public class ProxyAuthenticationHandler extends ChannelInboundHandlerAdapter {

    public static final String HANDLER_NAME = "ProxyAuthenticationHandler";

    private static final String AUTH_REQUEST_FRAME_DECODER_NAME = "AuthRequestMessageFrameDecoder";

    private static final String IDLE_HANDLER_NAME = "AuthRequestIdleHandler";

    private static final String AUTH_REQUEST_HEADER_CHECKER_NAME = "AuthRequestHeaderChecker";

    public static final String TRAFFIC_SHAPING_HANDLER_NAME = "trafficShapingHandler";

    private static final MessageHeaderCheckHandler AUTH_REQUEST_HEADER_CHECKER = new MessageHeaderCheckHandler(AuthRequestMessage.getMessageHeader());

    private static final Logger log = LoggerFactory.getLogger(ProxyAuthenticationHandler.class);

    private ClientSession clientSession;

    private AbstractComponent<ClientProcessor> component;

    private static final int CHECK_INTERVAL = 1000;

    ProxyAuthenticationHandler(AbstractComponent<ClientProcessor> component) {
        super();
        this.component = component;
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) {
        ChannelPipeline cp = ctx.pipeline();
        cp.remove(AUTH_REQUEST_HEADER_CHECKER_NAME);
        cp.remove(IDLE_HANDLER_NAME);
        cp.remove(AUTH_REQUEST_FRAME_DECODER_NAME);
    }

    /**
     * IdleStateHandler -> [SslHandler] -> ClientSessionHandler -> FSMessageOutboundEncoder -> AuthRequestMessageFrameDecoder -> ProxyAuthenticationHandler
     */
    @Override
    public void handlerAdded(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        this.clientSession = ConnectionContext.clientSession(channel);

        ChannelPipeline cp = ctx.pipeline();
        cp.addFirst(IDLE_HANDLER_NAME, new IdleStateHandler(10, 0, 0));
        cp.addBefore(HANDLER_NAME, AUTH_REQUEST_FRAME_DECODER_NAME, new LengthFieldBasedFrameDecoder(Short.MAX_VALUE, AuthRequestMessage.LENGTH_OFFSET, AuthRequestMessage.LENGTH_SIZE, AuthRequestMessage.LENGTH_ADJUSTMENT, 0));
        cp.addBefore(AUTH_REQUEST_FRAME_DECODER_NAME, AUTH_REQUEST_HEADER_CHECKER_NAME, AUTH_REQUEST_HEADER_CHECKER);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof ByteBuf) {
            try {
                channelRead0(ctx, (ByteBuf) msg);
            } finally {
                ReferenceCountUtil.release(msg);
            }
        } else {
            ctx.fireChannelRead(msg);
        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        if (evt instanceof IdleStateEvent) {
            ctx.close();
            return;
        }

        ctx.fireUserEventTriggered(evt);
    }

    protected void channelRead0(ChannelHandlerContext ctx, ByteBuf buf) throws SerializationException {
        ClientSession session = this.clientSession;
        Predicate<AuthRequestMessage> authPredicate = ConnectionContext.authPredicate(ctx.channel());

        AuthRequestMessage authRequestMessage = new AuthRequestMessage(buf);
        boolean auth = authPredicate.test(authRequestMessage);
        if (!auth) {
            if (log.isTraceEnabled()) {
                log.trace("Auth failure, from client {}", ((SocketChannel) ctx.channel()).remoteAddress().getHostName());
            }

            AuthResponseMessage response = new AuthResponseMessage(false);
            ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
            return;
        }

        if (log.isTraceEnabled()) {
            log.trace("Auth success, from client {}", ((SocketChannel) ctx.channel()).remoteAddress().getHostName());
        }

        session.passAuth(this.component, authRequestMessage);

        AuthResponseMessage response = getSuccessAuthResponseMessage(authRequestMessage);
        ctx.write(response, ctx.voidPromise());


        ChannelPipeline cp = ctx.pipeline();
        cp.remove(this);

        //添加限流控制
        cp.addFirst(TRAFFIC_SHAPING_HANDLER_NAME, getTrafficShapingHandler(response));

        ChannelInboundHandler proxyHandler = ProxyHandlerDynamicProxyFactory.createProxyHandler(new ProxyHandler(component), component);
        cp.addLast(ProxyHandler.HANDLER_NAME, proxyHandler);

        ctx.flush();
    }

    /**
     * 获取限流控制
     *
     * @param response
     * @return
     */
    private ChannelTrafficShapingHandler getTrafficShapingHandler(AuthResponseMessage response) {
        int writeLimit = (int) response.getExtraData().get(Constants.WRITE_LIMIT_PARAM_NAME);
        int readLimit = (int) response.getExtraData().get(Constants.READ_LIMIT_PARAM_NAME);

        //读写参数是相对于客户端的，服务端这里刚好相反
        return new ChannelTrafficShapingHandler(readLimit * 1000L, writeLimit * 1000L, CHECK_INTERVAL);
    }

    /**
     * 添加限流信息到AuthRequestMessage，返回给客户端
     *
     * @param authRequestMessage
     * @return
     */
    private AuthResponseMessage getSuccessAuthResponseMessage(AuthRequestMessage authRequestMessage) {
        AuthResponseMessage response = new AuthResponseMessage(true);
        ServerConfig.Node node = component.getParentComponent().getParentComponent().getServerConfig();

        if (node.authType == ClientAuthType.SIMPLE) {
            response.getExtraData().put(Constants.READ_LIMIT_PARAM_NAME, node.readLimit);
            response.getExtraData().put(Constants.WRITE_LIMIT_PARAM_NAME, node.writeLimit);
        } else if (node.authType == ClientAuthType.USER) {
            String group = node.getArgument(Constants.GROUP_PARAM_NAME);

            Server server = component.getParentComponent().getParentComponent().getParentComponent();
            UserDatabase userDatabase = server.getUserDatabase();
            UserDatabase.UserGroup userGroup = userDatabase.getUserGroup(group);

            String username = authRequestMessage.getParameter(Constants.USER_PARAM_NAME);
            DbUserVo userVo = userGroup.getUserMap().get(username);

            response.getExtraData().put(Constants.READ_LIMIT_PARAM_NAME, 0);
            response.getExtraData().put(Constants.WRITE_LIMIT_PARAM_NAME, 0);
            if (userVo.getReadLimit() == LimitStatusEnum.NOT_SET.getVaule()) {
                if (userGroup.getDefaultReadLimit() != LimitStatusEnum.NOT_SET.getVaule()) {
                    response.getExtraData().put(Constants.READ_LIMIT_PARAM_NAME, userGroup.getDefaultReadLimit());
                }
            } else {
                response.getExtraData().put(Constants.READ_LIMIT_PARAM_NAME, userVo.getReadLimit());
            }

            if (userVo.getWriteLimit() == LimitStatusEnum.NOT_SET.getVaule()) {
                if (userGroup.getDefaultWriteLimit() != LimitStatusEnum.NOT_SET.getVaule()) {
                    response.getExtraData().put(Constants.WRITE_LIMIT_PARAM_NAME, userGroup.getDefaultWriteLimit());
                }
            } else {
                response.getExtraData().put(Constants.WRITE_LIMIT_PARAM_NAME, userVo.getWriteLimit());
            }
        }

        return response;
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        if (cause instanceof SerializationException) {
            log.info("An exception occur when deserialize AuthRequestMessage", cause);
            ctx.close();
            return;
        }

        log.warn("An exception occur in ProxyAuthenticationHandler", cause);
    }
}
