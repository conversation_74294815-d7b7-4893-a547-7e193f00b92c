server:
  port: 8090
spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: @package.environment@
  servlet:
    multipart:
      max-file-size: 1000MB
      max-request-size: 1000MB
  thymeleaf:
    cache: false
    mode: HTML
    encoding: UTF-8
    servlet:
      content-type: text/html
    prefix: classpath:templates/
mybatis-plus:
  #mapper xml文件路径
  mapper-locations: classpath:com/cn/xiang/ordermanageweb/mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      logic-delete-value: 1 # 逻辑已删除值(默认为 true)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 false)
    banner: false



