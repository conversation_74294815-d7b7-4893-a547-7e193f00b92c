const url = 'product';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getProductListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'GET',
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        getProductById: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/findById',
                    method: 'GET',
                    params: {
                        productId: queryArg.productId
                    }
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
