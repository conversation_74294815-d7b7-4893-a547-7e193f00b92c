import {connect} from 'react-redux'
import LineChart from '../../../../../components/echarts/lineChart';

const mapStateToProps = (state, ownProps) => {
    //这里主要做redux状态status与组件prop的适配，如果store变化不导致组件更新的话，直接返回ownProps即可。
    return {
        data: state.lineChart.data
    };
};
const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {}
};

const Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(LineChart);

export default Container;