package com.cn.xiang.fsockmanageweb.vo;


import jakarta.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/4/8 13:51
 */
public class GroupVo {
    @NotEmpty(groups = {ModifyGroup.class})
    private String id;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public interface AddGroup {
    }

    public interface ModifyGroup {
    }
}
