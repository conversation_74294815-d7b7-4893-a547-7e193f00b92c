package com.xiang.traffic.misc;

import com.xiang.traffic.protocol.Message;
import com.xiang.traffic.protocol.SerializationException;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelOutboundHandlerAdapter;
import io.netty.channel.ChannelPromise;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ChannelHandler.Sharable
public class FSMessageOutboundEncoder extends ChannelOutboundHandlerAdapter {
    private static final Logger log = LoggerFactory.getLogger("FSMessageOutboundEncoder");

    public static final String HANDLER_NAME = "FSMessageOutboundEncoder";

    public static final FSMessageOutboundEncoder INSTANCE = new FSMessageOutboundEncoder();

    private FSMessageOutboundEncoder() {
        super();
    }

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) {
        if (msg instanceof Message) {
            Message message = (Message) msg;

            try {
                ByteBuf buf = message.serialize(ctx.alloc());
                ctx.write(buf, promise);
            } catch (SerializationException e) {
                log.error("Serialize exception occur, with type: {}", message.getClass().getName(), e);
            }
        } else {
            ctx.write(msg, promise);
        }
    }

    @Override
    public boolean isSharable() {
        return true;
    }
}
