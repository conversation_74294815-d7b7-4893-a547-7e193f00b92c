package com.cn.xiang.fsockmanageweb.vo;


import jakarta.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/4/8 13:51
 */
public class UserVo {
    @NotEmpty(groups = {ModifyGroup.class})
    private String id;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String username;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String password;
    private boolean enabled;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String expiration;

    private String groupName;

    private boolean noExpiration;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }


    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getExpiration() {
        return expiration;
    }

    public void setExpiration(String expiration) {
        this.expiration = expiration;
    }

    public interface AddGroup {
    }

    public interface ModifyGroup {
    }

    public boolean isNoExpiration() {
        return noExpiration;
    }

    public void setNoExpiration(boolean noExpiration) {
        this.noExpiration = noExpiration;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
}
