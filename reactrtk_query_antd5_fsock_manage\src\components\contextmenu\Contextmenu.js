import React, {Component} from 'react'
import './index.less'

class Contextmenu extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        let dataList = this.props.dataList || [];
        dataList = dataList.map((item, index) => {
            return <li key={index} onClick={item.onClick}>
                {item.icon}
                <span>{item.label}</span>
            </li>
        })

        return (
            <ul style={{
                left: this.props.left + 'px',
                display: this.props.visible ? 'block' : 'none',
                top: this.props.top + 'px'
            }} className="contextmenu" onMouseLeave={this.props.onMouseLeave}>
                {dataList}
            </ul>
        )
    }
}

export default Contextmenu
