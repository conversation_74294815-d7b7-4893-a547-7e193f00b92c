[{"name": "com.xiang.traffic.client.proxy.transparent.LinuxNative", "methods": [{"name": "getTargetAddress0", "parameterTypes": ["int"]}]}, {"name": "java.net.InetSocketAddress", "methods": [{"name": "<init>", "parameterTypes": ["java.net.InetAddress", "int"]}, {"name": "<init>", "parameterTypes": ["java.lang.String", "int"]}]}, {"name": "java.net.InetAddress", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["byte[]"]}, {"name": "getByName", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.System", "methods": [{"name": "loadLibrary", "parameterTypes": ["java.lang.String"]}, {"name": "load", "parameterTypes": ["java.lang.String"]}]}]