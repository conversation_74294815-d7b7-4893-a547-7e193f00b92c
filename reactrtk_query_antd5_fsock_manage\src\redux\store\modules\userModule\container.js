import UserModule from './index';
import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getUserListData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData1: data.list.records,
            total: data.list.total
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();
let initData = function (search) {
    this.searchInputFormRef.current.setFieldsValue({
        search: search
    });

    window.setTimeout(() => {
        let inputEle = this.searchInput.current.input;
        let parentEle = inputEle.parentElement;
        let buttonEle = parentEle.querySelector('.ant-input-group-addon button');
        buttonEle.click();
    }, 100);
};

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期

            // const {unsubscribe} = dispatch(baseApi.endpoints.getUserListData.initiate({
            //     page: 1,
            //     pageSize: 10,
            //     search: ''
            // }, {
            //     //subscribe属性可以配置不把数据存储到store
            //     // subscribe: false,
            //     forceRefetch: true
            // }));
            // apiUnsubscribeSet.add(unsubscribe);
            let search = moduleParam.search || '';
            initData.call(this, search);

        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
            // this.setState({page: 1, pageSize: 10});
            // const {unsubscribe} = dispatch(baseApi.endpoints.getUserListData.initiate({
            //     page: 1,
            //     pageSize: 10,
            //     search: ''
            // }, {
            //     //subscribe属性可以配置不把数据存储到store
            //     // subscribe: false,
            //     forceRefetch: true
            // }));
            // apiUnsubscribeSet.add(unsubscribe);
            let search = moduleParam.search || '';
            initData.call(this, search);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData1 !== this.props.tableData1) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData1);
            }
        },
        loadData: function (page, pageSize) {
            pageSize = pageSize ?? 10;
            page = page ?? 1;
            this.setState({
                page: page,
                pageSize: pageSize
            })
            dispatch(baseApi.endpoints.getUserListData.initiate({
                page: page,
                pageSize: pageSize,
                search: ''
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
        },
        inputOnSearch: function (value, event) {
            this.setState({page: 1, pageSize: 10});
            dispatch(baseApi.endpoints.getUserListData.initiate({
                page: 1,
                pageSize: 10,
                search: value
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
        },
        randomString: function (len) {
            len = len || 10;
            let chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz123456789$@';

            let random;
            let str = '';
            for (let i = 0; i < len; i++) {
                random = Math.floor(Math.random() * Math.pow(10, 5)) % chars.length;
                str += chars[random];
            }
            return str;
        },
        randomUsername: async function () {
            let isExist = true;
            let randomUsername;
            while (isExist) {
                randomUsername = 'user_' + this.props.randomString(10);
                let response = await dispatch(baseApi.endpoints.postIsUserExist.initiate({username: randomUsername}, {
                    subscribe: false, forceRefetch: true,
                }));
                isExist = response.data.isUserExist;
            }

            return randomUsername;
        },
        handleAutoGenerateUserChange: function (checked) {
            if (checked === true) {
                let addModalInst = this.addModalRef.current;

                this.props.randomUsername.call(this).then((randomUsername) => {
                    addModalInst.formRef.current.setFieldsValue({
                        username: randomUsername,
                        password: 'passwd_' + this.props.randomString(11)
                    })
                });
            }
        },
        handleAddOk: function () {
            let this_ = this;
            let modalInst = this.addModalRef.current;
            let formInst = modalInst.formRef.current;
            let tableInst = this.tableRef;
            //校验表单
            formInst.validateFields()
                .then(values => {
                    let params = formInst.getFieldsValue();
                    params.expiration = params.expiration.format('YYYY-MM-DD HH:mm:ss');
                    // 校验通过
                    dispatch(baseApi.endpoints.postUserData.initiate(params, {
                        subscribe: false, forceRefetch: true,
                    })).then((resp) => {
                        let data = resp.data;

                        formInst.resetFields();

                        //关闭模态框
                        modalInst.setState({
                            visible: false
                        });
                        if(data.status){
                            //刷新表格
                            dispatch(baseApi.endpoints.getUserListData.initiate({
                                page: 1,
                                pageSize: 10,
                                search: ''
                            }, {
                                subscribe: false, forceRefetch: true
                            })).then((response) => {
                                this_.setState({page: 1, pageSize: 10});
                                let data = response.data.list.records;
                                tableInst.current._dataMethod_.refresh(data);

                                window.global.Notification.open({
                                    type: window.global.Notification.TYPE.SUCCESS,
                                    message: "添加用户成功.",
                                    delay: 3,
                                });
                            });
                        }else{
                            window.global.Notification.open({
                                type: window.global.Notification.TYPE.ERROR,
                                message: data.errorMsg,
                                delay: 3,
                            });
                        }
                    });
                })
        },
        handleModifyOk: function () {
            let modalInst = this.modifyModalRef.current;
            let formInst = modalInst.formRef.current;
            let tableInst = this.tableRef;
            let this_ = this;
            //校验表单
            formInst.validateFields()
                .then(values => {
                    let params = formInst.getFieldsValue();
                    params.expiration = params.expiration.format('YYYY-MM-DD HH:mm:ss');
                    // 校验通过
                    dispatch(baseApi.endpoints.putUserData.initiate(params, {
                        subscribe: false, forceRefetch: true,
                    })).then(() => {
                        formInst.resetFields();

                        //关闭模态框
                        modalInst.setState({
                            visible: false
                        });


                        //刷新表格
                        dispatch(baseApi.endpoints.getUserListData.initiate({
                            page: 1,
                            pageSize: 10,
                            search: ''
                        }, {
                            subscribe: false, forceRefetch: true
                        })).then((response) => {
                            this_.setState({page: 1, pageSize: 10});
                            let data = response.data.list.records;
                            tableInst.current._dataMethod_.refresh(data);

                            //清空选项
                            this_.setState({
                                selectedRowKeys: []
                            });

                            window.global.Notification.open({
                                type: window.global.Notification.TYPE.SUCCESS,
                                message: "修改用户成功.",
                                delay: 3,
                            });
                        });
                    });
                })
                .catch(info => {
                    console.log('Validate Failed:', info);
                });
        },
        handleDelOk: function () {
            let this_ = this;
            let tableInst = this.tableRef;
            let selectedId = this.selectedRows[0].id;
            dispatch(baseApi.endpoints.delUserData.initiate({id: selectedId}, {
                subscribe: false, forceRefetch: true,
            })).then(() => {
                //隐藏弹窗
                let delModalInst = this.delModalRef.current;
                delModalInst.setState({
                    visible: false
                });

                //刷新表格
                dispatch(baseApi.endpoints.getUserListData.initiate({
                    page: 1,
                    pageSize: 10,
                    search: ''
                }, {
                    subscribe: false, forceRefetch: true
                })).then((response) => {
                    this_.setState({page: 1, pageSize: 10});
                    let data = response.data.list.records;
                    tableInst.current._dataMethod_.refresh(data);

                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "删除用户成功.",
                        delay: 3,
                    });
                });
            });
        }
    };
};

let Container = ModuleContainerCreator.create(UserModule, mapStateToProps, mapDispatchToProps);
export default Container;
