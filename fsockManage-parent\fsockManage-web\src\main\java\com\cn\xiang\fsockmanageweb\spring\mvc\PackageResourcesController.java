package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cn.xiang.fsockmanageweb.po.PackageResourcesPo;
import com.cn.xiang.fsockmanageweb.services.IPackageResourcesService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import com.cn.xiang.fsockmanageweb.vo.PackageResourcesVo;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@RestController
@RequestMapping("/api_v1/packageResources")
public class PackageResourcesController extends ControllerSupport {
    @Autowired
    private IPackageResourcesService packageResourcesService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        LambdaQueryWrapper<PackageResourcesPo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<PackageResourcesPo> list = packageResourcesService.list(lambdaQueryWrapper.eq(PackageResourcesPo::isUploaded,
                true));

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public Object upload(@RequestParam("file") MultipartFile file) {
        String id = packageResourcesService.upload(file);
        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("id", id);
        return message;
    }

    @RequestMapping(value = "saveUpload", method = RequestMethod.POST)
    public Object saveUpload(@RequestBody @Validated PackageResourcesVo packageResourcesVo) {
        packageResourcesService.saveUpload(packageResourcesVo);
        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public Object del(@Validated @NotEmpty String id) {
        packageResourcesService.removeUploadPackageResources(id);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }
}
