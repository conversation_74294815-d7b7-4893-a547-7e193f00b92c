package com.cn.xiang.mybaits.services.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.mybaits.mapper.ITrafficCapacityMapper;
import com.cn.xiang.mybaits.po.GroupPo;
import com.cn.xiang.mybaits.po.TrafficCapacityPo;
import com.cn.xiang.mybaits.services.IGroupService;
import com.cn.xiang.mybaits.services.ITrafficCapacityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class TrafficCapacityServiceImpl extends ServiceImpl<ITrafficCapacityMapper, TrafficCapacityPo> implements ITrafficCapacityService {

    @Autowired
    private IGroupService groupService;

    @Override
    public TrafficCapacityPo findGroupTrafficCapacityByUsername(String username) {
        GroupPo groupPo = groupService.findGroupByUserName(username);
        if (groupPo != null) {
            return groupPo.getTrafficCapacityPo();
        }
        return null;
    }

}
