package com.cn.xiang.fsockmanageweb.vo;


import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/4/8 13:51
 */
public class TrafficRuleVo {
    @NotEmpty(groups = {ModifyGroup.class})
    private String id;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String name;
    @Min(groups = {AddGroup.class, ModifyGroup.class}, value = 0L)
    private long dailyLimit;
    @Min(groups = {AddGroup.class, ModifyGroup.class}, value = 0L)
    private long weeklyLimit;
    @Min(groups = {AddGroup.class, ModifyGroup.class}, value = 0L)
    private long monthlyLimit;
    @Min(groups = {AddGroup.class, ModifyGroup.class}, value = 0L)
    private int writeLimit;
    @Min(groups = {AddGroup.class, ModifyGroup.class}, value = 0L)
    private int readLimit;
    private boolean enable;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getDailyLimit() {
        return dailyLimit;
    }

    public void setDailyLimit(long dailyLimit) {
        this.dailyLimit = dailyLimit;
    }

    public long getWeeklyLimit() {
        return weeklyLimit;
    }

    public void setWeeklyLimit(long weeklyLimit) {
        this.weeklyLimit = weeklyLimit;
    }

    public long getMonthlyLimit() {
        return monthlyLimit;
    }

    public void setMonthlyLimit(long monthlyLimit) {
        this.monthlyLimit = monthlyLimit;
    }

    public int getWriteLimit() {
        return writeLimit;
    }

    public void setWriteLimit(int writeLimit) {
        this.writeLimit = writeLimit;
    }

    public int getReadLimit() {
        return readLimit;
    }

    public void setReadLimit(int readLimit) {
        this.readLimit = readLimit;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }


    public interface AddGroup {
    }

    public interface ModifyGroup {
    }
}
