package com.cn.xiang.mybaits.services.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.mybaits.mapper.IUserAccessLogMapper;
import com.cn.xiang.mybaits.po.UserAccessLogPo;
import com.cn.xiang.mybaits.services.IUserAccessLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/2/5 23:13
 */
@Service
@Transactional
public class UserAccessLogServiceImpl extends ServiceImpl<IUserAccessLogMapper, UserAccessLogPo> implements IUserAccessLogService {
    private static final int BYTE_TO_MEGABYTE = 1024;

    @Override
    public String userEnter(String userId, String address) {
        UserAccessLogPo userAccessLogPo = new UserAccessLogPo();
        userAccessLogPo.setUserId(userId);
        userAccessLogPo.setAddress(address);
        userAccessLogPo.setConnectTime(LocalDateTime.now());

        save(userAccessLogPo);
        return userAccessLogPo.getId();
    }

    @Override
    public void userLeave(String accessLogId, long uploadTrafficUsage, long downloadTrafficUsage) {
        UserAccessLogPo userAccessLog = getById(accessLogId);
        userAccessLog.setDisconnectTime(LocalDateTime.now());
        userAccessLog.setUploadTrafficUsage(uploadTrafficUsage);
        userAccessLog.setUploadTrafficUsageHuman(uploadTrafficUsage / BYTE_TO_MEGABYTE / BYTE_TO_MEGABYTE + "MB");
        userAccessLog.setDownloadTrafficUsage(downloadTrafficUsage);
        userAccessLog.setDownloadTrafficUsageHuman(downloadTrafficUsage / BYTE_TO_MEGABYTE / BYTE_TO_MEGABYTE + "MB");

        updateById(userAccessLog);
    }

    @Override
    public long getUserTrafficUsage(String username) {
        return this.baseMapper.getUserTrafficUsage(username);
    }
}
