package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.mapper.IOrderSysUserMapper;
import com.cn.xiang.fsockmanageweb.po.OrderSysUserPo;
import com.cn.xiang.fsockmanageweb.services.IOrderSysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
@DS("order")
public class OrderSysUserServiceImpl extends ServiceImpl<IOrderSysUserMapper, OrderSysUserPo> implements IOrderSysUserService {
}
