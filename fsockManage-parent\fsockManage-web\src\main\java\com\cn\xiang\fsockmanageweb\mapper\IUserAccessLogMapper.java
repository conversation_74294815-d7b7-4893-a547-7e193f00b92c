package com.cn.xiang.fsockmanageweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cn.xiang.fsockmanageweb.po.UserAccessLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2024/2/5 23:11
 */
@Mapper
public interface IUserAccessLogMapper extends BaseMapper<UserAccessLogPo> {

    @Select("select sum(download_traffic_usage) from tb_user_access_log as userAccessLog, tb_user as user where userAccessLog.user_id = user.id and user.username = #{username}")
    public long getUserDownloadTrafficUsage(@Param("username") String username);

    public String findUserTrafficUsage(@Param("userId") String userId, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
