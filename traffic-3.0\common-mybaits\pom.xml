<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>traffic-parent</artifactId>
        <groupId>com.xiang</groupId>
        <version>3.1-SNAPSHOT</version>
    </parent>
    <groupId>com.cn.xiang</groupId>
    <artifactId>traffic-mybaits</artifactId>
    <packaging>jar</packaging>

    <modelVersion>4.0.0</modelVersion>

    <properties>
        <!--   <spring.version>5.3.31</spring.version>
           <mybaits.version>3.5.15</mybaits.version>
           <mybaits-plus.version>3.5.5</mybaits-plus.version>
           <mysql.version>8.0.33</mysql.version>
           <mybaits-spring.version>2.0.7</mybaits-spring.version>
           <slf4j.version>1.7.36</slf4j.version>
           <druid.version>1.2.21</druid.version>-->
        <mybatis-plus-boot-starter.version>3.5.5</mybatis-plus-boot-starter.version>
        <mybatis-spring.version>2.0.7</mybatis-spring.version>
        <mybatis.version>3.5.10</mybatis.version>
        <druid.version>1.2.21</druid.version>
        <mysql-connector-java.version>8.0.30</mysql-connector-java.version>
        <aspectjweaver.version>1.9.1</aspectjweaver.version>
        <logbak.version>1.3.0</logbak.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectjweaver.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${mybatis-spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>


        <!--  &lt;!&ndash; spring的核心依赖 &ndash;&gt;
          <dependency>
              <groupId>org.springframework</groupId>
              <artifactId>spring-context</artifactId>
              <version>${spring.version}</version>
          </dependency>
          &lt;!&ndash; spring的切面 &ndash;&gt;
          <dependency>
              <groupId>org.springframework</groupId>
              <artifactId>spring-aspects</artifactId>
              <version>${spring.version}</version>
          </dependency>
          &lt;!&ndash; 对数据访问层的支持 &ndash;&gt;
          <dependency>
              <groupId>org.springframework</groupId>
              <artifactId>spring-jdbc</artifactId>
              <version>${spring.version}</version>
          </dependency>
          <dependency>
              <groupId>org.mybatis</groupId>
              <artifactId>mybatis</artifactId>
              <version>${mybaits.version}</version>
          </dependency>
          &lt;!&ndash; mybatis依赖 &ndash;&gt;
          <dependency>
              <groupId>com.baomidou</groupId>
              <artifactId>mybatis-plus</artifactId>
              <version>${mybaits-plus.version}</version>
          </dependency>
          &lt;!&ndash; 数据库驱动 &ndash;&gt;
          <dependency>
              <groupId>mysql</groupId>
              <artifactId>mysql-connector-java</artifactId>
              <version>${mysql.version}</version>
          </dependency>
          &lt;!&ndash;数据源&ndash;&gt;
          <dependency>
              <groupId>com.alibaba</groupId>
              <artifactId>druid</artifactId>
              <version>${druid.version}</version>
          </dependency>
          &lt;!&ndash; spring整合mybatis单独的jar包 &ndash;&gt;
          <dependency>
              <groupId>org.mybatis</groupId>
              <artifactId>mybatis-spring</artifactId>
              <version>${mybaits-spring.version}</version>
              <exclusions>
                  <exclusion>
                      <groupId>org.mybatis</groupId>
                      <artifactId>mybatis</artifactId>
                  </exclusion>
              </exclusions>
          </dependency>
          &lt;!&ndash; 日志 &ndash;&gt;
          <dependency>
              <groupId>org.slf4j</groupId>
              <artifactId>slf4j-log4j12</artifactId>
          </dependency>
          <dependency>
              <groupId>org.slf4j</groupId>
              <artifactId>slf4j-api</artifactId>
          </dependency>-->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.7.18</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
