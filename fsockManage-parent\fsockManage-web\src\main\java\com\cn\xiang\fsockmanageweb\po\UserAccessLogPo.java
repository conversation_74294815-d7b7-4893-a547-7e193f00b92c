package com.cn.xiang.fsockmanageweb.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;

import java.time.LocalDateTime;

/**
 *
 */
@TableName("tb_user_access_log")
public class UserAccessLogPo extends BaseEntity {
    private String userId;

    private String address;

    private Long uploadTrafficUsage;

    private String uploadTrafficUsageHuman;

    private Long downloadTrafficUsage;
    private String downloadTrafficUsageHuman;

    private LocalDateTime connectTime;

    private LocalDateTime disconnectTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getUploadTrafficUsage() {
        return uploadTrafficUsage;
    }

    public void setUploadTrafficUsage(Long uploadTrafficUsage) {
        this.uploadTrafficUsage = uploadTrafficUsage;
    }

    public Long getDownloadTrafficUsage() {
        return downloadTrafficUsage;
    }

    public void setDownloadTrafficUsage(Long downloadTrafficUsage) {
        this.downloadTrafficUsage = downloadTrafficUsage;
    }

    public LocalDateTime getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(LocalDateTime connectTime) {
        this.connectTime = connectTime;
    }

    public LocalDateTime getDisconnectTime() {
        return disconnectTime;
    }

    public void setDisconnectTime(LocalDateTime disconnectTime) {
        this.disconnectTime = disconnectTime;
    }

    public String getUploadTrafficUsageHuman() {
        return uploadTrafficUsageHuman;
    }

    public void setUploadTrafficUsageHuman(String uploadTrafficUsageHuman) {
        this.uploadTrafficUsageHuman = uploadTrafficUsageHuman;
    }

    public String getDownloadTrafficUsageHuman() {
        return downloadTrafficUsageHuman;
    }

    public void setDownloadTrafficUsageHuman(String downloadTrafficUsageHuman) {
        this.downloadTrafficUsageHuman = downloadTrafficUsageHuman;
    }
}
