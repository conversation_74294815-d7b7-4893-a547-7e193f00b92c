import Utils from '../../../../core/utils/utils';
import {baseApi} from "../../../../redux/store/request/services/base/base"
import {connect} from "react-redux";
import PasswordLoginForm from "./passwordLoginForm";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('postSysUserLogin', baseApi, state);
    const {isSuccess} = result;
    if (isSuccess) {
        return {};
    }
    return ownProps;
};


const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        onLoginFormSuccess: function (values) {
            const promise = dispatch(baseApi.endpoints.postSysUserLogin.initiate(values, {
                //subscribe属性可以配置不把数据存储到store
                subscribe: false,
                forceRefetch: true
            }));
            promise.then((res) => {
                //重置表单
                let formInst = this.formRef.current;
                formInst.resetFields();
                if (res.data.status) {
                    this.props.loginSuccessCallback && this.props.loginSuccessCallback();
                } else {
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: res.data.errorMsg,
                        delay: 3,
                    });
                }
            });
        },
    };
};

let Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(PasswordLoginForm);
export default Container;
