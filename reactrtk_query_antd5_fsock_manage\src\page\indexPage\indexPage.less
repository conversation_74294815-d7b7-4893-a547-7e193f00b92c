.index-layout-ctn {
  height: 100%;
  /*min-height: 920px;*/
  min-height: 100%;
  /**
    layout beg
   */

  section.ant-layout {
    height: calc(100% - 64px);

    section.ant-layout {
      main {
        overflow-y: scroll;
        background-color: white;
        padding: 24px;
      }
    }
  }

  /**
      layout end
    */

  .ant-layout {
    .ant-layout {
      padding: 24px;
    }
  }
}

/**全局滚动条样式
 scrollbar beg
 */

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  height: 1px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(97, 184, 179, 0.1);
  background: #78b4b4;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(87, 175, 187, 0.1);
  border-radius: 10px;
  background: #ededed;
}

/**
scrollbar end
 */

/**
header beg
 */

.header.ant-layout-header {
  display: inline-flex;
  padding-right: 0;
  /**
  logo beg
   */

  .logo {
    width: 122px;
    height: 31px;
    background: rgba(255, 255, 255, 0.2);
    margin: 16px 28px 16px 0;
    float: left;
  }

  /**
logo end
 */

  /**
  user image beg
   */

  .user-image-ctn {
    display: inline-flex;
    flex-direction: row-reverse;
    flex-grow: 100;

    .ant-avatar-circle {
      margin-top: 6px;
      margin-right: 35px;
    }
  }

  /**
  user image end
   */
}

/**
header end
 */


