package com.cn.xiang.ordermanageweb.services.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.ordermanageweb.mapper.IDeliverMapper;
import com.cn.xiang.ordermanageweb.po.*;
import com.cn.xiang.ordermanageweb.services.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class DeliverServiceImpl extends ServiceImpl<IDeliverMapper, DeliverPo> implements IDeliverService {
    @Autowired
    private IOrdersService ordersService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IOrderItemsService orderItemsService;

    @Autowired
    private IProductsService productsService;

    @Override
    public Map<String, String> deliverDetail(String orderId) {
        //order库
        OrdersPo ordersPo = ordersService.getById(orderId);
        SysUserPo sysUserPo = sysUserService.getById(ordersPo.getUserId());
        OrderItemsPo orderItemsPo = orderItemsService.getOne(new LambdaQueryWrapper<OrderItemsPo>().eq(OrderItemsPo::getOrderId, orderId));
        ProductsPo productsPo = productsService.getById(orderItemsPo.getProductId());
        DeliverPo deliverPo = this.getOne(new LambdaQueryWrapper<DeliverPo>().eq(DeliverPo::getOrdersPo, ordersPo.getId()));

        //校验用户
        Principal principal = SecurityContextHolder.getContext().getAuthentication();
        String username = principal.getName();
        if (!username.equals(sysUserPo.getUsername())) {
            throw new RuntimeException("用户信息校验失败");
        }

        Map<String, String> result = new HashMap<>();
        result.put("orderId", orderId);
        result.put("productName", productsPo.getName());
        result.put("price", orderItemsPo.getPrice() + "");
        result.put("quantity", orderItemsPo.getQuantity() + "");

        result.put("totalPrice", ordersPo.getTotalAmount() + "");

        result.put("buyer", sysUserPo.getUsername());
        result.put("username", deliverPo.getUserPo().getUsername());
        result.put("password", deliverPo.getUserPo().getPassword());
        result.put("userExpiration", deliverPo.getUserPo().getExpiration());
        result.put("serverIp", deliverPo.getServerPo().getIp());
        result.put("serverPort", deliverPo.getServerPo().getPort());
        result.put("serverCertPort", deliverPo.getServerPo().getCertPort());
        result.put("orderStatus", ordersPo.getStatus());

        return result;
    }
}
