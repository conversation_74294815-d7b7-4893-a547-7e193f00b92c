import {createSlice} from "@reduxjs/toolkit";
import Utils from "../../../core/utils/utils";

/**
 * selectedSubredditSlice
 *
 */
const selectedSubredditInitState = '';

const selectedSubredditSlice = createSlice({
    name: 'selectedSubredditSlice',
    initialState: selectedSubredditInitState,
    reducers: {
        selectSubreddit: (state = selectedSubredditInitState, action = {}) => {
            return action.payload;
        }
    },
});

/**
 * fetchBySubredditSlice
 *
 */
const fetchBySubredditInitState = {};
/**
 *初始化Subreddit状态
 * @param state
 * @param action
 */
const initSubredditState = (state, action) => {
    !state[action.payload.subreddit] &&
    (state[action.payload.subreddit] = {
        isFetching: false,
        didInvalidate: false,
        data: {},
        callbackFetch: false
    });
}

const fetchBySubredditSlice = createSlice({
    name: 'fetchBySubredditSlice',
    initialState: fetchBySubredditInitState,
    reducers: {
        invalidateSubreddit: (state = fetchBySubredditInitState, action = {}) => {
            initSubredditState(state, action);

            state[action.payload.subreddit].didInvalidate = true;
        },
        receiveFetch: (state = fetchBySubredditInitState, action = {}) => {
            initSubredditState(state, action);

            state[action.payload.subreddit].isFetching = false;
            state[action.payload.subreddit].didInvalidate = false;
            state[action.payload.subreddit].data = Utils.deepClone(action.payload.posts);
            state[action.payload.subreddit].lastUpdated = action.payload.receivedAt;
        },
        receiveCallbackFetch: (state = fetchBySubredditInitState, action = {}) => {
            initSubredditState(state, action);

            state[action.payload.subreddit].isFetching = false;
            state[action.payload.subreddit].didInvalidate = false;
            state[action.payload.subreddit].callbackFetch = true;
            state[action.payload.subreddit].lastUpdated = action.payload.receivedAt;
        },
        beginFetch: (state = fetchBySubredditInitState, action = {}) => {
            initSubredditState(state, action);

            state[action.payload.subreddit].isFetching = true;
            state[action.payload.subreddit].didInvalidate = false;
        },
    },
});

let result = {
    reducer: {
        selectedSubreddit: selectedSubredditSlice.reducer,
        fetchBySubreddit: fetchBySubredditSlice.reducer
    },
    actions: {
        ...selectedSubredditSlice.actions,
        ...fetchBySubredditSlice.actions
    }
}

export default result;