package com.cn.xiang.mybaits.services.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.mybaits.mapper.IUserMapper;
import com.cn.xiang.mybaits.po.UserPo;
import com.cn.xiang.mybaits.services.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class UserServiceImpl extends ServiceImpl<IUserMapper, UserPo> implements IUserService {
    @Override
    public UserPo findUserByGroupNameAndUsername(String groupName, String username) {
        return baseMapper.findUserByGroupNameAndUsername(groupName, username);
    }

    @Override
    public List<UserPo> findUsersByGroupName(String groupName){
        return baseMapper.findUsersByGroupName(groupName);
    }
}
