package com.cn.xiang.fsockmanageweb.spring;

import jakarta.servlet.ServletContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


/**
 * servletContext相关对象的工具类
 *
 * <AUTHOR>
 * @date 2018年6月6日 上午11:44:31
 * @version 1.0
 *
 */
public class ServletContextUtil {
    private ServletContextUtil() {
        throw new IllegalAccessError("can not be instantiated");
    }

    /**
     * 获取HttpServletRequest
     *
     * @return
     */
    public static HttpServletRequest getHttpServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest();
    }

    /**
     * 获取HttpSession
     *
     * @return
     */
    public static HttpSession getHttpSession() {
        return ServletContextUtil.getHttpServletRequest().getSession();
    }

    /**
     * 获取HttpSession
     *
     * @param create
     * @return
     */
    public static HttpSession getHttpSession(boolean create) {
        return ServletContextUtil.getHttpServletRequest().getSession(create);
    }

    /**
     * 获取ServletContext
     *
     * @return
     */
    public static ServletContext getServletContext() {
        return ServletContextUtil.getHttpServletRequest().getServletContext();
    }

    /**
     * 获取response
     *
     * @return
     */
    public static HttpServletResponse getHttpServletResponse() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getResponse();
    }
}
