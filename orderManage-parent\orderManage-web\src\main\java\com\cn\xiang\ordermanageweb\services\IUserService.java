package com.cn.xiang.ordermanageweb.services;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.ordermanageweb.po.UserPo;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
public interface IUserService extends IService<UserPo> {

    UserPo getById1(String userId);
}
