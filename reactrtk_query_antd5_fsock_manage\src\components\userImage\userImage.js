import {<PERSON><PERSON>, <PERSON>u, Popover, Modal} from 'antd';
import React, {Component} from "react";
import './userImage.less';

const MENU_ITEMS = [
    /*{label: '菜单项一', key: 'item-1'},
    {label: '菜单项二', key: 'item-2'},
    {label: '菜单项三', key: 'item-3'},
    {label: '菜单项四', key: 'item-4'},*/
    {label: '退出', key: 'exit'},
]

class UserImage extends Component {
    menuRef;

    constructor(props) {
        super(props);

        this.menuRef = React.createRef();
        this.redirectFormRef = React.createRef();
        this.state = {
            isPopoverOpen: false,
            menuSelectedKeys: [],
            isModalOpen: false
        }
    }

    setPopoverOpenState = (isOpen) => {
        this.setState({
            isPopoverOpen: isOpen
        })
    }

    handlePopoverOpenChange = (isOpen) => {
        this.setPopoverOpenState(isOpen);
    }

    onMenuClick = (e) => {
        //菜单点击，隐藏popover弹窗
        this.setPopoverOpenState(false);

        if (e.key === 'exit') {
            this.showExitModal(true);
        }
    }

    onMenuItemSelect = (e) => {
        // this.setState({
        //     menuSelectedKeys: [].concat(e.key)
        // });
    }

    showExitModal = (isOpen) => {
        this.setState({
            isModalOpen: isOpen,
        });
    }

    hideModal = () => {
        this.showExitModal(false);
    }

    handleConfirm = (e) => {
        // this.hideModal();

        //TODO send a request to logout,if success to logout,
        // then submit a redirect form
        // this.redirectFormRef.current.submit();
        //
        this.props.logout.call(this);
    }


    render() {
        return <>
            <Popover open={this.state.isPopoverOpen} onOpenChange={this.handlePopoverOpenChange}
                     id={'user-image-popover'} placement="bottom"
                     content={
                         <Menu ref={this.menuRef} style={{position: 'relative', zIndex: '100'}} items={MENU_ITEMS}
                               mode={'vertical'}
                               theme={'dark'} onClick={this.onMenuClick} onSelect={this.onMenuItemSelect}
                               selectedKeys={this.state.menuSelectedKeys}
                         />
                     }>
                <Avatar className={'user-image'} {...this.props.avatorProps}/>
            </Popover>
            <Modal
                closable={false}
                centered={true}
                title="退出"
                open={this.state.isModalOpen}
                onOk={this.handleConfirm}
                onCancel={this.hideModal}
                okText="确认"
                cancelText="取消">
                确认要退出？
            </Modal>
            <form action={'api_v1/sysUser/logout/redirect'} method={'post'} ref={this.redirectFormRef} id={'redirect_form'}
                  style={{display: 'none'}}/>
        </>
    }
}

export default UserImage;
