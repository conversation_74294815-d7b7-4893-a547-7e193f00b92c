import React, {Component, Suspense} from 'react';
import LoadingMask from "../../../../components/loading/LoadingMask";

const enhance = (LoadedComponent) => {
    class OfficialAsyncComponent extends Component {

        render() {
            return (
                <Suspense fallback={<LoadingMask show={true}/>}>
                    <LoadedComponent ref={this.props.forwardedRef} {...this.props} />
                </Suspense>
            );
        }
    }

    return React.forwardRef((props, ref) => {
        return <OfficialAsyncComponent {...props} forwardedRef={ref}/>
    });
};

export default enhance;
