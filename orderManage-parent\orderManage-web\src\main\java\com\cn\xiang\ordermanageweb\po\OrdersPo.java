package com.cn.xiang.ordermanageweb.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;

/**
 * <AUTHOR>
 * @date 2024/6/7 19:45
 */
@TableName(value = "tb_orders")
public class OrdersPo extends BaseEntity {
    private String userId;
    private String status;
    private double totalAmount;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }
}
