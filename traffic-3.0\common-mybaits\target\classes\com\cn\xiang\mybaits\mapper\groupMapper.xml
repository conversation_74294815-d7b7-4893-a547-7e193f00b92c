<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.mybaits.mapper.IGroupMapper">
    <resultMap id="groupResultMap" type="com.cn.xiang.mybaits.po.GroupPo">
        <!-- <id column="id" property="id"/>
         <result column="name" property="name"/>
         <result column="created_by" property="createdBy"/>
         <result column="created_time" property="createdTime"/>
         <result column="updated_by" property="updatedBy"/>
         <result column="updated_time" property="updatedTime"/>-->

        <association property="trafficRulePo" column="traffic_rule_id"
                     select="com.cn.xiang.mybaits.mapper.ITrafficRuleMapper.selectById"/>
    </resultMap>

    <select id="findGroupByName" parameterType="String" resultMap="groupResultMap">
        select * from tb_group g where g.name = #{groupName}
    </select>

</mapper>
