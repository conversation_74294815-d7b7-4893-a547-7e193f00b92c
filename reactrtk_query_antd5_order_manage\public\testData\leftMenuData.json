{"status": true, "errorCode": "000000", "errorMsg": "", "content": [{"id": "sub1", "pid": "", "type": "sub", "iconType": "HomeOutlined", "name": "menuSub1"}, {"id": "sub2", "pid": "", "type": "sub", "iconType": "HomeOutlined", "name": "menuSub2"}, {"id": "sub3", "pid": "", "type": "sub", "iconType": "HomeOutlined", "name": "menuSub3"}, {"id": "sub4", "pid": "", "type": "sub", "iconType": "PlayCircleOutlined", "name": "menuSub4"}, {"id": "sub2_item1", "pid": "sub1", "type": "item", "name": "sub2_item1", "iconType": "HomeOutlined", "href": "#indexModule5"}, {"id": "sub1_item4", "pid": "sub1", "type": "item", "name": "sub1_item4", "iconType": "HomeOutlined", "href": "#indexModule4"}, {"id": "sub1_item1", "pid": "sub1", "type": "item", "name": "sub1_item1", "iconType": "HomeOutlined", "href": "#indexModule1"}, {"id": "sub1_item2", "pid": "sub1", "type": "item", "name": "sub1_item2", "iconType": "HomeOutlined", "href": "#indexModule2"}, {"id": "sub1_item3", "pid": "sub1", "type": "item", "name": "sub1_item3", "iconType": "HomeOutlined", "href": "#indexModule3"}, {"id": "sub2_item2", "pid": "sub2", "type": "item", "name": "sub2_item2", "iconType": "HomeOutlined", "href": "#indexModule2"}, {"id": "sub2_item3", "pid": "sub2", "type": "item", "name": "sub2_item3", "iconType": "HomeOutlined", "href": "#indexModule3"}, {"id": "sub2_item4", "pid": "sub2", "type": "item", "name": "sub2_item4", "iconType": "HomeOutlined", "href": "#indexModule4"}, {"id": "sub2_item5", "pid": "sub2", "type": "item", "name": "sub2_item5", "iconType": "HomeOutlined", "href": "#indexModule1"}, {"id": "sub2_item6", "pid": "sub2", "type": "item", "name": "sub2_item6", "iconType": "HomeOutlined", "href": "#indexModule1"}, {"id": "sub2_item7", "pid": "sub2", "type": "item", "name": "sub2_item7", "iconType": "HomeOutlined", "href": "#indexModule1"}, {"id": "sub2_item8", "pid": "sub2", "type": "item", "name": "sub2_item8", "iconType": "HomeOutlined", "href": "#indexModule1"}, {"id": "sub3_item1", "pid": "sub3", "type": "item", "name": "sub3_item1", "iconType": "HomeOutlined", "href": "#indexModule1"}, {"id": "sub4_item1", "pid": "sub4", "type": "item", "name": "sub4_item1", "iconType": "HomeOutlined", "href": "#indexModule2"}, {"id": "sub1_sub1", "pid": "sub1", "type": "sub", "iconType": "DownCircleOutlined", "name": "sub1_sub1"}, {"id": "sub1_sub1_item1", "pid": "sub1_sub1", "type": "item", "name": "sub1_sub1_item1", "iconType": "HomeOutlined", "href": "#indexModule6"}, {"id": "sub5", "pid": "", "type": "item", "name": "sub5", "iconType": "PlayCircleOutlined", "href": "#indexModule55"}, {"id": "sub6", "pid": "", "type": "item", "name": "用户管理", "iconType": "UserOutlined", "href": "#userModule"}, {"id": "sub7", "pid": "", "type": "item", "name": "用户访问日志管理", "iconType": "GlobalOutlined", "href": "#userAccessLogModule"}, {"id": "sub8", "pid": "", "type": "item", "name": "组管理", "iconType": "UsergroupAddOutlined", "href": "#groupModule"}, {"id": "sub9", "pid": "", "type": "item", "name": "流量规则管理", "iconType": "LineChartOutlined", "href": "#trafficRuleModule"}]}