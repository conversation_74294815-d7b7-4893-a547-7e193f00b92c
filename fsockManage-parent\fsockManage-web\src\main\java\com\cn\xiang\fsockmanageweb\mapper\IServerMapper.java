package com.cn.xiang.fsockmanageweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cn.xiang.fsockmanageweb.po.ServerPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5 15:36
 */
@Mapper
public interface IServerMapper extends BaseMapper<ServerPo> {

    public long listUserCountByGroupName(@Param("groupName") String groupName);

    List<ServerPo> listBySearch(@Param("search") String search);

    ServerPo findServerByUserId(@Param("userId") String userId);
}
