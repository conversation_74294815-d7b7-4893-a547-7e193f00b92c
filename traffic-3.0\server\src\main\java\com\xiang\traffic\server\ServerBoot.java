package com.xiang.traffic.server;

import com.xiang.traffic.ComponentException;
import com.xiang.traffic.LifecycleState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.nio.charset.StandardCharsets;

/**
 * 服务器启动引导类
 */
public abstract class ServerBoot {

    private static final Logger log = LoggerFactory.getLogger(ServerBoot.class);

    private static final Server server = new StandardServer();

    public static void main(String[] args) throws MalformedURLException, ClassNotFoundException {
        if (server.getState() != LifecycleState.NEW) {
            throw new Error();
        }
        boot();
    }

    private static void boot() {
        synchronized (ServerBoot.class) {
            if (server.getState() != LifecycleState.NEW) {
                return;
            }

            printBanner();
            long st = System.currentTimeMillis();
            log.info("traffic server {} start...", server.getVersion());
            try {
                server.init();
                server.start();
                long ed = System.currentTimeMillis();
                log.info("traffic server {} start complete, use {} millisecond", server.getVersion(), ed - st);

                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    log.info("traffic server {} ready to shutdown", server.getVersion());
                    shutdown();
                    log.info("traffic server {} shut down complete", server.getVersion());
                }));

            } catch (ComponentException e) {
                log.error("traffic server {} start failure, cause:", server.getVersion());
                log.info("If it caused by BUG, please submit issue at https://github.com/abc123lzf/flyingsocks , Thanks");
            }
        }
    }

    private static void shutdown() {
        synchronized (ServerBoot.class) {
            if (server.getState() != LifecycleState.STARTED) {
                throw new IllegalStateException("Server state is not STARTED");
            }

            server.stop();
        }
    }

    private static void printBanner() {
        try (InputStream is = server.getConfigManager().loadResource("classpath://META-INF/banner"); BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.US_ASCII))) {
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
        } catch (IOException ignore) {
            // NOOP
        }
    }


    private ServerBoot() {
        throw new UnsupportedOperationException();
    }
}
