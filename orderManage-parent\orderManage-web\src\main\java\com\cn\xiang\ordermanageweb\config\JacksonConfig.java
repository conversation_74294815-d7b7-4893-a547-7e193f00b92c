package com.cn.xiang.ordermanageweb.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class JacksonConfig {
    @Bean
    public BeanPostProcessor objectMapperBeanPostProcessor() {
        return new ObjectMapperBeanPostProcessor();
    }

    /**
     *
     * 拦截ObjectMapper对象并修改配置，当序列化空值时不显示null，改为""
     *
     * <AUTHOR>
     * @date 2022年06月06日 下午9:00:25
     * @version 1.0
     *
     */
    static class ObjectMapperBeanPostProcessor implements BeanPostProcessor {

        @Override
        public Object postProcessAfterInitialization(Object bean, String beanName)
                throws BeansException {

            if (bean.getClass().equals(ObjectMapper.class)
                    && beanName.equals("jacksonObjectMapper")) {
                ObjectMapper objectMapper = (ObjectMapper) bean;
                objectMapper.getSerializerProvider()
                        .setNullValueSerializer(new JsonSerializer<Object>() {
                            @Override
                            public void serialize(Object value, JsonGenerator gen,
                                    SerializerProvider serializers) throws IOException {
                                gen.writeString("");
                            }
                        });
                //支持单引号
//                objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
            }
            return bean;
        }
    }

}
