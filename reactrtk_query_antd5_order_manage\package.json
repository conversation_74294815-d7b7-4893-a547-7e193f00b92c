{"name": "create-react-app-antd5", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@ant-design/icons": "^5.6.1", "@reduxjs/toolkit": "^2.5.1", "antd": "^5.24.1", "axios": "^1.6.3", "echarts": "^5.6.0", "history": "^5.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-scripts": "^5.0.1", "redux": "^5.0.1", "react-redux": "^9.2.0", "redux-logger": "^4.0.0", "redux-thunk": "^3.1.0"}, "scripts": {"start": "craco start", "build": "SET NODE_OPTIONS=--openssl-legacy-provider &&craco build", "test": "SET NODE_OPTIONS=--openssl-legacy-provider &&craco test"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "babel-plugin-import": "^1.13.8", "craco-less": "^3.0.1"}}