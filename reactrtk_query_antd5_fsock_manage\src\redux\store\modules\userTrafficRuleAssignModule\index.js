import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Space, Table} from "antd";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: false
});

class groupTrafficRuleAssignModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true, selectedRowKeys: [],
        }
        this.tableRef = React.createRef();
    }

    selectedRows;

    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            selectedRowKeys: selectedRowKeys, btnDisabled: false,
        })
    }

    render() {
        return (<div>
            <Space style={{marginBottom: 16}}>
                <Button type="primary" disabled={this.state.btnDisabled}
                        onClick={this.props.assignBtnOnClick.bind(this)}>
                    分配
                </Button>
            </Space>
            <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={{
                type: 'radio', selectedRowKeys: this.state.selectedRowKeys, onChange: this.onChange
            }}>
                <Column align={"center"} title="流量规则名" dataIndex="name"/>
                <Column align={"center"} title="日流量" dataIndex="dailyLimit"/>
                <Column align={"center"} title="周流量" dataIndex="weeklyLimit"/>
                <Column align={"center"} title="月流量" dataIndex="monthlyLimit"/>
                <Column align={"center"} title="写流量限制" dataIndex="writeLimit"/>
                <Column align={"center"} title="读流量限制" dataIndex="readLimit"/>
                <Column align={"center"} title="是否可用" dataIndex="enable" render={
                    (status) => {
                        return status ? '是' : '否';
                    }
                }/>
            </TableWrapper>
        </div>)
    }
}

export default groupTrafficRuleAssignModule;
