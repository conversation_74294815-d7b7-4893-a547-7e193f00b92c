package com.cn.xiang.mybaits.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.mybaits.config.base.BaseEntity;
import com.cn.xiang.mybaits.po.typeHandler.TrafficCapacityPoTypeHandler;
import com.cn.xiang.mybaits.po.typeHandler.TrafficRulePoTypeHandler;

/**
 * <AUTHOR>
 * @date 2024/2/5 15:36
 */
@TableName(value = "tb_group", autoResultMap = true)
public class GroupPo extends BaseEntity {
    private String name;

    /**
     * 需要配合@TableName中autoResultMap属性为true才会生效
     */
    @TableField(value = "traffic_rule_id", typeHandler = TrafficRulePoTypeHandler.class)
    private TrafficRulePo trafficRulePo;
    @TableField(value = "traffic_capacity_id", typeHandler = TrafficCapacityPoTypeHandler.class)
    private TrafficCapacityPo trafficCapacityPo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public TrafficRulePo getTrafficRulePo() {
        return trafficRulePo;
    }

    public void setTrafficRulePo(TrafficRulePo trafficRulePo) {
        this.trafficRulePo = trafficRulePo;
    }

    public TrafficCapacityPo getTrafficCapacityPo() {
        return trafficCapacityPo;
    }

    public void setTrafficCapacityPo(TrafficCapacityPo trafficCapacityPo) {
        this.trafficCapacityPo = trafficCapacityPo;
    }
}
