const url = 'trafficCapacity';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getTrafficCapacityListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'GET'
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let list = content.list;
                for (let i = 0; i < list.length; i++) {
                    list[i].key = list[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
    }
}
