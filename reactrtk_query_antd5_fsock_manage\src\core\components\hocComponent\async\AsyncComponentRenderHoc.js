import React, {Component} from 'react';
import {connect} from "react-redux";
import {createSlice} from "@reduxjs/toolkit";
import ModuleRegister from "../../../module/register/moduleRegister";

/**
 * 包裹容器类组件，让容器内部异步加载组件，并生成一个action,eg:dynamic_router_tableModule2Action，调用renderModule（module）action即可实现容器内异步加载组件。
 * TargetContainerComponent需要约定传递一个asyncComponent属性，该值是一个异步组件，在TargetContainerComponent组件render方法中需要的地方引用该动态组件即可。
 * TargetContainerComponent实现参考/component/router/Route.js
 *
 * Route组件使用时必须配置一个actionId属性，Route组件会自动生成一个action，eg:dynamic_router_tableModule2Action
 * @param TargetContainerComponent
 * @returns {React.ForwardRefExoticComponent<React.PropsWithoutRef<{}> & React.RefAttributes<unknown>>}
 */
const enhance = (TargetContainerComponent) => {
    let actionObj_;
    let reducer_;
    // let componentName_ = 'Dynamic_component_' + TargetContainerComponent.name;
    const ACTION_ID_PREFIX = 'dynamic_router_';
    /**
     * 创建slice，生成action和reducer
     * @param actionId
     * @returns {{reducer: {[p: string]: (state: ({modules: []} | undefined), action: UnknownAction) => {modules: []}}, actions: CaseReducerActions<{renderModule: reducers.renderModule}, string>}}
     */
    const generateActionReducer = (actionId) => {
        let initialState = {
            module: undefined
        };
        const slice = createSlice({
            name: actionId + 'Slice',
            initialState,
            reducers: {
                /**
                 * 渲染模型
                 * @param state
                 * @param action
                 */
                renderModule: (state, action) => {
                    state.module = action.payload.module;
                }
            },
        });

        return {
            reducer: {
                [actionId]: slice.reducer
            },
            actions: slice.actions
        };
    }

    /**
     * 定义action
     * @param actionReducerObj
     * @param actionId
     * @returns {{}}
     */
    const defineAction = (actionReducerObj, actionId) => {
        let actions = actionReducerObj.actions;

        /**
         * 参数是moduleId，即路由url
         * @param moduleId
         * @returns {(function(*, *): void)|*}
         */
        const renderModule = (moduleId) => {
            return function (dispatch, getState) {
                let moduleMap = ModuleRegister.getRegisterModuleMap();
                let module = moduleMap[moduleId];
                dispatch(actions.renderModule({
                    module,
                }));
            }
        };

        return {
            [actionId]: {
                renderModule
            }
        };
    }
    /**
     * 注册action
     * @param actionReducerObj
     * @param componentName
     */
    const registerAction = (actionReducerObj, componentName) => {
        actionObj_ = defineAction(actionReducerObj, componentName);
        window.global.actions = Object.assign(window.global.actions, actionObj_);
    }
    /**
     * 注册reducer
     * @param actionReducerObj
     * @param componentName
     */
    const registerReducer = (actionReducerObj, componentName) => {
        reducer_ = actionReducerObj.reducer;
        window.global.DynamicReducerManager.registerReducer(reducer_);
    }

    /**
     * 生成action
     */
    const generateAction = (actionId) => {
        let actionReducerObj = generateActionReducer(actionId);

        registerAction(actionReducerObj, actionId);

        registerReducer(actionReducerObj, actionId);
    }

    const removeAction = () => {
        unregisterAction();
        unregisterReducer();
    }
    /**
     * 取消注册action
     */
    const unregisterAction = () => {
        if (actionObj_) {
            let keys = Object.keys(actionObj_);
            if (keys.length === 1) {
                let key = keys[0];
                delete window.global.actions[key];
            }
        }
    }
    /**
     * 取消注册reducer
     */
    const unregisterReducer = () => {
        if (reducer_) {
            let keys = Object.keys(reducer_);
            if (keys.length === 1) {
                let key = keys[0];
                window.global.DynamicReducerManager.unregisterReducer(key);
            }
        }
    }

    /**
     * 包裹组件
     */
    class AsyncComponentRenderWrapper extends Component {

        componentDidMount() {
            if (!this.props.actionId) {
                throw new Error('missing property actionId in Component named Route');
            }
            let actionId = ACTION_ID_PREFIX + this.props.actionId;
            generateAction(actionId);
        }

        componentWillUnmount() {
            removeAction();
        }

        render() {
            let {asyncComponent, ...props_} = this.props;
            return (
                <TargetContainerComponent {...props_} asyncComponent={asyncComponent}
                                          ref={this.props.forwardedRef}></TargetContainerComponent>
            );
        }
    }

    let Wrapper = React.forwardRef((props, ref) => {
        return <AsyncComponentRenderWrapper {...props} forwardedRef={ref}/>
    });


    const mapStateToProps = (state, ownProps) => {
        //这里主要做redux状态status与组件prop的适配，如果store变化不导致组件更新的话，直接返回ownProps即可。
        let actionId = ACTION_ID_PREFIX + ownProps.actionId;
        if (state[actionId] && state[actionId].module) {
            return {
                asyncComponent: state[actionId].module
            };
        }
        return ownProps;
    };
    const mapDispatchToProps = (dispatch, ownProps) => {
        //注入属性到组件props上
        return {};
    };

    const Container = connect(
        mapStateToProps,
        mapDispatchToProps,
        null,
        {forwardRef: true}
    )(Wrapper);

    return Container;
};

export default enhance;
