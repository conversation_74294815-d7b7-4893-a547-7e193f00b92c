const url = 'trafficRule';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getTrafficRuleListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'GET',
                    body: queryArg,
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let list = content.list;
                for (let i = 0; i < list.length; i++) {
                    list[i].key = list[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
        postTrafficRuleData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'POST',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        putTrafficRuleData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'PUT',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        delTrafficRuleData: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url,
                    method: 'DELETE',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
