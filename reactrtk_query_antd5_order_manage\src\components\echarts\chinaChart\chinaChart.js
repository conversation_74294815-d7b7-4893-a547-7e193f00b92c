import React from 'react'
import EChartAdapter from "../adapter/EChartAdapter";
import registerChinaMap from "./china";

const OPTION = {
    // geo: { // 公共样式
    //     map: 'china',
    //     label: {
    //         show: false, // 显示省份标签
    //     },
    //     itemStyle: {
    //         label: {show: false},
    //         borderWidth: 0.5, // 区域边框宽度
    //         borderColor: '#000', // 区域边框颜色
    //         areaColor: '#762e2e', // 区域颜色
    //     },
    //     emphasis: {
    //         label: {show: false},
    //         areaColor: '#F6C9AB', // 区域颜色
    //     },
    //     roam: true,
    //     // zoom: 1.2 // 设置地图默认大小
    // },
    backgroundColor: '#fff',
    title: {
        text: '全国地图数据',
        subtext: '',
        x: 'center'
    },
    tooltip: {
        trigger: 'item'
    },

    //左侧小导航图标
    visualMap: {
        show: true,
        x: 'left',
        y: 'center',
        splitList: [
            {start: 600, end: 1000},
            {start: 500, end: 600},
            {start: 400, end: 500},
            {start: 300, end: 400},
            {start: 200, end: 300},
            {start: 100, end: 200},
            {start: 0, end: 100},
        ],
        color: ['#278e6f', '#c6c8d0', '#9feaa5', '#85daef', '#74e2ca', '#e6ac53', '#9fb5ea']
    },
    series: [{
        name: '数据',
        type: 'map',
        map: 'china',
        roam: true,
        label: {
            show: true,
            fontSize: '10px'
        },
        emphasis: {
            label: {
                show: true,  //省份名称
            },
            itemStyle: {
                areaColor: '#ea2dac',
            },
        },
        data: []  //数据
    }]
};

/**
 * echarts图表整合示例
 */
class ChinaChart extends React.Component {


    getOption = () => {
        let option = Object.assign({}, OPTION);
        option = Object.assign(option, this.props.option);

        let data = this.props.data;
        if (data) {
            option.series[0].data = data;
        }
        return option;
    }

    render() {
        let {id, option, ...props_} = this.props;
        return <EChartAdapter  {...props_}
                               option={this.getOption()}
                               id={this.props.id}
                               eChartInitCallback={(echarts, eChartInst) => {
                                   registerChinaMap(echarts);
                               }}/>
    }
}

export default ChinaChart;