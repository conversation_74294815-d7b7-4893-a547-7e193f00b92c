//TODO antd5.0去掉了css和less，已经无法引入
// import 'antd/es/modal/style/index.css';
import './LoadingMask.less';
import {useSelector} from "react-redux";

const LoadingMaskFn = function (props) {
    let isShow = useSelector((state) => state.loadingMask);

    if (isShow) {
        return (<div className='loading-mask-ctn'>
            <div className="loading-mask-ant-modal-mask"/>
            <div tabIndex="-1" className="loading-mask-ant-modal-wrap loading-ctn">
                <div className="lds-spinner">
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                    <div/>
                </div>
                <div className='text-ctn'>
                    <span>L</span>
                    <span>o</span>
                    <span>a</span>
                    <span>d</span>
                    <span>i</span>
                    <span>n</span>
                    <span>g</span>
                    <span>.</span>
                    <span>.</span>
                    <span>.</span>
                </div>
            </div>
        </div>);
    }
    //不显示
    return null;
}
export default LoadingMaskFn;
