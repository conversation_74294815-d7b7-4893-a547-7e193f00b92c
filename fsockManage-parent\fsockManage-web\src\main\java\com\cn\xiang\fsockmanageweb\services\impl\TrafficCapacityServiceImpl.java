package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.services.ITrafficCapacityService;
import com.cn.xiang.fsockmanageweb.mapper.ITrafficCapacityMapper;
import com.cn.xiang.fsockmanageweb.po.GroupPo;
import com.cn.xiang.fsockmanageweb.po.TrafficCapacityPo;
import com.cn.xiang.fsockmanageweb.services.IGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class TrafficCapacityServiceImpl extends ServiceImpl<ITrafficCapacityMapper, TrafficCapacityPo> implements ITrafficCapacityService {

    @Autowired
    private IGroupService groupService;

    @Override
    public TrafficCapacityPo findGroupTrafficCapacityByUsername(String username) {
        GroupPo groupPo = groupService.findGroupByUserName(username);
        if (groupPo != null) {
            return groupPo.getTrafficCapacityPo();
        }
        return null;
    }

}
