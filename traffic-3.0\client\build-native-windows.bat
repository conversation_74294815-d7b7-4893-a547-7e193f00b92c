@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Traffic Client GraalVM Native Build Script for Windows
echo ========================================

:: 检查是否安装了GraalVM
where native-image >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: native-image command not found!
    echo Please install GraalVM and add native-image to your PATH.
    echo Download from: https://github.com/graalvm/graalvm-releases
    pause
    exit /b 1
)

:: 显示GraalVM版本信息
echo Checking GraalVM version...
native-image --version

:: 清理之前的构建
echo Cleaning previous builds...
if exist target\traffic-client.exe del target\traffic-client.exe
if exist target\traffic-client-*-jar-with-dependencies.jar del target\traffic-client-*-jar-with-dependencies.jar

:: 使用Maven构建JAR包
echo Building JAR with dependencies...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: Maven build failed!
    pause
    exit /b 1
)

:: 查找生成的JAR文件
for %%f in (target\traffic-client-*-jar-with-dependencies.jar) do set JAR_FILE=%%f

if not exist "%JAR_FILE%" (
    echo ERROR: JAR file not found!
    pause
    exit /b 1
)

echo Found JAR file: %JAR_FILE%

:: 使用Maven Native插件构建Native Image
echo Building native image using Maven plugin...
call mvn native:compile -Pnative
if %errorlevel% neq 0 (
    echo WARNING: Maven native plugin failed, trying direct native-image command...
    
    :: 备用方案：直接使用native-image命令
    echo Building native image directly...
    native-image ^
        --no-fallback ^
        --enable-http ^
        --enable-https ^
        --enable-all-security-services ^
        --allow-incomplete-classpath ^
        --report-unsupported-elements-at-runtime ^
        --initialize-at-build-time=org.slf4j ^
        --initialize-at-run-time=io.netty,com.xiang.traffic.client.gui ^
        -H:+ReportExceptionStackTraces ^
        -H:+AddAllCharsets ^
        -H:IncludeResources=".*\.properties$" ^
        -H:IncludeResources=".*\.yml$" ^
        -H:IncludeResources=".*\.yaml$" ^
        -H:IncludeResources="META-INF/.*" ^
        -H:ConfigurationFileDirectories=src/main/resources/META-INF/native-image ^
        -H:Name=traffic-client ^
        -jar "%JAR_FILE%"
    
    if !errorlevel! neq 0 (
        echo ERROR: Native image build failed!
        pause
        exit /b 1
    )
)

:: 检查生成的可执行文件
if exist target\traffic-client.exe (
    echo SUCCESS: Native executable created at target\traffic-client.exe
    
    :: 显示文件大小
    for %%A in (target\traffic-client.exe) do echo File size: %%~zA bytes
    
    echo.
    echo You can now run the native executable:
    echo   target\traffic-client.exe
) else if exist traffic-client.exe (
    echo SUCCESS: Native executable created at traffic-client.exe
    
    :: 移动到target目录
    move traffic-client.exe target\
    
    :: 显示文件大小
    for %%A in (target\traffic-client.exe) do echo File size: %%~zA bytes
    
    echo.
    echo You can now run the native executable:
    echo   target\traffic-client.exe
) else (
    echo ERROR: Native executable not found!
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
pause
