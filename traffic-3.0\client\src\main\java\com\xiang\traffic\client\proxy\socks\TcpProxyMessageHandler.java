package com.xiang.traffic.client.proxy.socks;

import com.xiang.traffic.client.proxy.ProxyRequest;
import com.xiang.traffic.client.proxy.ProxyRequestManager;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:13
 */
public class TcpProxyMessageHandler extends ChannelInboundHandlerAdapter {

    private static final Logger log = LoggerFactory.getLogger(TcpProxyMessageHandler.class);

    private final ProxyRequest proxyRequest;

    public TcpProxyMessageHandler(ProxyRequest proxyRequest, ProxyRequestManager manager) {
        this.proxyRequest = Objects.requireNonNull(proxyRequest);
        manager.publish(proxyRequest);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof ByteBuf) {
            ByteBuf buf = (ByteBuf) msg;

            byte[] bytes = new byte[buf.readableBytes()];
            buf.readBytes(bytes); // 移动读索引
            buf.resetReaderIndex();

//            System.out.println("客户端接收器接收到的数据：" );
//            System.out.println(new String(bytes));

            try {
                proxyRequest.transferClientMessage(buf);
            } finally {
                buf.release();
            }
        } else {
            ctx.fireChannelRead(msg);
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        if (cause instanceof IOException) {
            log.info("Local client close connection, from {}", ctx.channel().remoteAddress());
            ctx.close();
        } else if (log.isWarnEnabled()) {
            log.warn("Exception caught in TCPProxyMessageHandler", cause);
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        proxyRequest.close();
        ctx.pipeline().remove(this);
        ctx.fireChannelInactive();
    }
}
