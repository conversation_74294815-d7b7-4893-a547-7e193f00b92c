import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Space, Table} from "antd";
import PackageResourcesAddModal from "../../../../components/packageResources/modal/PackageResourcesAddModal";
import PackageResourcesDelModal from "../../../../components/packageResources/modal/PackageResourcesDelModal";
import {
    DownloadOutlined,
} from '@ant-design/icons';
import Utils from "../../../../core/utils/utils";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: false
});

class UserAccessLogModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true,
            selectedRowKeys: []
        }
        this.addModalRef = React.createRef();
        this.delModalRef = React.createRef();
        this.tableRef = React.createRef();
    }

    selectedRows;
    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            btnDisabled: false,
            selectedRowKeys: selectedRowKeys
        })
    }

    addBtnOnClick = (event) => {
        //打开模态框
        this.addModalRef.current.setState({
            visible: true
        });
    };
    delBtnOnClick = (event) => {
        //打开模态框
        this.delModalRef.current.setState({
            visible: true
        });
    };

    render() {
        return (
            <div>
                <Space style={{marginBottom: 16}}>
                    <Button type="primary" onClick={this.addBtnOnClick}>
                        添加
                    </Button>

                    <Button type="primary" disabled={this.state.btnDisabled} onClick={this.delBtnOnClick}>
                        删除
                    </Button>
                </Space>
                <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={{
                    type: 'radio',
                    selectedRowKeys: this.state.selectedRowKeys,
                    onChange: this.onChange
                }}>
                    <Column align={"center"} title="文件名" dataIndex="fileName"/>
                    <Column align={"center"} title="版本" dataIndex="version"/>
                    <Column align={"center"} title="文件路径" dataIndex="filePath"/>
                    <Column align={"center"} title="操作" render={(_, record) => {
                        let packageResourceId = record.id;

                        return <Space size="middle">
                            <a style={{fontSize: '25px'}} href={(() => {
                                return Utils.BaseUrl.getBaseUrl() + "/packageResources/download?packageResourceId=" + packageResourceId;
                            })()}><DownloadOutlined/></a>
                        </Space>
                    }}/>
                </TableWrapper>
                <PackageResourcesAddModal ref={this.addModalRef} userModalRef={this} handleAddOk={() => {
                    return this.props.handleAddOk.call(this);
                }}/>

                <PackageResourcesDelModal ref={this.delModalRef} handleDelOk={() => {
                    return this.props.handleDelOk.call(this);
                }}/>
            </div>
        )
    }
}

export default UserAccessLogModule;
