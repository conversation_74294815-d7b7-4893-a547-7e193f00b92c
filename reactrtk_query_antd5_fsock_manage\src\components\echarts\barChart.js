import React from 'react'
import EChartAdapter from "./adapter/EChartAdapter";
import * as echarts from "echarts";

let dataAxis = ['点', '击', '柱', '子', '或', '者', '两', '指', '在', '触', '屏', '上', '滑', '动', '能', '够', '自', '动', '缩', '放'];
const OPTION = {
    title: {
        text: '渐变色 阴影 点击缩放',
        subtext: ''
    },
    xAxis: {
        data: dataAxis,
        axisLabel: {
            inside: true,
            color: '#fff'
        },
        axisTick: {
            show: false
        },
        axisLine: {
            show: false
        },
        z: 10
    },
    yAxis: {
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: '#999'
        }
    },
    dataZoom: [
        {
            type: 'inside'
        }
    ],
    series: [
        {
            type: 'bar',
            showBackground: true,
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {offset: 0, color: '#83bff6'},
                    {offset: 0.5, color: '#188df0'},
                    {offset: 1, color: '#188df0'}
                ])
            },
            emphasis: {
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {offset: 0, color: '#2378f7'},
                        {offset: 0.7, color: '#2378f7'},
                        {offset: 1, color: '#83bff6'}
                    ])
                }
            },
            data: []
        }
    ]
};

/**
 * echarts图表整合示例
 */
class BarChart extends React.Component {

    getOption = () => {
        let option = Object.assign({}, OPTION);
        option = Object.assign(option, this.props.option);

        let data = this.props.data;
        if (data) {
            option.series[0].data = data;
        }
        return option;
    }

    render() {
        let {id, option, ...props_} = this.props;
        return <EChartAdapter {...props_}
                              option={this.getOption()}
                              id={this.props.id}
                              // echart实例初始化回调
                              eChartInitCallback={(echarts, eChartInst) => {
                                  // Enable data zoom when user click bar.
                                  const zoomSize = 6;
                                  let this_ = this;
                                  eChartInst.on('click', function (params) {
                                      // console.log(dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)]);
                                      eChartInst.dispatchAction({
                                          type: 'dataZoom',
                                          startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],
                                          endValue:
                                              dataAxis[Math.min(params.dataIndex + zoomSize / 2, this_.props.data.length - 1)]
                                      });
                                  });
                              }}
        />
    }
}

export default BarChart;