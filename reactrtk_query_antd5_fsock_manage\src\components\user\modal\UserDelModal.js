import React, {Component} from 'react';
import {Modal} from 'antd'

/**
 * 用户删除弹窗
 */
class UserDelModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            visible: false
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };

    render() {
        return <Modal
            title="删除"
            open={this.state.visible}
            onOk={this.props.handleDelOk}
            onCancel={this.handleCancel}
            okText={'删除'}
            cancelText={'取消'}>
            <p>确定要删除该用户？</p>
        </Modal>
    }
}

export default UserDelModal;
