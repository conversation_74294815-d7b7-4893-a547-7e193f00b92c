package com.xiang.traffic.server.core.client;

import com.cn.xiang.mybaits.services.IUserAccessLogService;
import com.cn.xiang.mybaits.utils.ApplicationContextHolder;
import com.xiang.traffic.Component;
import com.xiang.traffic.server.ServerConfig;
import com.xiang.traffic.server.core.ClientSession;
import com.xiang.traffic.server.core.token.UserToken;
import com.xiang.traffic.server.enumeration.ClientAuthType;
import com.xiang.traffic.server.enumeration.DbTypeEnum;
import com.xiang.traffic.server.utils.Utils;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import io.netty.handler.traffic.TrafficCounter;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * ProxyHandler类代理工厂
 * 对ProxyHandler的handlerAdded、channelInactive方法拦截，并记录访问日志。
 *
 * <AUTHOR>
 * @date 2024/2/6 23:19
 */
public class ProxyHandlerDynamicProxyFactory {
    private static final String HANDLER_ADDED_METHOD_NAME = "handlerAdded";
    private static final String CHANNEL_INACTIVE_METHOD_NAME = "channelInactive";
    private static final String ACCESS_LOG_ID_KEY = "accessLogId";

    public static ChannelInboundHandler createProxyHandler(ProxyHandler proxyHandler, Component<ClientProcessor> component) {
        //simple模式,不创建代理
        if (component.getParentComponent().getParentComponent().getServerConfig().authType == ClientAuthType.SIMPLE ||
                !Utils.getDbTypeFromSystemProperty().equals(DbTypeEnum.MYBATIS_PLUS)) {
            return proxyHandler;
        }
        //只用在user模式使用mybaits,创建代理
        return (ChannelInboundHandler) Proxy.newProxyInstance(ProxyHandlerDynamicProxyFactory.class.getClassLoader(), new Class[]{ChannelInboundHandler.class},
                new InvocationHandler() {
                    @Override
                    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                        if (method.getName().equals(HANDLER_ADDED_METHOD_NAME) && args.length == 1) {
                            return handlerAdded(proxy, method, args);
                        } else if (method.getName().equals(CHANNEL_INACTIVE_METHOD_NAME) && args.length == 1) {
                            return channelInactive(proxy, method, args);
                        }

                        return method.invoke(proxyHandler, args);
                    }

                    /**
                     * handlerAdded包装
                     * @param proxy
                     * @param method
                     * @param args
                     * @return
                     * @throws InvocationTargetException
                     * @throws IllegalAccessException
                     */
                    private Object handlerAdded(Object proxy, Method method, Object[] args) throws InvocationTargetException, IllegalAccessException {
                        Object result = method.invoke(proxyHandler, args);

                        ChannelHandlerContext ctx = (ChannelHandlerContext) args[0];
                        recordEnterLog(ctx);
                        return result;
                    }

                    /**
                     * channelInactive包装
                     * @param proxy
                     * @param method
                     * @param args
                     * @return
                     * @throws InvocationTargetException
                     * @throws IllegalAccessException
                     */
                    private Object channelInactive(Object proxy, Method method, Object[] args) throws InvocationTargetException, IllegalAccessException {
                        ChannelHandlerContext ctx = (ChannelHandlerContext) args[0];
                        recordLeaveLog(ctx);

                        return method.invoke(proxyHandler, args);
                    }

                    /**
                     * 记录访问日志
                     *
                     * @param ctx
                     */
                    private void recordEnterLog(ChannelHandlerContext ctx) {
                        ServerConfig.Node node = component.getParentComponent().getParentComponent().getServerConfig();
                        if (node.authType.equals(ClientAuthType.USER)) {
                            ClientSession clientSession = ConnectionContext.clientSession(ctx.channel());
                            UserToken userToken = (UserToken) clientSession.getAuthToken();
                            String userId = userToken.getPrinciple().getId();

                            IUserAccessLogService userAccessLogService = ApplicationContextHolder.getBean(IUserAccessLogService.class);
                            String accessLogId = userAccessLogService.userEnter(userId, ctx.channel().remoteAddress().toString());

//                            ctx.channel().attr(AttributeKey.valueOf(ACCESS_LOG_ID_KEY)).set(accessLogId);
                            clientSession.getAttributes().put(ACCESS_LOG_ID_KEY, accessLogId);
                        }
                    }

                    /**
                     * 记录用户离开日志
                     * @param ctx
                     */
                    private void recordLeaveLog(ChannelHandlerContext ctx) {
//                        String accessLogId = (String) ctx.channel().attr(AttributeKey.valueOf(ACCESS_LOG_ID_KEY)).get();
                        String accessLogId = (String) ConnectionContext.clientSession(ctx.channel()).getAttributes().get(ACCESS_LOG_ID_KEY);

                        ChannelTrafficShapingHandler trafficShapingHandler = (ChannelTrafficShapingHandler) ctx.pipeline()
                                .get(ProxyAuthenticationHandler.TRAFFIC_SHAPING_HANDLER_NAME);
                        TrafficCounter trafficCounter = trafficShapingHandler.trafficCounter();

                        IUserAccessLogService userAccessLogService = ApplicationContextHolder.getBean(IUserAccessLogService.class);
                        userAccessLogService.userLeave(accessLogId, trafficCounter.cumulativeReadBytes(), trafficCounter.cumulativeWrittenBytes());
                    }
                });
    }
}
