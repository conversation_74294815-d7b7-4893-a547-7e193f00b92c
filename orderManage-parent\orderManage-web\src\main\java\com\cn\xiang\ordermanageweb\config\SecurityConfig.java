package com.cn.xiang.ordermanageweb.config;

import com.cn.xiang.ordermanageweb.config.provider.LoginValidateAuthenticationProvider;
import com.cn.xiang.ordermanageweb.utils.JSONResultMessage;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import java.io.IOException;

@Configuration
public class SecurityConfig {
    private static final String LOGIN_SUCCESS_MSG = "登陆成功！";
    private static final String LOGOUT_SUCCESS_MSG = "登出成功!";

    @Autowired
    private LoginValidateAuthenticationProvider loginValidateAuthenticationProvider;

    private AuthenticationSuccessHandler successHandler = new AuthenticationSuccessHandler() {
        @Override
        public void onAuthenticationSuccess(HttpServletRequest request,
                                            HttpServletResponse response, Authentication authentication)
                throws IOException, ServletException {
            sendMessage(request, response, authentication);
        }

        private void sendMessage(HttpServletRequest request, HttpServletResponse response,
                                 Authentication authentication) throws IOException {
            response.setContentType("application/json;charset=UTF-8");
            JSONResultMessage result = new JSONResultMessage();
            result.success();
            result.addParameter("message", LOGIN_SUCCESS_MSG);
            response.getWriter().write(result.toString());
        }
    };

    private AuthenticationFailureHandler failureHandler = new AuthenticationFailureHandler() {
        @Override
        public void onAuthenticationFailure(HttpServletRequest request,
                                            HttpServletResponse response, AuthenticationException exception)
                throws IOException, ServletException {
            sendMessage(request, response, exception);
        }

        private void sendMessage(HttpServletRequest request, HttpServletResponse response,
                                 AuthenticationException exception) throws IOException {
            response.setContentType("application/json;charset=UTF-8");
            JSONResultMessage result = new JSONResultMessage();
            result.error(exception.getMessage());
            response.getWriter().write(result.toString());
        }
    };

    private LogoutSuccessHandler logoutSuccessHandler = new LogoutSuccessHandler() {
        @Override
        public void onLogoutSuccess(HttpServletRequest request,
                                    HttpServletResponse response, Authentication authentication)
                throws IOException, ServletException {
            sendMessage(request, response, authentication);
        }

        private void sendMessage(HttpServletRequest request, HttpServletResponse response,
                                 Authentication authentication) throws IOException {
            response.setContentType("application/json;charset=UTF-8");
            JSONResultMessage result = new JSONResultMessage();
            result.success();
            result.addParameter("message", LOGOUT_SUCCESS_MSG);
            response.getWriter().write(result.toString());
        }
    };

//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
//        // 这里要设置自定义认证
//        auth.authenticationProvider(loginValidateAuthenticationProvider);
//    }

//    public AccessDecisionManager accessDecisionManager() {
//        List<AccessDecisionVoter<? extends Object>> decisionVoters = Arrays
//                .asList(new WebExpressionVoter(), new AclAccessDecisionVoter());
//        // return new UrlAffirmativeBasedDecisionManager(decisionVoters);
//        return new UnanimousBased(decisionVoters);
//    }

//    @Override
//    protected void configure(HttpSecurity http) throws Exception {
//        // 认证配置
//        http.csrf().disable().
//                authorizeRequests().antMatchers("/swagger-**/**").permitAll()
//                .antMatchers("/v2/api-docs").permitAll()
//                .antMatchers("/static/**").permitAll()
//                .antMatchers("/*.(png)").permitAll()
//                .antMatchers("/*.json").permitAll()
//                .antMatchers("/*.ico").permitAll()
//                .antMatchers("/*.jpg").permitAll()
//                .antMatchers("/api_v1/sysUser/isLogined").permitAll()
//                // 支付宝支付回调post，如果不配置，会被security身份认证拦截
//                .antMatchers("/api_v1/alipay/notify").permitAll()
//                .anyRequest()
//                .authenticated().
//                // 登陆配置
//                        and().formLogin().successHandler(successHandler)
//                .failureHandler(failureHandler)
//                .loginProcessingUrl("/api_v1/sysUser/login").permitAll()
//                .loginPage("/").permitAll()
//                // 登出配置
//                .and().logout().logoutUrl("/api_v1/sysUser/logout")
//                .logoutSuccessHandler(logoutSuccessHandler).invalidateHttpSession(true)
//                .clearAuthentication(true).deleteCookies("JSESSIONID").permitAll();
//    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
// 认证配置
        http.csrf((csrf) -> {
            csrf.disable();
        }).authorizeHttpRequests((authorizeRequests) -> {
            authorizeRequests.requestMatchers("/swagger-**/**").permitAll();
            authorizeRequests.requestMatchers("/v2/api-docs").permitAll();
            authorizeRequests.requestMatchers("/static/**").permitAll();
            authorizeRequests.requestMatchers("/*.(png)").permitAll();
            authorizeRequests.requestMatchers("/*.json").permitAll();
            authorizeRequests.requestMatchers("/*.ico").permitAll();
            authorizeRequests.requestMatchers("/*.jpg").permitAll();
//                    authorizeRequest.requestMatchers("/api_v1/sysUser/testAdd").permitAll();
            //配置了 formLoginConfigurer.loginPage("/").permitAll();
            //只是把"/"访问权限开了，没有把"/index.html"的访问权限开启
            authorizeRequests.requestMatchers("/index.html").permitAll();
            authorizeRequests.requestMatchers("/api_v1/sysUser/isLogined").permitAll();
            // 支付宝支付回调post，如果不配置，会被security身份认证拦截
            authorizeRequests.requestMatchers("/api_v1/alipay/notify").permitAll();
            authorizeRequests.anyRequest().authenticated();
            // 登陆配置
        }).formLogin((formLoginConfigurer) -> {
            formLoginConfigurer.successHandler(successHandler);
            formLoginConfigurer.failureHandler(failureHandler);
            formLoginConfigurer.loginProcessingUrl("/api_v1/sysUser/login").permitAll();
            formLoginConfigurer.loginPage("/").permitAll();
        }).logout((logoutConfigurer) -> {
            logoutConfigurer.logoutUrl("/api_v1/sysUser/logout").permitAll();
            logoutConfigurer.logoutSuccessHandler(logoutSuccessHandler);
            logoutConfigurer.invalidateHttpSession(true);
            logoutConfigurer.clearAuthentication(true);
            logoutConfigurer.deleteCookies("JSESSIONID");
        });

        AuthenticationManagerBuilder authenticationManagerBuilder = http.getSharedObject(AuthenticationManagerBuilder.class);
        authenticationManagerBuilder.authenticationProvider(loginValidateAuthenticationProvider);

        return http.build();
    }
}
