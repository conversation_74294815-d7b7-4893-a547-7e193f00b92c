package com.cn.xiang.ordermanageweb.vo;

import com.alipay.api.domain.GoodsDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/26 14:05
 */
public class PayVo {
    //订单标题，如Iphone6 16G
    private String subject;
    //商户订单号，如20150320010101001
    private String outTradeNo;
    //订单总金额
    private double totalAmount;
    //商品明细列表
    private List<GoodsDetail> goodsDetails;

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<GoodsDetail> getGoodsDetails() {
        return goodsDetails;
    }

    public void setGoodsDetails(List<GoodsDetail> goodsDetails) {
        this.goodsDetails = goodsDetails;
    }
}
