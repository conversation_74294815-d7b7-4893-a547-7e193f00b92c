import React, {Component} from 'react';
import {Form, Input, Modal, Switch} from 'antd'

/**
 * 流量规则添加弹窗
 */
class TrafficRuleAddModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };


    render() {
        return <Modal
            title="添加"
            open={this.state.visible}
            onOk={this.props.handleAddOk}
            onCancel={this.handleCancel}
            okText={'添加'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    label="流量规则名"
                    name="name"
                    rules={[{required: true, message: 'Please input your traffic rule name!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="日流量限制"
                    name="dailyLimit"
                    rules={[{required: true, message: 'Please input your dailyLimit!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="周流量限制"
                    name="weeklyLimit"
                    rules={[{required: true, message: 'Please input your weeklyLimit!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="月流量限制"
                    name="monthlyLimit"
                    rules={[{required: true, message: 'Please input your monthlyLimit!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="写入流量限制"
                    name="writeLimit"
                    rules={[{required: true, message: 'Please input your writeLimit!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="读取流量限制"
                    name="readLimit"
                    rules={[{required: true, message: 'Please input your readLimit!'}]}
                >
                    <Input/>
                </Form.Item>

                <Form.Item
                    label="是否可用"
                    name="enable"
                    valuePropName="checked"
                    initialValue={true}
                >
                    <Switch defaultChecked={true}/>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default TrafficRuleAddModal;
