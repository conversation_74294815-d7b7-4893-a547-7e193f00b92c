import {connect} from 'react-redux'
import LoadingMask from '../../../../components/loading/LoadingMask';

const mapStateToProps = (state, ownProps) => {
    //这里主要做redux状态status与组件prop的适配，如果store变化不导致组件更新的话，直接返回ownProps即可。
    return {
        show: state.loadingMask
    };
};

const Container = connect(
    mapStateToProps,
    null,
    null,
    {forwardRef: true}
)(LoadingMask);

export default Container;