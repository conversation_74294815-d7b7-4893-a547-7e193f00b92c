<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.fsockmanageweb.mapper.IServerMapper">
    <!-- <resultMap id="serverResultMap" type="java.util.HashMap">
         <id column="id" property="id"/>
         <result column="ip" property="ip"/>
         <result column="port" property="port"/>
         <result column="cert_port" property="certPort"/>
         <result column="shell_port" property="shellPort"/>
         <result column="username" property="username"/>
         <result column="password" property="password"/>
         <result column="group_name" property="groupName"/>
         <result column="user_count" property="userCount"/>
         <result column="created_by" property="createdBy"/>
         <result column="created_time" property="createdTime"/>
         <result column="updated_by" property="updatedBy"/>
         <result column="updated_time" property="updatedTime"/>
     </resultMap>-->

    <select id="listUserCountByGroupName" parameterType="String" resultType="java.lang.Long">
        select count(u.id) from tb_group g,tb_user u where g.id=u.group_id and g.name=#{groupName}
        and g.deleted=0 and u.deleted=0 and u.enabled=1
    </select>
    <select id="listBySearch" parameterType="String" resultType="com.cn.xiang.fsockmanageweb.po.ServerPo">
        select * from tb_server where
        (
        ip like concat('%',#{search},'%')
        or username like concat('%',#{search},'%')
        or password like concat('%',#{search},'%')
        or port like concat('%',#{search},'%')
        or group_name like concat('%',#{search},'%')
        )
        and deleted=0
    </select>
    <select id="findServerByUserId" parameterType="String" resultType="com.cn.xiang.fsockmanageweb.po.ServerPo">
        select s.* from tb_server s ,tb_group g,tb_user u
        where u.group_id=g.id and g.name=s.group_name and u.id=#{userId}
        and s.deleted=0 and u.deleted=0 and g.deleted=0
    </select>
</mapper>
