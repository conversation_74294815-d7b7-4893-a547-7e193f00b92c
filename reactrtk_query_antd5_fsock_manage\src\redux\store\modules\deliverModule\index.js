import React, {Component} from 'react';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Form, Input, Space, Table} from "antd";
import './index.less'

const {Column} = Table;
const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource', itemKeyName: 'key', isForwardedRef: false
});

class UserModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true,
            selectedRowKeys: [],
            page: 1,
            pageSize: 10,
        }
        this.tableRef = React.createRef();
        this.searchInput = React.createRef();
        this.searchInputFormRef = React.createRef();
    }

    selectedRows;
    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            btnDisabled: false,
            selectedRowKeys: selectedRowKeys,
        })
    }

    render() {
        return (<div>
                <Space style={{marginBottom: 16}} id={'user-module-ctn'}>
                    <Button disabled={this.state.btnDisabled} type="primary" onClick={this.props.deliverBtnOnClick.bind(this)}>
                        发货
                    </Button>
                    <Form ref={this.searchInputFormRef}
                          style={{width: '230px'}}
                          labelCol={{span: 0}}
                          wrapperCol={{span: 30}}
                          name="searchInputForm"
                    >
                        <Form.Item name='search'>
                            <Input.Search ref={this.searchInput} placeholder={'username/group/trafficRule'}
                                          defaultValue={''}
                                          onSearch={this.props.inputOnSearch.bind(this)}/>
                        </Form.Item>
                    </Form>
                </Space>
                <TableWrapper
                    ref={this.tableRef}
                    dataSource={this.props.dataSource}
                    rowSelection={{
                        type: 'radio',
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onChange
                    }}
                    pagination={{
                        total: this.props.total,
                        pageSizeOptions: [10, 20, 50, 100],
                        onChange: this.props.loadData.bind(this),
                        showTotal: total => `共${total}条记录 `,
                        defaultPageSize: 10,
                        defaultCurrent: 1,
                        current: this.state.page,
                        pageSize: this.state.pageSize,
                        position: ['bottomRight'],
                        size: 'small',
                        showSizeChanger: true,
                        showQuickJumper: true,
                    }}
                    scroll={{y: '650px'}}>
                    <Column align={"center"} title="用户名" dataIndex="username"/>
                    <Column align={"center"} title="密码" dataIndex="password"/>
                    <Column align={"center"} title="有效期" dataIndex="expiration"/>
                    <Column align={"center"} title="当月已用流量" dataIndex="userTrafficUsage"
                            render={(text, record, index) => {
                                let userTrafficUsage = record['userTrafficUsage'];
                                let userTrafficUsageGb = userTrafficUsage / 1000 / 1000 / 1000;
                                userTrafficUsageGb = Math.round(userTrafficUsageGb * 1000) / 1000;
                                return userTrafficUsageGb + ' GB';
                            }}/>
                    <Column align={"center"} title="是否可用" dataIndex="status" render={(status) => {
                        return status ? '是' : '否';
                    }}/>
                    <Column align={"center"} title="是否已售出" dataIndex="delivered" render={(delivered) => {
                        return delivered ? '是' : '否';
                    }}/>
                    <Column align={"center"} title="流量规则" dataIndex={'trafficRuleName'}
                    />
                    <Column align={"center"} title="组" dataIndex="groupName"
                    />
                </TableWrapper>
            </div>
        )
    }
}

export default UserModule;
