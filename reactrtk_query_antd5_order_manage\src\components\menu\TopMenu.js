import React, {Component} from 'react';
import Menu from './menu';


class TopMenu extends Component {
    constructor(props) {
        super(props);
        this.config = {
            style: {lineHeight: '64px'},
            theme: 'dark',
            mode: 'horizontal',
            defaultSelectedKeys: ['nav1'],
            //使用需要链接
            hasLink: false
        };
    }

    onItemClick = (e) => {
        let key = e.key;
        this.props.loadLeftMenu(key);
    }

    selectFirstItem = () => {
        //初始化时自动选中第一个菜单
        let liEle = document.querySelector('.header>ul[role="menu"]>.ant-menu-item:nth-child(2)');
        if (liEle) {
            liEle.click();
        }
    }

    componentDidMount() {
        this.props.componentDidMount && this.props.componentDidMount.apply(this);
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        //避免点击当前已选中的菜单触发重新加载
        if (prevProps.data !== this.props.data) {
            //加载左边菜单
            this.selectFirstItem();
        }
    }

    componentWillUnmount() {
        this.props.componentWillUnmount && this.props.componentWillUnmount.apply(this);
    }

    render() {
        return (<Menu data={this.props.data} config={this.config}
                      onItemClick={this.onItemClick}/>);
    }
}

export default TopMenu;
