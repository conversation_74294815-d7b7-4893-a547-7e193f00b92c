package com.cn.xiang.fsockmanageweb.po.typeHandler;

import com.cn.xiang.fsockmanageweb.po.TrafficRulePo;
import com.cn.xiang.fsockmanageweb.mapper.ITrafficRuleMapper;
import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/2/5 16:48
 */
public class TrafficRulePoTypeHandler implements TypeHandler<TrafficRulePo> {
    @Override
    public void setParameter(PreparedStatement ps, int i, TrafficRulePo parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getId());
    }

    @Override
    public TrafficRulePo getResult(ResultSet rs, String columnName) throws SQLException {
        String trafficRuleId = rs.getString(columnName);
        ITrafficRuleMapper trafficRuleMapper = ApplicationContextHolder.getInstance().getApplicationContext().getBean(ITrafficRuleMapper.class);
        return trafficRuleMapper.selectById(trafficRuleId);
    }

    @Override
    public TrafficRulePo getResult(ResultSet rs, int columnIndex) throws SQLException {
        String trafficRuleId = rs.getString(columnIndex);
        ITrafficRuleMapper trafficRuleMapper = ApplicationContextHolder.getInstance().getApplicationContext().getBean(ITrafficRuleMapper.class);
        return trafficRuleMapper.selectById(trafficRuleId);
    }

    @Override
    public TrafficRulePo getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return null;
    }
}
