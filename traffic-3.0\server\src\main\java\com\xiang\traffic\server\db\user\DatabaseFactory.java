package com.xiang.traffic.server.db.user;

import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.server.enumeration.DbTypeEnum;

/**
 * <AUTHOR>
 * @date 2024/2/5 23:39
 */
public class DatabaseFactory {

    public static UserDatabase createUserDatabase(DbTypeEnum dbType, ConfigManager<?> configManager) {
        switch (dbType) {
            case TEXT:
                return new TextUserDatabase(configManager);
            case MYBATIS_PLUS:
                return new MybaitsPlusUserDatabase(configManager);
            default:
                throw new IllegalArgumentException("不支持的数据库类型");
        }
    }
}
