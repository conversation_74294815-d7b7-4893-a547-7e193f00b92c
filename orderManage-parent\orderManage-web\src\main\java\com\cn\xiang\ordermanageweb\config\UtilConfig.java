package com.cn.xiang.ordermanageweb.config;

import com.cn.xiang.ordermanageweb.config.properties.AlipayConfigProperties;
import com.cn.xiang.ordermanageweb.spring.ApplicationContextHolder;
import com.cn.xiang.ordermanageweb.spring.support.ValidateExceptionResolver;
import org.hibernate.validator.HibernateValidator;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(AlipayConfigProperties.class)
public class UtilConfig {
    @Bean
    public ApplicationContextHolder configApplicationContextHolder() {
        return ApplicationContextHolder.getInstance();
    }

    @Bean
    public HibernateValidator hibernateValidator() {
        return new HibernateValidator();
    }

    @Bean
    public ValidateExceptionResolver validateExceptionResolver() {
        return new ValidateExceptionResolver();
    }
}
