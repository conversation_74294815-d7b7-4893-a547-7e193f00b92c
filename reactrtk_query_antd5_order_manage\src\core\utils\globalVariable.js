/*
* 全局变量，可以用来做页面参数传递
* */
import Utils from "./utils";

const globalDataObj = {};
/**
 * 设置变量
 * @param keys  如['aaa','bbb','ccc']
 * @param obj
 */
const set = (keys = [], obj) => {
    if (!obj) {
        return;
    }

    let tmpObj = globalDataObj;
    let preObj = globalDataObj;
    let preKey;
    for (let key of keys) {
        if (!tmpObj[key]) {
            tmpObj[key] = {};
        }
        preObj = tmpObj;
        preKey = key;
        tmpObj = tmpObj[key];
    }
    preObj[preKey] = obj;
};
/**
 * 获取变量，如果key没传，则返回这个globalDataObj
 * @param keys []
 * @returns {*}
 */
const get = (keys) => {
    if (!keys) {
        return Utils.deepClone(globalDataObj);
    }

    let result = globalDataObj;
    for (let key of keys) {
        if (!result[key]) {
            return undefined;
        } else {
            result = result[key];
        }
    }

    // if (!result || (result && result.constructor.name === 'String')) {
    //     return result;
    // }
    //返回一份复制的对象，避免用户直接修改到globalDataObj
    return Utils.deepClone(result);
};
/**
 * 移除变量
 * @param keys
 * @returns {boolean}
 */
const remove = (keys) => {
    let obj = globalDataObj;
    let preKey;
    let preObj = globalDataObj;
    for (let key of keys) {
        if (!obj[key]) {
            return false;
        } else {
            preKey = key;
            preObj = obj;
            obj = obj[key];
        }
    }

    return delete preObj[preKey];
};

let result = {
    set,
    get,
    remove,
};

export default result;
