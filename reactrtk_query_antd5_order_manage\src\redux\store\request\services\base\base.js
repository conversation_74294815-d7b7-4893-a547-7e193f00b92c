import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react'
import Utils from "../../../../../core/utils/utils";
import Constants from "../../../../../core/utils/constants";

/**
 * 转换response中的data
 * @param response
 * @param meta
 * @param arg
 * @returns {string}
 */
const transformResponse = (response = {content: ''}, meta, arg) => {
    return response.content;
};

/**
 * 请求中添加遮罩
 * @param arg
 * @param dispatch
 * @param getState
 * @param extra
 * @param requestId
 * @param queryFulfilled
 * @param getCacheEntry
 * @param updateCachedData
 * @returns {Promise<void>}
 */
const onQueryStarted = async (arg,
                              {
                                  dispatch,
                                  getState,
                                  extra,
                                  requestId,
                                  queryFulfilled,
                                  getCacheEntry,
                                  updateCachedData,
                              }) => {
    //500毫秒每执行完请求，开启遮罩
    window.setTimeout(() => {
        Utils.getStatePromise(queryFulfilled).then((state) => {
            if (state === Constants.PROMISE_STATE.PENDING) {
                window.global.store.dispatch(window.global.actions.loadingMask.showMask(true));
            }
        })
    }, 500);

    //等待请求完成
    await queryFulfilled;
    window.global.store.dispatch(window.global.actions.loadingMask.showMask(false));
}

let isDevelopment = process.env.NODE_ENV === 'development';
let baseUrl;
if (isDevelopment) {
    baseUrl = '/api_v1';
} else {
    // baseUrl = 'http://localhost:8080/dev/api_v1';
    // baseUrl = 'http://**************/dev/api_v1';
    baseUrl = '/dev/api_v1';
}
Utils.BaseUrl.setBaseUrl(baseUrl);

export const baseApi = createApi({
    reducerPath: 'baseApi',
    baseQuery: fetchBaseQuery({baseUrl: baseUrl}),
    endpoints: (builder) => {
        let apis = {};

        //获取所有api文件
        const files = require.context('../../services', false, /\.js$/);
        let modules = files.keys().map((key) => {
            const fileName = key.split('/').pop(); // 提取文件名部分
            return {
                module: files(key),
                name: fileName, // 文件名(带后缀)
            };
        });

        modules.forEach((module) => {
            apis = Object.assign(apis, module.module.default(builder, onQueryStarted, transformResponse));
        });

        return apis;
    },
})
// export const {
//     useGetTableDataQuery,
//     useGetTopMenuDataQuery,
//     useGetLeftMenuDataQuery,
//     useGetLineChartDataQuery,
//     useGetLineChartPushDataQuery,
// } = baseApi;
