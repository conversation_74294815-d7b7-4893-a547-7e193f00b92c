<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c354cf3d-e2ce-444b-9cab-3b10b15e61dc" name="Changes" comment="修复数据库连接报错">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/traffic-3.0/client/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/traffic-3.0/client/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/traffic-3.0/client/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/traffic-3.0/client/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="f1057ea4c061c7ae70c864ce7a2aae3f3b34cda7" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="MavenImportPreferences">
    <option name="explicitlyEnabledProfiles" value="dev" />
    <option name="explicitlyDisabledProfiles" value="prod" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2sA67GVNp9u7HFIAmHn3ZlOGO5c" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.ClientBoot.executor&quot;: &quot;Debug&quot;,
    &quot;Application.ServerBoot.executor&quot;: &quot;Run&quot;,
    &quot;Maven.fsockManage-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.fsockManage-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.fsockManage-parent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.fsockManage-web [org.springframework.boot:spring-boot-maven-plugin:3.4.1:process-aot].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-parent [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-parent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-server [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-server [deploy].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-server [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.traffic-server [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.Bootstrap (1).executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.Bootstrap (2).executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.Bootstrap.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/ideaWorkspace/workspace__/ideaWK/springbootWk/fsock_manage/traffic-3.0&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\IntelliJ IDEA 2024.1.7\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\ideaWorkspace\workspace__\ideaWK\springbootWk\fsock_manage\traffic-3.0" />
      <recent name="E:\ideaWorkspace\workspace__\ideaWK\springbootWk\fsock_manage\traffic-3.0\server\src\main\zipBak" />
      <recent name="E:\ideaWorkspace\workspace__\ideaWK\springbootWk\fsock_manage\traffic-3.0\server\src\main\docker" />
      <recent name="E:\ideaWorkspace\workspace__\ideaWK\springbootWk\fsock_manage\traffic-3.0\server\src\main\batch" />
      <recent name="E:\ideaWorkspace\workspace__\ideaWK\springbootWk\fsock_manage\reactrtk_query_antd5_order_manage" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\ideaWorkspace\workspace__\ideaWK\springbootWk\fsock_manage\traffic-3.0\client" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Application.ServerBoot">
    <configuration name="ClientBoot" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xiang.traffic.client.ClientBoot" />
      <module name="traffic-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.traffic.client.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServerBoot" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xiang.traffic.server.ServerBoot" />
      <module name="traffic-server" />
      <option name="VM_PARAMETERS" value="-Dflyingsocks.config.location=E:\ideaWorkspace\workspace__\ideaWK\springbootWk\fsock_manage\traffic-3.0\server\src\main\resources" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xiang.traffic.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Bootstrap (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="orderManage-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cn.xiang.ordermanageweb.Bootstrap" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.cn.xiang.ordermanageweb.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Bootstrap" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="fsockManage-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cn.xiang.fsockmanageweb.Bootstrap" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.cn.xiang.fsockmanageweb.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EurekaServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="eureka-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cn.xiang.eurekaserver.EurekaServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.ClientBoot" />
      <item itemvalue="Application.ServerBoot" />
      <item itemvalue="Spring Boot.EurekaServerApplication" />
      <item itemvalue="Spring Boot.Bootstrap (1)" />
      <item itemvalue="Spring Boot.Bootstrap" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.Bootstrap" />
        <item itemvalue="Spring Boot.Bootstrap (1)" />
        <item itemvalue="Application.ServerBoot" />
        <item itemvalue="Application.ClientBoot" />
        <item itemvalue="Application.ServerBoot" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task id="LOCAL-00001" summary="修复打包后首页无法访问。securityconfig">
      <option name="closed" value="true" />
      <created>1737881482334</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1737881482334</updated>
    </task>
    <task id="LOCAL-00002" summary="docker compose build执行时，docker配置的代理没效果，所以先执行pull">
      <option name="closed" value="true" />
      <created>1737885618801</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1737885618801</updated>
    </task>
    <task active="true" id="Default" summary="Default task">
      <changelist id="c354cf3d-e2ce-444b-9cab-3b10b15e61dc" name="Changes" comment="docker compose build执行时，docker配置的代理没效果，所以先执行pull" />
      <created>1737885864431</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1737885864431</updated>
      <workItem from="1737885865558" duration="246000" />
      <workItem from="1737901619579" duration="1680000" />
      <workItem from="1737991064039" duration="1150000" />
      <workItem from="1738038190749" duration="29464000" />
      <workItem from="1738547196206" duration="27000" />
      <workItem from="1738574528318" duration="1267000" />
      <workItem from="1738588008835" duration="4542000" />
      <workItem from="1738637060387" duration="742000" />
      <workItem from="1738639255720" duration="603000" />
      <workItem from="1738652311307" duration="729000" />
      <workItem from="1738898069703" duration="667000" />
      <workItem from="1739428621743" duration="1102000" />
      <workItem from="1739429736198" duration="811000" />
      <workItem from="1739455629674" duration="1984000" />
      <workItem from="1739711026666" duration="2491000" />
      <workItem from="1740031813724" duration="2993000" />
      <workItem from="1740057499948" duration="424000" />
      <workItem from="1740117248526" duration="169000" />
      <workItem from="1740117845513" duration="249000" />
      <workItem from="1740122900534" duration="23000" />
      <workItem from="1740123476236" duration="13000" />
      <workItem from="1740125488748" duration="6000" />
      <workItem from="1744441956870" duration="2829000" />
      <workItem from="1745237255524" duration="254000" />
      <workItem from="1745830492942" duration="66000" />
      <workItem from="1747292023926" duration="797000" />
      <workItem from="1747907169855" duration="1462000" />
      <workItem from="1747919242338" duration="603000" />
      <workItem from="1747921380898" duration="11000" />
      <workItem from="1748183613217" duration="57000" />
      <workItem from="1748611142841" duration="1186000" />
      <workItem from="1748662933738" duration="1511000" />
      <workItem from="1748665727986" duration="3480000" />
      <workItem from="1748672057035" duration="43000" />
      <workItem from="1753002900192" duration="311000" />
      <workItem from="1753003921916" duration="5043000" />
      <workItem from="1753014886455" duration="55000" />
      <workItem from="1753125667863" duration="73000" />
      <workItem from="1753126396461" duration="300000" />
      <workItem from="1753365326293" duration="3192000" />
      <workItem from="1753368642907" duration="188000" />
      <workItem from="1753368839286" duration="2041000" />
      <workItem from="1753370916062" duration="1003000" />
    </task>
    <task id="LOCAL-00003" summary="docker compose build执行时，docker配置的代理没效果，所以先执行pull">
      <option name="closed" value="true" />
      <created>1737886044065</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1737886044065</updated>
    </task>
    <task id="LOCAL-00004" summary="更新moduleTab的icon">
      <option name="closed" value="true" />
      <created>1739457078842</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1739457078842</updated>
    </task>
    <task id="LOCAL-00005" summary="更新package.json中的依赖">
      <option name="closed" value="true" />
      <created>1740032994702</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1740032994702</updated>
    </task>
    <task id="LOCAL-00006" summary="更新package.json中的依赖">
      <option name="closed" value="true" />
      <created>1740033887092</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1740033887092</updated>
    </task>
    <task id="LOCAL-00007" summary="修复数据库连接报错">
      <option name="closed" value="true" />
      <created>1748669195228</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1748669195228</updated>
    </task>
    <task id="LOCAL-00008" summary="修复数据库连接报错">
      <option name="closed" value="true" />
      <created>1753008832338</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753008832338</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="34bf022f-5086-4b5f-96ec-2590bbccd421" value="TOOL_WINDOW" />
        <entry key="72e09fc8-4e40-4f82-8109-86c74e24d046" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="34bf022f-5086-4b5f-96ec-2590bbccd421">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:E:/ideaWorkspace/workspace__/ideaWK/springbootWk/fsock_manage/traffic-3.0" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="72e09fc8-4e40-4f82-8109-86c74e24d046">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:E:/ideaWorkspace/workspace__/ideaWK/springbootWk/fsock_manage/traffic-3.0" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修复打包后首页无法访问。securityconfig" />
    <MESSAGE value="docker compose build执行时，docker配置的代理没效果，所以先执行pull" />
    <MESSAGE value="更新moduleTab的icon" />
    <MESSAGE value="更新package.json中的依赖" />
    <MESSAGE value="修复数据库连接报错" />
    <option name="LAST_COMMIT_MESSAGE" value="修复数据库连接报错" />
  </component>
</project>