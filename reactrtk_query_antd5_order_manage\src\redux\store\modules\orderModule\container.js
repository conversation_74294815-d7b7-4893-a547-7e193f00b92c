import UserModule from './index';
import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getProductById', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            product: data.product
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期
            let productId = moduleParam['productId'];

            const promise = dispatch(baseApi.endpoints.getProductById.initiate({
                productId: productId
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            promise.then(({data, isSuccess}) => {
                if (isSuccess) {
                    let product = data.product;
                    this.setState({
                        totalPrice: product.price
                    });
                }
            });
            const {unsubscribe} = promise;
            apiUnsubscribeSet.add(unsubscribe);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
        },
        confirmOnClick: function (e) {
            let productId = this.props.product.id;
            dispatch(baseApi.endpoints.postOrderCreate.initiate({
                orderType: this.state.orderType,
                productId: productId
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            })).then(({data, isSuccess}) => {
                if (isSuccess) {
                    if (data.status) {
                        window.global.Notification.open({
                            type: window.global.Notification.TYPE.SUCCESS,
                            message: "创建订单成功.",
                            delay: 3,
                        });

                        window.open('api_v1/toPay', '_blank');
                    } else {
                        window.global.Notification.open({
                            type: window.global.Notification.TYPE.ERROR,
                            message: data.errorMsg,
                            delay: 3,
                        });
                    }
                }
            });
        }
    };
};

let Container = ModuleContainerCreator.create(UserModule, mapStateToProps, mapDispatchToProps);
export default Container;
