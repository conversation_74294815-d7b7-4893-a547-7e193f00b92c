import UserModule from './index';
import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import SimpleRouter from "../../../../core/router/smpleRouter";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getProductListData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            productList: data.list
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期
            const {unsubscribe} = dispatch(baseApi.endpoints.getProductListData.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
            const {unsubscribe} = dispatch(baseApi.endpoints.getProductListData.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
        },
        onBuyBtnClick: (e) => {
            let parent = e.target;
            while (parent.nodeName !== 'BUTTON') {
                parent = parent.parentNode;
            }

            let productId = parent.dataset.productId;
            SimpleRouter.open('订单详情', "/orderModule", {productId: productId});
        }
    };
};

let Container = ModuleContainerCreator.create(UserModule, mapStateToProps, mapDispatchToProps);
export default Container;
