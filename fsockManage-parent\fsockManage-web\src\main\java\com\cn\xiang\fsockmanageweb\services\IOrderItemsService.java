package com.cn.xiang.fsockmanageweb.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.OrderItemsPo;

/**
 * <AUTHOR>
 * @date 2024/4/10 21:45
 */

public interface IOrderItemsService extends IService<OrderItemsPo> {
    OrderItemsPo getOne1(LambdaQueryWrapper<OrderItemsPo> eq);

}
