package com.xiang.traffic.server.core.token;

import com.xiang.traffic.server.db.vo.DbUserVo;
import com.xiang.traffic.server.enumeration.ClientAuthType;

/**
 * <AUTHOR>
 * @date 2024/2/6 21:23
 */
public class UserToken extends AbstractAuthToken<DbUserVo> {

    public UserToken(DbUserVo principle, String remoteIp) {
        super(principle, remoteIp);
    }

    @Override
    public ClientAuthType getTokenType() {
        return ClientAuthType.USER;
    }
}
