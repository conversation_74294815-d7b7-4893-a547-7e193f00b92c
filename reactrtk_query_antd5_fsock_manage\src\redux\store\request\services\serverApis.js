const url = 'server';

let transformResponse_ = (response = {content: ''}, meta, arg) => {
    return response;
};
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getServerListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '?search=' + queryArg.search,
                    method: 'GET'
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let list = content.list;
                for (let i = 0; i < list.length; i++) {
                    list[i].key = list[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
        postServerData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'POST',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        putServerData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'PUT',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        delServerData: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url,
                    method: 'DELETE',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        uploadZipToServer: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                formData.set('packageResourceId', queryArg.packageResourceId);
                return {
                    url: url + '/uploadZipToServer',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse_,
            onQueryStarted: onQueryStarted
        }),
        modifyServerConfig: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url + '/modifyServerConfig',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse_,
            onQueryStarted: onQueryStarted
        }),
        installDockerToServer: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url + '/installDockerToServer',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse_,
            onQueryStarted: onQueryStarted
        }),
        startServer: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url + '/startServer',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse_,
            onQueryStarted: onQueryStarted
        }),
        autoStart: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                formData.set('packageResourceId', queryArg.packageResourceId);
                return {
                    url: url + '/autoStart',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse_,
            onQueryStarted: onQueryStarted
        }),
        stopServer: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url + '/stopServer',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse_,
            onQueryStarted: onQueryStarted
        }),
    }
}
