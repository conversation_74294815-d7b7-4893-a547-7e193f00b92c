import React, {Component} from 'react';
import {Form, Input, Modal, Switch, DatePicker} from 'antd'

/**
 * 用户添加弹窗
 */
class UserAddModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };

    render() {
        return <Modal
            title="添加"
            open={this.state.visible}
            onOk={this.props.handleAddOk}
            onCancel={this.handleCancel}
            okText={'添加'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    label="用户名"
                    name="username"
                    rules={[{required: true, message: 'Please input your username!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="密码"
                    name="password"
                    rules={[{required: true, message: 'Please input your password!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="有效日期"
                    name="expiration"
                    rules={[{required: true, message: 'Please input expiration!'}]}
                >
                    <DatePicker
                        showTime
                    />
                </Form.Item>
                <Form.Item
                    label="组名（可选）"
                    name="groupName"
                    rules={[{required: false, message: 'Please input groupName!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="是否可用"
                    name="enabled"
                    valuePropName="checked"
                    initialValue={true}
                >
                    <Switch defaultChecked={true}/>
                </Form.Item>
                <Form.Item
                    label="是否永久"
                    name="noExpiration"
                    valuePropName="checked"
                    initialValue={false}
                >
                    <Switch defaultChecked={false}/>
                </Form.Item>
                <Form.Item
                    label="自动生成用户"
                    name="autoGenerateUser"
                    valuePropName="checked"
                    initialValue={false}
                >
                    <Switch defaultChecked={false} onChange={this.props.handleAutoGenerateUserChange}/>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default UserAddModal;
