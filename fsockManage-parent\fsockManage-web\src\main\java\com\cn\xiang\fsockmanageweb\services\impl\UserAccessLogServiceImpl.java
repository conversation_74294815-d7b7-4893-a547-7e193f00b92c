package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.po.UserAccessLogPo;
import com.cn.xiang.fsockmanageweb.po.UserPo;
import com.cn.xiang.fsockmanageweb.utils.JsonUtils;
import com.cn.xiang.fsockmanageweb.mapper.IUserAccessLogMapper;
import com.cn.xiang.fsockmanageweb.services.IUserAccessLogService;
import com.cn.xiang.fsockmanageweb.services.IUserService;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5 23:13
 */
@Service
@Transactional
public class UserAccessLogServiceImpl extends ServiceImpl<IUserAccessLogMapper, UserAccessLogPo> implements IUserAccessLogService {
    private static final int BYTE_TO_MEGABYTE = 1024;
    @Autowired
    private IUserService userService;

    @Override
    public String userEnter(String userId, String address) {
        UserAccessLogPo userAccessLogPo = new UserAccessLogPo();
        userAccessLogPo.setUserId(userId);
        userAccessLogPo.setAddress(address);
        userAccessLogPo.setConnectTime(LocalDateTime.now());

        save(userAccessLogPo);
        return userAccessLogPo.getId();
    }

    @Override
    public void userLeave(String accessLogId, long uploadTrafficUsage, long downloadTrafficUsage) {
        UserAccessLogPo userAccessLog = getById(accessLogId);
        userAccessLog.setDisconnectTime(LocalDateTime.now());
        userAccessLog.setUploadTrafficUsage(uploadTrafficUsage);
        userAccessLog.setUploadTrafficUsageHuman(uploadTrafficUsage / BYTE_TO_MEGABYTE / BYTE_TO_MEGABYTE + "MB");
        userAccessLog.setDownloadTrafficUsage(downloadTrafficUsage);
        userAccessLog.setDownloadTrafficUsageHuman(downloadTrafficUsage / BYTE_TO_MEGABYTE / BYTE_TO_MEGABYTE + "MB");

        updateById(userAccessLog);
    }

    @Override
    public long getUserDownloadTrafficUsage(String username) {
        return this.baseMapper.getUserDownloadTrafficUsage(username);
    }

    @Override
    public Page<ObjectNode> findPagesWithUsername(PageDTO<UserAccessLogPo> pageDTO) {
        List<UserAccessLogPo> userAccessLogPoList = this.list(pageDTO);
        List<ObjectNode> list = new ArrayList<>();
        Page<ObjectNode> page = PageDTO.of(pageDTO.getCurrent(), pageDTO.getSize(), pageDTO.getTotal());
        for (UserAccessLogPo userAccessLogPo : userAccessLogPoList) {
            ObjectNode objNode = JsonUtils.createJsonObjectNode();
            try {
                BeanInfo beanInfo = Introspector.getBeanInfo(UserAccessLogPo.class, Object.class);
                for (PropertyDescriptor pd : beanInfo.getPropertyDescriptors()) {
                    String name = pd.getName();
                    objNode.set(name, JsonUtils.beanToJsonNode(pd.getReadMethod().invoke(userAccessLogPo)));

                    UserPo userPo = userService.getById(userAccessLogPo.getUserId());
                    if (userPo != null) {
                        objNode.set("username", JsonUtils.beanToJsonNode(userPo.getUsername()));
                    }
                }
                list.add(objNode);
            } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                throw new RuntimeException(e);
            }
        }
        page.setRecords(list);
        return page;
    }
}
