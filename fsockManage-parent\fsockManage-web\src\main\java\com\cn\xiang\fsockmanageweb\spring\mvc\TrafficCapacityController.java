package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.po.TrafficCapacityPo;
import com.cn.xiang.fsockmanageweb.services.ITrafficCapacityService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api_v1/trafficCapacity")
public class TrafficCapacityController extends ControllerSupport {
    @Autowired
    private ITrafficCapacityService trafficRuleService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        List<TrafficCapacityPo> list = trafficRuleService.list();

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }
}
