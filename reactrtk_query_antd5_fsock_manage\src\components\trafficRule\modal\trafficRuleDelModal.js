import React, {Component} from 'react';
import {Modal} from 'antd'

/**
 * 流量规则删除弹窗
 */
class TrafficRuleDelModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            visible: false
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };

    render() {
        return <Modal
            title="删除"
            open={this.state.visible}
            onOk={this.props.handleDelOk}
            onCancel={this.handleCancel}
            okText={'删除'}
            cancelText={'取消'}>
            <p>确定要删除该流量规则？</p>
        </Modal>
    }
}

export default TrafficRuleDelModal;
