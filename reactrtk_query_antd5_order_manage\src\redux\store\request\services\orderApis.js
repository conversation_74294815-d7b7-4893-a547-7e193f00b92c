const url = 'order';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        postOrderCreate: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('orderType', queryArg.orderType);
                formData.set('productId', queryArg.productId);
                return {
                    url: url + '/create',
                    method: 'post',
                    body: formData,
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                return response;
            },
            onQueryStarted: onQueryStarted
        }),
        getOrderListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'GET',
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let list = content.list;
                for (let i = 0; i < list.length; i++) {
                    list[i].key = list[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
        cancelOrder: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('orderId', queryArg.orderId);
                return {
                    url: url + '/cancel',
                    method: 'POST',
                    body: formData
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        getDeliverDetailData: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/deliverDetail?' + 'orderId=' + queryArg.orderId,
                    method: 'GET',
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
