import './indexPage.less';
import React, {Component} from 'react';
import {UserOutlined} from '@ant-design/icons';
import {Layout} from 'antd';
// import LoadingMask from "../../redux/store/components/loadingMask/container"
import LoadingMask from "../../components/loading/LoadingMaskFn"
import TopMenu from "../../redux/store/components/menu/topMenu/container"
import LeftMenu from "../../redux/store/components/menu/leftMenu/container"
import UserImage from '../../components/userImage/container'
import ModulePage from '../../components/modulePage/ModulePage'

const {Header, Sider} = Layout;

class IndexPage extends Component {
    constructor(props) {
        super(props);

        this.state = {
            collapsed: false,
        }
    }

    onCollapse = (collapsed) => {
        this.setState({collapsed});
    };

    render() {
        return (
            <Layout className={'index-layout-ctn'}>
                <Header className="header">
                    <div className="logo"/>
                   {/* <TopMenu/>*/}
                    <div className='user-image-ctn'>
                        <UserImage avatorProps={{size: 50, icon: <UserOutlined/>}}/>
                    </div>
                </Header>
                <Layout>
                    <Sider collapsible collapsed={this.state.collapsed} onCollapse={this.onCollapse} width={200}>
                        <LeftMenu/>
                    </Sider>
                    <Layout>
                        <ModulePage/>
                        <LoadingMask show={false}/>
                    </Layout>
                </Layout>
            </Layout>
        )
    }
}

export default IndexPage;
