import React from 'react'
import * as echarts from "echarts";

/**
 * echarts图表整合适配
 */
class EChartAdapter extends React.Component {
    eChart;
    eChartCtnRef;

    constructor(props) {
        super(props);
        this.eChartCtnRef = React.createRef();
    }

    init = () => {
        if (this.props.id) {
            this.eChart = echarts.init(document.getElementById(this.props.id));
        } else {
            this.eChart = echarts.init(this.eChartCtnRef.current);
        }

        this.props.eChartInitCallback && this.props.eChartInitCallback(echarts, this.eChart);

        // 页面resize时，调用图表resize
        window.addEventListener('resize', () => {
            this.eChart.resize();
        });

        //通过图表resize解决canvas无法占满容器的问题
        window.setTimeout(() => {
            this.eChart.resize();
        }, 50);

        this.setOption(this.props.option);
    };

    setOption = (option = {}) => {
        this.eChart.setOption(option);
    };

    componentDidMount() {
        this.init();
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        this.setOption(this.props.option);
        //修复canvas无法占满容器的问题
        if(!this.eChart.isDisposed()){
            this.eChart.resize();
        }
    }

    componentWillUnmount() {
        this.eChart.dispose();
    }

    render() {
        let {option, eChartInitCallback, ...props_} = this.props;
        return <div ref={this.eChartCtnRef} {...props_}/>
    }
}

export default EChartAdapter;
