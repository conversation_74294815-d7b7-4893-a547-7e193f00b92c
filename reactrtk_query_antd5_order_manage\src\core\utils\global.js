import {message,notification} from 'antd';
import store from '../../redux/storeConfig';
import actions_ from "../../redux/actions";
import moduleRegister from '../module/register/moduleRegister'
import SimpleRouter from '../router/smpleRouter'
import GlobalVariable from './globalVariable'
import LocalStorageVariable from "./LocalStorageVariable";
import Utils from './utils'
import Constants from './constants'
import reducersMap from '../../redux/reducers'
import {
    combineReducers
} from 'redux'

let actions = actions_;
/**
 * 通知
 * @type {{open: open, TYPE: {SUCCESS: string, INFO: string, WARNING: string, ERROR: string}, DIRECTION: {TOP_LEFT: string, TOP_RIGHT: string, BOTTOM_LEFT: string, BOTTOM_RIGHT: string}}}
 */
const Notification = (() => {
    let target =
        {
            open: (options) => {
                // options = {
                //     type: Notification.TYPE.SUCCESS,
                //     direction: Notification.DIRECTION.TOP_RIGHT,
                //     message: 'test',
                //     description: 'desc'
                // }

                notification.config({
                    placement: options.direction || target.DIRECTION.TOP_RIGHT
                });

                notification[options.type]({
                    message: options.message,
                    description: options.description,
                });
            },
            TYPE: {
                SUCCESS: 'success',
                INFO: 'info',
                WARNING: 'warning',
                ERROR: 'error'
            },
            DIRECTION: {
                TOP_LEFT: 'topLeft',
                TOP_RIGHT: 'topRight',
                BOTTOM_LEFT: 'bottomLeft',
                BOTTOM_RIGHT: 'bottomRight'
            }
        };
    return target;
})();

/**
 * 全局消息
 * @type {{open: (function(*): *), config: config, TYPE: {SUCCESS: string, WARNING: string, INFO: string, LOADING: string, ERROR: string}}}
 */
const GlobalMessage = (() => {
    let target = {
        /**打开全局消息
         * @param options
         * @returns {*}
         */
        open: (options) => {
            // options = {
            //     type: GlobalMessage.TYPE.SUCCESS,
            //     content:"test",
            //     delay: 3,
            //     onClose: (() => {
            //         console.log('message close');
            //     })
            // };
            let paramsArg = [
                options.content,
                options.delay || 3,
                options.onClose
            ];
            return message[options.type].apply(message, paramsArg);
        },
        /**
         * 配置全局消息
         * @param config
         */
        config: (config) => {
            // config = {
            //     top: 100,
            //     duration: 2,
            //     maxCount: 3,
            //     //配置渲染位置
            //     getContainer : ()=>document.body
            // }
            message.config(config);
        },
        TYPE: {
            SUCCESS: 'success',
            WARNING: 'warning',
            INFO: 'info',
            LOADING: 'loading',
            ERROR: 'error'
        }
    };
    return target;
})();

/**
 * 模型参数相关
 * @type {{setModuleParam, getModuleParam, removeModuleParam}}
 */
const ModuleParam = (() => {
    /**
     * 设置模型参数
     * @param moduleId
     * @param moduleParam
     */
    const setModuleParam = (moduleId, moduleParam) => {
        GlobalVariable.set([Constants.MODULE_PARAM_KEY, moduleId], moduleParam);
    };
    /**
     * 获取模型参数
     * @param moduleId
     * @returns {*}
     */
    const getModuleParam = (moduleId) => {
        return GlobalVariable.get([Constants.MODULE_PARAM_KEY, moduleId]);
    };
    /**
     * 清除模型参数
     * @param moduleId
     */
    const removeModuleParam = (moduleId) => {
        GlobalVariable.remove([Constants.MODULE_PARAM_KEY, moduleId]);
    };

    return {
        setModuleParam,
        getModuleParam,
        removeModuleParam
    }
})();

/**
 * 管理reducer注册
 */
const DynamicReducerManager = (() => {
    let baseReducers = Object.assign({}, reducersMap);

    /**
     *注册reducer
     * @param reducer
     * eg:
     * {
     *     'dynamic_router_tableModule2Action' : {
     *         renderModule: function(){}
     *     }
     * }
     */
    const registerReducer = (reducer) => {
        if (reducer) {
            let keys = Object.keys(reducer);
            if (keys.length === 1) {
                let key = keys[0];
                if (baseReducers[key]) {
                    throw new Error('Current reducer has duplicate registration！');
                } else {
                    baseReducers = Object.assign({}, baseReducers, reducer)
                    window.global.store.replaceReducer(combineReducers(baseReducers));
                }
            }
        }
    }
    /**
     * 取消注册reducer
     * @param reducerKey
     * eg:  dynamic_router_tableModule2Action
     */
    const unregisterReducer = (reducerKey) => {
        if (reducerKey) {
            delete baseReducers[reducerKey];
            window.global.store.replaceReducer(combineReducers(baseReducers));
        }
    }

    return {
        registerReducer,
        unregisterReducer
    };
})()

let obj = {
    moduleRegister,
    store,
    actions,
    Notification,
    GlobalMessage,
    Constants,
    SimpleRouter,
    GlobalVariable,
    LocalStorageVariable,
    Utils,
    ModuleParam,
    DynamicReducerManager
}

export default obj;
