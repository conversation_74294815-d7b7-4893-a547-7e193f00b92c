const url = 'orders';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getOrderListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'GET',
                    body: queryArg,
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let list = content.list;
                for (let i = 0; i < list.length; i++) {
                    list[i].key = list[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),

        orderDeliver: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.append('orderId', queryArg.orderId);
                formData.append('userId', queryArg.userId);
                return {
                    url: url + '/deliver',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: (response) => response,
            onQueryStarted: onQueryStarted
        }),
        getDeliverDetailData: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/deliverDetail?' + 'orderId=' + queryArg.orderId,
                    method: 'GET',
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        deliverConfirm: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.append('orderId', queryArg.orderId);
                return {
                    url: url + '/deliverConfirm',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: (response)=>response,
            onQueryStarted: onQueryStarted
        }),
    }
}
