/**
 * 模型状态
 * @type {{BEFORE_LOAD: string, LOADED: string}}
 */
const MODULE_STATE = {
    BEFORE_LOAD: 'before_load',
    LOADED: 'loaded',
}

/**
 * 请求模式
 * @type {{ONLY_STORE: string, ONLY_CALLBACK: string, CALLBACK_STORE: string}}
 */
const FETCH_MODE = {
    //只调用successCallback，不会向store.fetchBySubreddit存储请求数据
    CALLBACK_ONLY: 'callback_only',
    //只向store.fetchBySubreddit存储请求数据，不调用successCallback
    STORE_ONLY: 'store_only',
    //调用successCallback，同时向store.fetchBySubreddit存储请求数据
    STORE_CALLBACK: 'store_callback',
}

/**
 * promise状态
 * @type {{FULFILLED: string, PENDING: string, REJECTED: string}}
 */
const PROMISE_STATE = {
    PENDING: 'pending',
    FULFILLED: 'fulfilled',
    REJECTED: 'rejected'
}

/**
 * 常量
 * @type {{contextPath: string}}
 */
const Constants = {
    contextPath: process.env.NODE_ENV === 'development' ? '' : '/dev',
    MODULE_PARAM_KEY: '_moduleParam_',
    MODULE_STATE: MODULE_STATE,
    FETCH_MODE: FETCH_MODE,
    PROMISE_STATE: PROMISE_STATE,
};

export default Constants;