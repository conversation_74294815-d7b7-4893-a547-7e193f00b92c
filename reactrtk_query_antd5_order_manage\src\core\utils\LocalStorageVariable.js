/**
 * 设置变量
 * @param keys  如['aaa','bbb','ccc']
 * @param obj
 */
const set = (keys = [], obj) => {
    if (keys && keys.length > 0) {
        if (keys.length === 1) {
            localStorage.setItem(keys[0], obj);
        } else {
            let root = JSON.parse(localStorage.getItem(keys[0])) || {};
            let preObj = root;
            let tmpObj = root;
            let preKey = keys[0];

            let key0Arr = keys.splice(0, 1);
            for (let key of keys) {
                if (!tmpObj[key] || (tmpObj[key] && tmpObj[key].constructor.name !== 'Object')) {
                    tmpObj[key] = {};
                }
                preObj = tmpObj;
                preKey = key;
                tmpObj = tmpObj[key];
            }
            preObj[preKey] = obj;
            localStorage.setItem(key0Arr[0], JSON.stringify(root));
        }
    }
};
/**
 * 获取变量，如果key没传，则返回这个globalDataObj
 * @param keys []
 * @returns {*}
 */
const get = (keys) => {
    if (keys && keys.length > 0) {
        if (keys.length === 1) {
            let result = localStorage.getItem(keys[0]);
            try {
                return JSON.parse(result);
            } catch (e) {
                return result;
            }
        } else {
            let obj = JSON.parse(localStorage.getItem(keys[0]));
            keys.splice(0, 1);

            for (let key of keys) {
                if (!obj[key]) {
                    return undefined;
                } else {
                    obj = obj[key];
                }
            }
            return obj;
        }
    } else {
        //返回整个localstore对象
        let obj = {};
        Reflect.ownKeys(localStorage).forEach((key) => {
            let result = localStorage.getItem(key);
            try {
                obj[key] = JSON.parse(result);
            } catch (e) {
                obj[key] = result;
            }
        });
        return obj;
    }
}

/**
 * 移除变量
 * @param keys
 * @returns {boolean}
 */
// const remove = (keys) => {
//     if (keys && keys.length > 0) {
//         if (keys.length === 1) {
//             localStorage.removeItem(keys[0]);
//         } else {
//             let root = localStorage.getItem(keys[0]);
//             keys.splice(0, 1);
//             let preKey;
//             let preObj = root;
//             for (let key of keys) {
//                 if (!obj[key]) {
//                     return false;
//                 } else {
//                     preKey = key;
//                     preObj = obj;
//                     obj = obj[key];
//                 }
//             }
//         }
//     }
//
//     let obj = globalDataObj;
//     let preKey;
//     let preObj = globalDataObj;
//     for (let key of keys) {
//         if (!obj[key]) {
//             return false;
//         } else {
//             preKey = key;
//             preObj = obj;
//             obj = obj[key];
//         }
//     }
//
//     return delete preObj[preKey];
// };

let result = {
    set,
    get,
    // remove,
};

export default result;