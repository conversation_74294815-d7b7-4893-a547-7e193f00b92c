import {connect} from 'react-redux'
import ModulesContainer from '../../../../core/components/modulesContainer/modulesContainer'
import actions from "../../../actions";
import Constants from "../../../../core/utils/constants";

const mapStateToProps = (state, ownProps) => {
    //这里主要做redux状态status与组件prop的适配，如果store变化不导致组件更新的话，直接返回ownProps即可。
    return {
        modules: state.moduleContainer.modules,
    };
};

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        setCmptRef: (moduleId, cmptRef) => {
            //发布action，把映射更新到store
            dispatch(actions.modulesContainer.registerCmptRefMap(moduleId, cmptRef, Constants.MODULE_STATE.LOADED));
        }
    };
};

const Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(ModulesContainer);

export default Container;