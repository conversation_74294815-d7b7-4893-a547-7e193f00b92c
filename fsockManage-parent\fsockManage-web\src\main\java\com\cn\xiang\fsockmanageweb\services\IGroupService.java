package com.cn.xiang.fsockmanageweb.services;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.GroupPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5 15:37
 */
public interface IGroupService extends IService<GroupPo> {
    public GroupPo findGroupByName(String group);

    public GroupPo findGroupByUserName(String username);

    void assignTrafficRule(String groupId, String trafficRuleId);

    void assignTrafficCapacity(String groupId, String trafficCapacityId);

    void saveWithDefaultTrafficCapacity(GroupPo groupPo);

    List<GroupPo> listBySearch(String search);
}
