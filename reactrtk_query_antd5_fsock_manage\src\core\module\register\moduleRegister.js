// import AsyncComponent from "../../../core/components/hocComponent/async/AsyncComponent";
import OfficialAsyncComponent from "../../../core/components/hocComponent/async/OfficialAsyncComponent";
import React from 'react';
import Constants from "../../utils/constants";
import global from "../../utils/global";
import moduleConfig from "../../utils/config/moduleConfig";
//AsyncComponent
// const modules = {
//     indexModule1: AsyncComponent(() => import(/* webpackChunkName: "modules/indexModules/index1" */ "../../../redux/store/modules/indexModule1/container")),
//     indexModule2: AsyncComponent(() => import(/* webpackChunkName: "modules/indexModules/index2" */ "../../../modules/indexModule2/index")),
//     indexModule3: AsyncComponent(() => import(/* webpackChunkName: "modules/indexModules/index3" */ "../../../modules/indexModule3/index")),
//     indexModule4: AsyncComponent(() => import(/* webpackChunkName: "modules/indexModules/index4" */ "../../../modules/indexModule4/index")),
//     indexModule5: AsyncComponent(() => import(/* webpackChunkName: "modules/indexModules/index5" */ "../../../redux/store/modules/indexModule5/container")),
//     indexModule6: AsyncComponent(() => import(/* webpackChunkName: "modules/indexModules/index6" */ "../../../modules/indexModule6/index")),
// };

//OfficialAsyncComponent
// const modules = {
//     indexModule1: OfficialAsyncComponent(React.lazy(() => import(/* webpackChunkName: "modules/indexModules/index1" */ "../../../redux/store/modules/indexModule1/container"))),
//     indexModule2: OfficialAsyncComponent(React.lazy(() => import(/* webpackChunkName: "modules/indexModules/index2" */ "../../../modules/indexModule2/index"))),
//     indexModule3: OfficialAsyncComponent(React.lazy(() => import(/* webpackChunkName: "modules/indexModules/index3" */ "../../../modules/indexModule3/index"))),
//     indexModule4: OfficialAsyncComponent(React.lazy(() => import(/* webpackChunkName: "modules/indexModules/index4" */ "../../../modules/indexModule4/index"))),
//     indexModule5: OfficialAsyncComponent(React.lazy(() => import(/* webpackChunkName: "modules/indexModules/index5" */ "../../../redux/store/modules/indexModule5/container"))),
//     indexModule6: OfficialAsyncComponent(React.lazy(() => import(/* webpackChunkName: "modules/indexModules/index6" */ "../../../modules/indexModule6/index"))),
// };
/**
 * 封装组件lazy加载
 * @param moduleId
 * @param importCallback
 * @returns {React.ForwardRefExoticComponent<React.PropsWithoutRef<{}> & React.RefAttributes<unknown>>}
 */
const lazy = (moduleId, importCallback) => {
    let component = React.lazy(() => {
        // console.log('load module[' + moduleId + '] before');
        //把模型状态设置为Constants.MODULE_STATE.BEFORE_LOAD
        global.store.dispatch(global.actions.modulesContainer.registerCmptRefMap(moduleId, undefined, Constants.MODULE_STATE.BEFORE_LOAD));
        return importCallback && importCallback();
    })
    return OfficialAsyncComponent(component);
};

const pathModuleMap = {};
let isRegisterModule = false;
/**
 * 注册模型
 * @param moduleId
 * @param importCallback
 */
const registerModule = (moduleId, importCallback) => {
    pathModuleMap[moduleId] = lazy(moduleId, importCallback);
}

/**
 * 注销模型
 * @param moduleId
 * @returns {boolean}
 */
const unRegisterModule = (moduleId) => {
    return delete pathModuleMap[moduleId];
}

/**
 * 注册模型
 * @param moduleConfig
 */
const register = (moduleConfig = []) => {
    moduleConfig.forEach((item) => {
        registerModule(item.moduleId, item.importCallback);
    })
}

/**
 * 获取注册模型map
 * @returns {{}}
 */
const getRegisterModuleMap = () => {
    if (!isRegisterModule) {
        register(moduleConfig);
        isRegisterModule = true;
    }
    return pathModuleMap;
}

let result = {
    registerModule,
    unRegisterModule,
    getRegisterModuleMap
}

export default result;
