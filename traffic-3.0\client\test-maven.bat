@echo off
echo Testing Maven build...

REM Check Maven
where mvn >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: <PERSON>ven not found!
    pause
    exit /b 1
)

echo Maven found, building JAR...
call mvn clean package -DskipTests

if %errorlevel% neq 0 (
    echo ERROR: <PERSON>ven build failed!
    pause
    exit /b 1
)

echo SUCCESS: Maven build completed!

REM Check if JAR was created
if exist target\traffic-client-3.1-SNAPSHOT-jar-with-dependencies.jar (
    echo JAR file created successfully!
    for %%A in (target\traffic-client-3.1-SNAPSHOT-jar-with-dependencies.jar) do echo File size: %%~zA bytes
) else (
    echo WARNING: JAR file not found!
)

pause
