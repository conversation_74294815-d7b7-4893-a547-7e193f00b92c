package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.mapper.IOrderItemsMapper;
import com.cn.xiang.fsockmanageweb.po.OrderItemsPo;
import com.cn.xiang.fsockmanageweb.services.IOrderItemsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
@DS("order")
public class OrderItemsServiceImpl extends ServiceImpl<IOrderItemsMapper, OrderItemsPo> implements IOrderItemsService {
    @Override
    public OrderItemsPo getOne1(LambdaQueryWrapper<OrderItemsPo> eq) {
        return this.getOne(eq);
    }
}
