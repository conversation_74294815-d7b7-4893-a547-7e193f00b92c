package com.xiang.traffic.client;

import com.xiang.traffic.*;
import com.xiang.traffic.Component;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.TopLevelComponent;
import com.xiang.traffic.VoidComponent;
import org.apache.log4j.Appender;
import org.apache.log4j.FileAppender;
import org.apache.log4j.Logger;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.Enumeration;
import java.util.Locale;
import java.util.Objects;
import java.util.ResourceBundle;

public abstract class Client extends TopLevelComponent
        implements Component<VoidComponent>, Environment, ClientOperator {

    /**
     * 默认组件名
     */
    static final String DEFAULT_COMPONENT_NAME = "flyingsocks-client";


    private static final ResourceBundle EXIT_MSG_BUNDLE =
            ResourceBundle.getBundle("META-INF/i18n/exitmsg", Locale.getDefault());


    /**
     * GUI事件处理循环
     */
    private Runnable guiTask;


    Client() {
        super(DEFAULT_COMPONENT_NAME);
    }

    /**
     * @return 配置管理器
     */
    public final ConfigManager<?> getConfigManager() {
        return super.getConfigManager();
    }


    @Override
    public void cleanLogFiles() {
        Enumeration<?> appenders = Logger.getRootLogger().getAllAppenders();
        while (appenders.hasMoreElements()) {
            Appender appender = (Appender) appenders.nextElement();
            if (appender instanceof FileAppender) {
                Path path = Paths.get(((FileAppender) appender).getFile());
                try {
                    if (Files.exists(path) && Files.isRegularFile(path)) {
                        Files.delete(path);
                    }
                } catch (IOException e) {
                    log.warn("Could not delete log file [{}]", path, e);
                }
            }
        }
    }

    @Override
    public void openLogDirectory() {
        Appender appender = Logger.getRootLogger().getAppender(log.getName());
        if (appender instanceof FileAppender) {
            File folder = new File(((FileAppender) appender).getFile());
            try {
                Desktop.getDesktop().open(folder);
            } catch (IOException e) {
                log.warn("An error occurred while open log file directory", e);
            }
        }
    }

    @Override
    public void openConfigDirectory() {
        GlobalConfig gc = getConfigManager().getConfig(GlobalConfig.NAME, GlobalConfig.class);
        if (gc != null) {
            try {
                Desktop.getDesktop().open(gc.configPath().toFile());
            } catch (IOException e) {
                log.warn("Open config file directory occur a exception", e);
            }
        }
    }


    @Override
    public void openBrowser(String url) {
        try {
            if (isWindows()) {
                Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
            } else {
                Desktop dt = Desktop.getDesktop();
                if (dt.isSupported(Desktop.Action.BROWSE)) {
                    dt.browse(new URI(url));
                }
            }
        } catch (IOException | URISyntaxException e) {
            log.warn("Open browser occur a exception", e);
        }
    }

    /**
     * 设置GUI界面任务
     */
    public void setGUITask(Runnable task) {
        this.guiTask = Objects.requireNonNull(task);
    }

    /**
     * 运行GUI任务
     */
    boolean runGUITask() {
        Runnable task = this.guiTask;
        if (task != null) {
            task.run();
            return true;
        }

        return false;
    }


    public static void exitWithNotify(int status, String message) {
        if (Desktop.isDesktopSupported()) {
            JOptionPane.showMessageDialog(null, message != null ? message : "", "ERROR", JOptionPane.ERROR_MESSAGE);
        }
        System.exit(status);
    }


    public static void exitWithNotify(int status, String string, Object... args) {
        if (Desktop.isDesktopSupported()) {
            String msg;
            if (EXIT_MSG_BUNDLE.containsKey(string)) {
                msg = EXIT_MSG_BUNDLE.getString(string);
            } else {
                msg = string;
            }

            if (args == null || args.length == 0) {
                JOptionPane.showMessageDialog(null, msg,
                        EXIT_MSG_BUNDLE.getString("exitmsg.title"), JOptionPane.ERROR_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(null, MessageFormat.format(msg, args),
                        EXIT_MSG_BUNDLE.getString("exitmsg.title"), JOptionPane.ERROR_MESSAGE);
            }
        }

        System.exit(status);
    }

}
