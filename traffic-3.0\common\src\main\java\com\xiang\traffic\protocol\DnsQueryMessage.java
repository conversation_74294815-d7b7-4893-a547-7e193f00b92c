package com.xiang.traffic.protocol;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * DNS查询请求
 *
 * <AUTHOR>
 * @date 2024/6/6 15:17
 */
public class DnsQueryMessage extends DnsMessage {

    /**
     * 问题列表，一般只有一个
     */
    protected List<Question> questions;


    public DnsQueryMessage(short transactionId) {
        super(transactionId);
        setQR(false);
    }

    public DnsQueryMessage(ByteBuf buf) throws SerializationException {
        super(buf);
    }

    @Override
    protected ByteBuf serializeBody(ByteBufAllocator allocator) throws SerializationException {
        if (questions.isEmpty()) {
            throw new SerializationException(getClass(), "No question");
        }

        int size = 0;
        for (Question q : questions) {
            size += q.getName().length() + 1;
            size += 4;
        }

        ByteBuf body = allocator.directBuffer(size);
        for (Question q : questions) {
            body.writeCharSequence(q.getName(), StandardCharsets.US_ASCII);
            body.writeByte(0);
            body.writeShort(q.getType());
            body.writeShort(q.getKlass());
        }

        return body;
    }

    @Override
    protected void deserializeBody(ByteBuf buf) throws SerializationException {
        int size = Short.toUnsignedInt(super.questionCount);
        this.questions = new ArrayList<>(size);
        try {
            for (int i = 0; i < size; i++) {
                addQuestion(readQuestion(buf));
            }
        } catch (IndexOutOfBoundsException e) {
            throw new SerializationException(getClass(), "Read question error", e);
        }
    }


    public List<Question> getQuestions() {
        List<Question> questions = this.questions;
        if (questions == null) {
            return Collections.emptyList();
        }
        return Collections.unmodifiableList(questions);
    }


    public void addQuestion(Question question) {
        List<Question> questions = this.questions;
        if (questions == null) {
            questions = new ArrayList<>(1);
            this.questions = questions;
        }

        questions.add(Objects.requireNonNull(question));
        questionCount++;
    }
}
