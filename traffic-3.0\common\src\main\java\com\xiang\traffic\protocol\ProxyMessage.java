package com.xiang.traffic.protocol;

import io.netty.buffer.ByteBuf;

/**
 * 代理消息抽象类
 */
abstract class ProxyMessage extends ServiceStageMessage {

    public static final byte SERVICE_ID = 0;

    /**
     * 客户端发起代理请求的序列ID
     */
    int serialId;

    /**
     * 请求/响应内容
     */
    private ByteBuf message;


    ProxyMessage(int serialId) {
        super(SERVICE_ID);
        this.serialId = serialId;
    }


    ProxyMessage(ByteBuf serialBuf) throws SerializationException {
        super(serialBuf);
    }

    /**
     * @return 客户端发起代理请求的序列ID
     */
    public final int serialId() {
        return serialId;
    }

    /**
     * @return 代理消息请求/响应正文
     */
    public synchronized final ByteBuf getMessage() {
        if (message != null) {
            ByteBuf buf = message;
            message = null;
            return buf;
        }

        throw new IllegalStateException("ProxyMessage content is only can get one time");
    }

    public synchronized final void setMessage(ByteBuf buf) {
        if (message != null) {
            throw new IllegalStateException("message has set");
        }

        this.message = buf;
    }

    /**
     * 当condition为false时抛出SerializationException
     */
    static void assertTrue(boolean condition, String errorMessage) throws SerializationException {
        if (!condition) {
            throw new SerializationException(errorMessage);
        }
    }

}
