import loadMaskReducerObj from './store/components/loadingMask/reducer';
// import requestActionReducerObj from "./store/request/reducer"
import moduleContainerReducerObj from './store/components/modulesContainer/reducer'
import moduleTabsReducerObj from './store/components/moduleTabs/reducer'
import lineChartReducerObj from './store/components/echarts/lineChart/reducer'
import {baseApi} from "./store/request/services/base/base";

let reducersMap = Object.assign(
    {},
    // requestActionReducerObj.reducer,
    {[baseApi.reducerPath]: baseApi.reducer},
    loadMaskReducerObj.reducer,
    moduleContainerReducerObj.reducer,
    moduleTabsReducerObj.reducer,
    lineChartReducerObj.reducer,
);
export default reducersMap
