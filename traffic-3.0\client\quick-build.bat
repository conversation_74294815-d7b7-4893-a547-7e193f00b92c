@echo off
echo Building Traffic Client Native Image...

REM Check if native-image is available
where native-image >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: native-image not found! Please install GraalVM.
    pause
    exit /b 1
)

REM Check if JAR exists
if not exist target\traffic-client-3.1-SNAPSHOT-jar-with-dependencies.jar (
    echo Building JAR first...
    call mvn clean package -DskipTests
    if %errorlevel% neq 0 (
        echo ERROR: Maven build failed!
        pause
        exit /b 1
    )
)

echo Building native image...
native-image ^
    --no-fallback ^
    --enable-http ^
    --enable-https ^
    --enable-all-security-services ^
    --allow-incomplete-classpath ^
    --report-unsupported-elements-at-runtime ^
    --initialize-at-build-time=org.slf4j ^
    --initialize-at-run-time=io.netty,com.xiang.traffic.client.gui ^
    -H:+ReportExceptionStackTraces ^
    -H:+AddAllCharsets ^
    -H:IncludeResources=".*\.properties$" ^
    -H:IncludeResources=".*\.yml$" ^
    -H:IncludeResources="META-INF/.*" ^
    -H:ConfigurationFileDirectories=src/main/resources/META-INF/native-image ^
    -H:Name=traffic-client ^
    -jar target\traffic-client-3.1-SNAPSHOT-jar-with-dependencies.jar

if %errorlevel% neq 0 (
    echo ERROR: Native image build failed!
    pause
    exit /b 1
)

if exist traffic-client.exe (
    move traffic-client.exe target\
    echo SUCCESS: Native executable created at target\traffic-client.exe
) else (
    echo ERROR: Native executable not found!
    exit /b 1
)

pause
