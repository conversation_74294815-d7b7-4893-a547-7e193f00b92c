#!/bin/bash

echo "========================================"
echo "Traffic Client - Complete Build and Test"
echo "========================================"

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "ERROR: Maven not found! Please install <PERSON>ven and add it to PATH."
    exit 1
fi

# 检查GraalVM
if ! command -v native-image &> /dev/null; then
    echo "ERROR: native-image not found! Please install GraalVM and native-image component."
    exit 1
fi

echo "Step 1: Building regular JAR..."
mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "ERROR: Maven build failed!"
    exit 1
fi

echo ""
echo "Step 2: Testing regular JAR..."
JAR_FILE=$(find target -name "traffic-client-*-jar-with-dependencies.jar" | head -n 1)
if [ -f "$JAR_FILE" ]; then
    echo "Testing JAR: $JAR_FILE"
    echo "Starting application for 3 seconds..."
    java -jar "$JAR_FILE" &
    PID=$!
    sleep 3
    kill $PID 2>/dev/null
    echo "JAR test completed."
else
    echo "WARNING: JAR file not found, skipping JAR test."
fi

echo ""
echo "Step 3: Building Native Image..."
./build-native-linux.sh
if [ $? -ne 0 ]; then
    echo "ERROR: Native image build failed!"
    exit 1
fi

echo ""
echo "Step 4: Testing Native Image..."
./test-native.sh

echo ""
echo "========================================"
echo "Build and Test Summary:"
echo "========================================"

if [ -f "$JAR_FILE" ]; then
    echo "JAR file: $(basename "$JAR_FILE") ($(stat -c%s "$JAR_FILE") bytes)"
fi

if [ -f "target/traffic-client" ]; then
    echo "Native executable: traffic-client ($(stat -c%s "target/traffic-client") bytes)"
    
    echo ""
    echo "SUCCESS: Both JAR and Native Image builds completed successfully!"
    echo ""
    echo "You can run:"
    echo "  - JAR version: java -jar \"$JAR_FILE\""
    echo "  - Native version: ./target/traffic-client"
else
    echo "ERROR: Native executable not found!"
    exit 1
fi

echo ""
echo "========================================"
