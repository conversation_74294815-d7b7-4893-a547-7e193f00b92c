import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import PackageResourcesModule from "./index";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getPackageResourcesList', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData1: data.list
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期
            const {unsubscribe} = dispatch(baseApi.endpoints.getPackageResourcesList.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);

        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData1 !== this.props.tableData1) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData1);
            }
        },
        handleAddOk: function (e) {
            let modalInst = this.addModalRef.current;
            let formInst = modalInst.formRef.current;
            formInst.validateFields().then(values => {
                let promise = dispatch(baseApi.endpoints.saveUpload.initiate({
                    id: values.id,
                    version: values.version,
                    fileName: values.fileName,
                }, {
                    forceRefetch: true
                }));

                promise.then(({data, isSuccess}) => {
                    if (isSuccess && data.status) {
                        window.global.Notification.open({
                            type: window.global.Notification.TYPE.SUCCESS,
                            message: "添加zip资源文件成功",
                            delay: 3,
                        });

                        dispatch(baseApi.endpoints.getPackageResourcesList.initiate(undefined, {
                            //subscribe属性可以配置不把数据存储到store
                            subscribe: false,
                            forceRefetch: true
                        }));
                    }
                });

                formInst.resetFields();
                modalInst.setState({
                    visible: false
                });
            });
        },
        handleDelOk: function (e) {
            let tableInst = this.tableRef;
            let selectedId = this.selectedRows[0].id;
            dispatch(baseApi.endpoints.delPackageResources.initiate({id: selectedId}, {
                subscribe: false, forceRefetch: true,
            })).then(() => {
                //隐藏弹窗
                let delModalInst = this.delModalRef.current;
                delModalInst.setState({
                    visible: false
                });

                //刷新表格
                dispatch(baseApi.endpoints.getPackageResourcesList.initiate(undefined, {
                    subscribe: false, forceRefetch: true
                })).then((response) => {
                    let data = response.data.list;
                    tableInst.current._dataMethod_.refresh(data);

                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "删除zip资源文件成功.",
                        delay: 3,
                    });
                });
            });
        }
    };
};

let Container = ModuleContainerCreator.create(PackageResourcesModule, mapStateToProps, mapDispatchToProps);
export default Container;
