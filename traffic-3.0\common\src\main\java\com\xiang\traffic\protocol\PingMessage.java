package com.xiang.traffic.protocol;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.Unpooled;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:18
 */
public class PingMessage extends ServiceStageMessage {

    public static final byte SERVICE_ID = 0x7F;

    private static final byte[] CONTENT = "PING".getBytes(StandardCharsets.US_ASCII);

    private static final ByteBuf BODY;

    static {
        ByteBuf body = Unpooled.directBuffer(1 + CONTENT.length);
        body.writeBytes(CONTENT);
        BODY = body.asReadOnly();
    }

    public PingMessage() {
        super(SERVICE_ID);
    }

    public PingMessage(ByteBuf buf) throws SerializationException {
        super(buf);
    }

    @Override
    public ByteBuf serialize0(ByteBufAllocator allocator) throws SerializationException {
        return BODY.retainedSlice();
    }

    @Override
    protected void deserialize0(ByteBuf buf) throws SerializationException {
        try {
            if (buf.readableBytes() != CONTENT.length) {
                throw new SerializationException(PongMessage.class, "Illegal content: Wrong length");
            }

            for (byte b : CONTENT) {
                if (buf.readableBytes() == 0 || buf.readByte() != b) {
                    throw new SerializationException(PongMessage.class, "Illegal content: Wrong data");
                }
            }
        } catch (IndexOutOfBoundsException e) {
            throw new SerializationException("Unable to read ping message", e);
        }
    }
}
