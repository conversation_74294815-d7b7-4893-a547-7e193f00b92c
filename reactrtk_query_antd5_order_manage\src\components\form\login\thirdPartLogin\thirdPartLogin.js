import React, {Component} from "react";
import './thirdPartLogin.less';

class ThirdPartLogin extends Component {
    render() {
        let liArr = [];
        liArr = this.props.listItems.map((item) => {
            return (
                <li key={item.key}>
                    <item.Cmpt {...item.props}/>
                </li>
            );
        });

        return (
            <div className={'thirdPartLogin-ctn'}>
                <span className={'text-ctn'}>{this.props.labelText}</span>
                <ul className={'img-ctn'}>
                    {liArr}
                </ul>
            </div>);
    }
}

export default ThirdPartLogin;