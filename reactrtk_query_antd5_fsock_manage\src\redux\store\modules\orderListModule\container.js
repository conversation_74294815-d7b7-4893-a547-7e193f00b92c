import OrderListModule from './index';
import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import SimpleRouter from "../../../../core/router/smpleRouter";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getOrderListData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData: data.list
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期
            const {unsubscribe} = dispatch(baseApi.endpoints.getOrderListData.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
            const {unsubscribe} = dispatch(baseApi.endpoints.getOrderListData.initiate(undefined, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData !== this.props.tableData) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData);
            }
        },
        deliverOnClick: function (e) {
            let target = e.target;
            let orderId = target.dataset.orderid;

            SimpleRouter.open(orderId + '发货', "/deliverModule", {orderId: orderId});
        },
        viewDetailOnClick: function (e) {
            let target = e.target;
            let orderId = target.dataset.orderid;

            this.deliverModalRef.current.showModal(true);

            dispatch(baseApi.endpoints.getDeliverDetailData.initiate({orderId: orderId}, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
        },
    };
};

let Container = ModuleContainerCreator.create(OrderListModule, mapStateToProps, mapDispatchToProps);
export default Container;
