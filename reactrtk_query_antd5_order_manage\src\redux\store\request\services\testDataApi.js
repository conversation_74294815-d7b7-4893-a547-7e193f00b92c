const url = 'testData';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getLineChartData: builder.query({
            query: (name) => url + '/lineChartData',
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        getLineChartPushData: builder.query({
            query: (name) => url + '/lineChartPushData',
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        })
    }
}
