package com.xiang.traffic.misc;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.handler.codec.MessageToByteEncoder;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 异或编解码器
 *
 * <AUTHOR>
 * @date 2024/1/12 13:47
 */
public class XorEncoderFactory {
    //    private static final Logger LOGGER = LoggerFactory.getLogger(XorEncoderFactory.class);
    public static final byte[] DEFAULT_MASKS = {0x11, 0x58, 0x43, 0x15, 0x5f, 0x2f, -0xff / 2, 0x41, 0x45, 0x53};

    public static final int MISTAKE_MINUTE_FACTOR = 5;

    private static long getDateFactor() {
        ZonedDateTime now = ZonedDateTime.now();
        now = now.withZoneSameInstant(ZoneId.of("UTC"));

        long year = now.getYear();
        long month = now.getMonthValue();
        long day = now.getDayOfMonth();
        int hour = now.getHour();
        int minute = now.getMinute();

        //误差在5分钟内
        boolean greaterThan60 = minute + MISTAKE_MINUTE_FACTOR > 60;
        if (greaterThan60) {
            hour += 1;
            if (hour > 23) {
                hour = 0;
            }
        }

//        LOGGER.info("XorEncoderFactory.getDateFactor: year={}, month={}, day={}, hour={}", year, month, day, hour);
        return year << 21 | month << 14 | day << 7 | hour;
    }

    private static byte[] getMaskArray() {
        long factor = getDateFactor();
        int index = (int) (factor % DEFAULT_MASKS.length);
        byte mask = DEFAULT_MASKS[index];
        return new byte[]{mask};
    }

    public static XorEncoder createXorEncoder() {
        return new XorEncoder(getMaskArray());
    }

    public static XorDecoder createXorDecoder() {
        return new XorDecoder(getMaskArray());
    }

    static class XorEncoder extends MessageToByteEncoder {
        private byte[] maskBytes = DEFAULT_MASKS;
        private int maskIndex = 0;

        public XorEncoder(byte[] masks) {
            if (masks != null) {
                maskBytes = masks;
            }
        }


        private void rewindMaskIndex() {
            maskIndex = maskIndex >= maskBytes.length ? 0 : maskIndex;
        }


        @Override
        protected void encode(ChannelHandlerContext ctx, Object msg, ByteBuf out) throws Exception {
            if (msg instanceof ByteBuf) {
                ByteBuf buf = (ByteBuf) msg;
                byte b;
                while (buf.isReadable()) {
                    b = buf.readByte();
                    b ^= maskBytes[maskIndex++];
                    rewindMaskIndex();

                    out.writeByte(b);
                }
            }
        }
    }


    static class XorDecoder extends ByteToMessageDecoder {
        private byte[] maskBytes = new byte[0];

        public XorDecoder(byte[] masks) {
            if (masks != null) {
                maskBytes = masks;
            }
        }

        private int maskIndex = 0;

        private void rewindReadMaskIndex() {
            maskIndex = maskIndex >= maskBytes.length ? 0 : maskIndex;
        }


        @Override
        protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
            ByteBuf copy = in.retainedSlice(0, in.readableBytes());
            copy.writerIndex(0);

            byte b;
            while (in.isReadable()) {
                b = in.readByte();

                b ^= maskBytes[maskIndex++];
                rewindReadMaskIndex();

                copy.writeByte(b);
            }
            out.add(copy);

//            ByteBuf dist = ctx.alloc().buffer(in.readableBytes());
//            byte b;
//            while (in.isReadable()) {
//                b = in.readByte();
//
//                b ^= maskBytes[maskIndex++];
//                rewindReadMaskIndex();
//
//                dist.writeByte(b);
//            }
//            out.add(dist);
        }
    }

    /**
     * test xor
     *
     * @param args
     */

    public static void main(String[] args) {
        int hour = 0;
        int minute = 1;
        minute = minute + 5 > 60 ? 60 : minute;
        int tmp = (int) Math.ceil(minute / 60f * 10);
        if (tmp > 5) {
            hour += 1;
            if (hour > 23) {
                hour = 0;
            }
        }
        System.out.println(tmp);
        System.out.println(minute);
        System.out.println(hour);

//        testFactor();

//        byte[] buf = {1, 0, 4, 2, 3, 3};
//        byte[] mask = {15, 44};
//        int maskIndex = 0;
//        printBuf(buf);
//
//        for (int i = 0; i < buf.length; i++) {
//            byte b = buf[i];
//            byte mask_ = mask[maskIndex++];
//            if (maskIndex >= mask.length) {
//                maskIndex = 0;
//            }
//            buf[i] = (byte) (b ^ mask_);
//        }
//        printBuf(buf);
//
//        maskIndex = 0;
//        for (int i = 0; i < buf.length; i++) {
//            byte b = buf[i];
//            byte mask_ = mask[maskIndex++];
//            if (maskIndex >= mask.length) {
//                maskIndex = 0;
//            }
//            buf[i] = (byte) (b ^ mask_);
//        }
//        printBuf(buf);
//
//        System.out.println(0xff);
    }

    public static void printBuf(byte[] buf) {
        for (int i = 0; i < buf.length; i++) {
            System.out.print(buf[i]);
        }
        System.out.println();
    }

    public static void testFactor() {
        LocalDateTime now = LocalDateTime.now();
        long year = now.getYear();
        long month = now.getMonthValue();
        long day = now.getDayOfMonth();
        int hour = now.getHour();

        long factor = year << 21 | month << 14 | day << 7 | hour;

        print2(year << 21);
        print2(month << 14);
        print2(day << 7);
        print2(hour);
        print2(factor);
    }

    public static void print2(long num) {
        if (num > 0) {
            print2(num / 2);
            System.out.print(num % 2);
        } else {
            System.out.println();
        }
    }
}




