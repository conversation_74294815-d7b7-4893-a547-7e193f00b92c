
const url = 'menu';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getLeftMenuData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'GET',
                    params: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
