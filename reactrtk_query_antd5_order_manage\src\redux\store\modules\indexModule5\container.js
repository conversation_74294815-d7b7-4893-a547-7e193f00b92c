import IndexModule5 from './index';
import global from "../../../../core/utils/global";
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import Utils from "../../../../core/utils/utils";

//timer不能放在mapDispatchToProps中，会导致timer.ref为undefined，定时器清除不了
let timer = {};
/**
 * 为main添加 index-content样式
 */
const addClassToMain = () => {
    let mainDom = document.querySelector('.index-layout-ctn > .ant-layout > .ant-layout > main');
    mainDom.className += ' index-content';
}
/**
 * 为main移除 index-content样式
 */
const removeClassFromMain = () => {
    let mainDom = document.querySelector('.index-layout-ctn > .ant-layout > .ant-layout > main');
    mainDom.className = mainDom.className.replace(' index-content', '');
}
// let pushDataDispatchResult;

const mapStateToProps = (state, ownProps) => {
    //这里主要做redux状态status与组件prop的适配，如果store变化不导致组件更新的话，直接返回ownProps即可。
    return ownProps;
};

const mapDispatchToProps = (dispatch, ownProps) => {
    const pushDataToChart = async () => {
        // dispatch(actions.request.fetch('chart/lineChartPushData', {
        //     url: '/lineChartPushData',
        //     method: 'GET',
        //     successCallback: (response) => {
        //         console.log('pushDataToChart');
        //
        //         let content = response.content;
        //         //由于请求的是json文件配置的数据，是静态的，所以修改x,y的值，模拟数据一直变化
        //         content.x = content.x + '_' + (Math.floor(Math.random() * Math.pow(10, 2)) + 1);
        //         content.y = content.y + Math.floor(Math.random() * Math.pow(10, 3)) + 1;
        //         let state = global.store.getState();
        //
        //         //x,y数组删除头部数据，在尾部添加数据
        //         let lineChartData = Utils.deepClone(state.lineChart.data);
        //         let x = lineChartData.x;
        //         let y = lineChartData.y;
        //         x.splice(0, 1);
        //         x.push(content.x);
        //         y.splice(0, 1,);
        //         y.push(content.y);
        //
        //         dispatch(actions.lineChart.refreshData(lineChartData));
        //     }
        // }, Constants.FETCH_MODE.CALLBACK_ONLY));

        //请求重发，自己实现
        // if (pushDataDispatchResult) {
        //     pushDataDispatchResult.refetch();
        // } else {
        //     pushDataDispatchResult = dispatch(baseApi.endpoints.getLineChartPushData.initiate());
        //     pushDataDispatchResult.unsubscribe();
        // }

        //通过提供配置强制重发请求
        let data = await Utils.sendJqueryAjaxStyleRequest(baseApi.endpoints.getLineChartPushData, undefined, dispatch);
        console.log('pushDataToChart');

        let content = Utils.deepClone(data);
        //由于请求的是json文件配置的数据，是静态的，所以修改x,y的值，模拟数据一直变化
        content.x = content.x + '_' + (Math.floor(Math.random() * Math.pow(10, 2)) + 1);
        content.y = content.y + Math.floor(Math.random() * Math.pow(10, 3)) + 1;

        //x,y数组删除头部数据，在尾部添加数据
        let lineChartData = Utils.deepClone(global.store.getState().lineChart.data);
        let x = lineChartData.x;
        let y = lineChartData.y;
        x.splice(0, 1);
        x.push(content.x);
        y.splice(0, 1,);
        y.push(content.y);

        dispatch(window.global.actions.lineChart.refreshData(lineChartData));


        return true;
    };
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: async function (moduleParam) {
            //为main添加 index-content样式
            addClassToMain();

            console.log("moduleMounted");
            console.log(this);
            console.log("moduleParam");
            console.log(moduleParam);
            //模型挂载生命周期
            // dispatch(actions.request.fetch('chart/lineChartData', {
            //     url: '/lineChartData',
            //     method: 'GET',
            //     successCallback: (response) => {
            //         let content = response.content;
            //         dispatch(actions.lineChart.refreshData(content));
            //     }
            // }, Constants.FETCH_MODE.CALLBACK_ONLY));

            let data = await Utils.sendJqueryAjaxStyleRequest(baseApi.endpoints.getLineChartData, undefined, dispatch);
            dispatch(window.global.actions.lineChart.refreshData(data));

            //每两秒发送请求更新数据
            global.Utils.setInterval(pushDataToChart, 2 * 1000, timer);
        },
        [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //为main移除 index-content样式
            removeClassFromMain();

            console.log("modulePause");
            console.log(this);
            console.log("moduleParam");
            console.log(moduleParam);

            //模型暂停生命周期
            clearTimeout(timer.ref);
            delete timer.ref;
        },
        [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //为main添加 index-content样式
            addClassToMain();

            console.log("moduleResume");
            console.log(this);
            console.log("moduleParam");
            console.log(moduleParam);

            //模型恢复生命周期
            //每两秒发送请求更新数据
            global.Utils.setInterval(pushDataToChart, 2 * 1000, timer);
        },
        [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //为main移除 index-content样式
            removeClassFromMain();

            console.log("moduleDestroy");
            console.log(this);
            console.log("moduleParam");
            console.log(moduleParam);
            //模型销毁生命周期
            clearTimeout(timer.ref);
            delete timer.ref;
        }
    };
};

let Container = ModuleContainerCreator.create(IndexModule5, mapStateToProps, mapDispatchToProps);

export default Container;
