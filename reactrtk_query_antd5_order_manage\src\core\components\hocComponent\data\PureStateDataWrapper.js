import React, {Component} from 'react'
import Utils from "../../../utils/utils";

/**
 * 纯状态数组组件hoc,包裹后，把数据属性作为state，并增加refresh、add、update、remove方法到被包裹组件props._method_中
 * 数据属性为数组
 * @param WrappedComponent
 * @param config
 * @returns {React.ForwardRefExoticComponent<React.PropsWithoutRef<{}> & React.RefAttributes<unknown>>}
 */
const pureStateDataWrapperHoc = (WrappedComponent, config = {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: true
}) => {
    class PureStateDataWrapper extends Component {
        constructor(props) {
            super(props);
            this.state = {
                data: this.props[config.dataPropName] || []
            }

            this.setSourceTableRef = this.setSourceTableRef.bind(this);
        }

        setSourceTableRef = (ref) => {
            this.sourceTableRef = ref;
            return ref;
        }

        _dataMethod_ = {
            getDate: () => {
                return this.state.data;
            },
            refresh: (items) => {
                if (items) {
                    this.setState({
                        data: items
                    });
                }
            },
            add: (item) => {
                if (item) {
                    this.setState((prevState) => {
                        let state = Utils.deepClone(prevState);
                        state.data.push(item);
                        return state;
                    })
                }
            },
            update: (item) => {
                if (item) {
                    this.setState((prevState) => {
                        let state = Utils.deepClone(prevState);
                        for (let i = 0; i < state.data.length; i++) {
                            if (state.data[i][config.itemKeyName] === item[config.itemKeyName]) {
                                state.data[i] = item;
                            }
                        }
                        return state;
                    })
                }
            },
            remove: (itemKey) => {
                if (itemKey) {
                    this.setState((prevState) => {
                        let state = Utils.deepClone(prevState);
                        for (let i = 0; i < state.data.length; i++) {
                            if (state.data[i][config.itemKeyName] === itemKey) {
                                state.data.splice(i, 1);
                            }
                        }
                        return state;
                    });
                }
            }
        }

        render() {
            let {forwardedRef, ...props} = this.props;
            props[config.dataPropName] = this.state.data;

            if (config.isForwardedRef) {
                return <WrappedComponent _dataMethod_={this._dataMethod_} ref={forwardedRef} {...props}/>
            }
            return <WrappedComponent _dataMethod_={this._dataMethod_} {...props} ref={this.setSourceTableRef}/>
        }
    }

    return React.forwardRef((props, ref) => {
        if (config.isForwardedRef) {
            return <PureStateDataWrapper {...props} forwardedRef={ref}/>;
        }
        return <PureStateDataWrapper {...props} ref={ref}/>;
    });
}

export default pureStateDataWrapperHoc;
