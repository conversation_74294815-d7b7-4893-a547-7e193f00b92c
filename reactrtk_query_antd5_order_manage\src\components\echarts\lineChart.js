import React from 'react'
import EChartA<PERSON>pter from "./adapter/EChartAdapter";

const OPTION = {
    xAxis: {
        type: 'category',
        data: [],
        axisLabel: {interval: 0}
    },
    yAxis: {
        type: 'value'
    },
    series: [{
        data: [],
        type: 'line'
    }]
};

/**
 * echarts图表整合示例
 */
class LineChart extends React.Component {

    getOption = () => {
        let option = Object.assign({}, OPTION);
        option = Object.assign(option, this.props.option);

        let data = this.props.data;
        if (data) {
            option.xAxis.data = data.x;
            option.series[0].data = data.y;
        }
        return option;
    }

    render() {
        let {id, option, ...props_} = this.props;
        return <EChartAdapter {...props_}
                              option={this.getOption()}
                              id={this.props.id}/>
    }
}

export default LineChart;