[{"type": "agent-extracted", "classes": [{"hash": "com.xiang.traffic.client.ClientBoot"}, {"hash": "com.xiang.traffic.client.StandardClient"}, {"hash": "com.xiang.traffic.client.Client"}, {"hash": "io.netty.bootstrap.Bootstrap"}, {"hash": "io.netty.bootstrap.ServerBootstrap"}, {"hash": "io.netty.channel.nio.NioEventLoopGroup"}, {"hash": "io.netty.channel.socket.nio.NioSocketChannel"}, {"hash": "io.netty.channel.socket.nio.NioServerSocketChannel"}, {"hash": "io.netty.channel.socket.nio.NioDatagramChannel"}]}]