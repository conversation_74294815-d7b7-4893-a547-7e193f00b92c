const CracoLessPlugin = require("craco-less");
// Customize config here
module.exports = {
    //代理服务器配置
    devServer: {
        proxy: {
            '/api_v1': {
                //不要配置contextPath，否则cookie会丢失
                target: 'http://localhost:8080/',
                changeOrigin: true,
                // pathRewrite: {
                //     "^/api_v1": ''
                // }
            },
        },
    },
    //less样式按需引入
    babel: {
        plugins: [
            [
                "import",
                {
                    "libraryName": "antd",
                    "libraryDirectory": "es",
                    //可以设置为true即是less,注意！！！！此时不需要加引号
                    //也可以设置为css,css需要加引号
                    "style": true
                }
            ]
        ]
    },
    plugins: [
        {
            plugin: CracoLessPlugin,
            //antd5.0后下面这部分已经无效，因为5.0去掉了所有less，不能再通过修改less变量的方式修改主题
            // options: {
            //     // 此处根据 less-loader 版本的不同会有不同的配置，详见 less-loader 官方文档
            //     lessLoaderOptions: {
            //         lessOptions: {
            //             modifyVars: {
            //                 //通过修改less变量，更改antd主题样式
            //                 '@primary-color': '#1DA57A',
            //                 '@link-color': '#1DA57A',
            //                 '@border-radius-base': '2px',
            //             },
            //             javascriptEnabled: true,
            //         }
            //     }
            // }
        }
    ],
    webpack: {
        configure: (webpackConfig, {env, paths}) => {
            return webpackConfig;
        },
    },
};
