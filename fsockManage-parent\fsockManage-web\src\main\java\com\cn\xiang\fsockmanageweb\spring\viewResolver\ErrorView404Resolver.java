package com.cn.xiang.fsockmanageweb.spring.viewResolver;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

/**
 * 404错误视图处理器，总是重定向回首页
 *
 * <AUTHOR>
 * @date 2018年8月13日 下午5:25:53
 * @version 1.0
 *
 */
public class ErrorView404Resolver implements ErrorViewResolver, Ordered {
    private static final int ORDER = Ordered.LOWEST_PRECEDENCE - 1;

    @Override
    public ModelAndView resolveErrorView(HttpServletRequest request, HttpStatus status,
                                         Map<String, Object> model) {
        if(status == HttpStatus.NOT_FOUND) {
            return new ModelAndView("redirect:/");
        }
        return null;
    }

    @Override
    public int getOrder() {
        return ORDER;
    }
}
