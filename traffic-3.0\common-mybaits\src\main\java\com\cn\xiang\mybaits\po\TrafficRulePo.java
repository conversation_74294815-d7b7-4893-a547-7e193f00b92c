package com.cn.xiang.mybaits.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.mybaits.config.base.BaseEntity;

/**
 * <AUTHOR>
 * @date 2024/2/5 16:23
 */
@TableName(value = "tb_traffic_rule")
public class TrafficRulePo extends BaseEntity {
    private String name;
    private long dailyLimit;
    private long weeklyLimit;
    private long monthlyLimit;
    private int writeLimit;
    private int readLimit;
    private boolean enable;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getDailyLimit() {
        return dailyLimit;
    }

    public void setDailyLimit(long dailyLimit) {
        this.dailyLimit = dailyLimit;
    }

    public long getWeeklyLimit() {
        return weeklyLimit;
    }

    public void setWeeklyLimit(long weeklyLimit) {
        this.weeklyLimit = weeklyLimit;
    }

    public long getMonthlyLimit() {
        return monthlyLimit;
    }

    public void setMonthlyLimit(long monthlyLimit) {
        this.monthlyLimit = monthlyLimit;
    }

    public int getWriteLimit() {
        return writeLimit;
    }

    public void setWriteLimit(int writeLimit) {
        this.writeLimit = writeLimit;
    }

    public int getReadLimit() {
        return readLimit;
    }

    public void setReadLimit(int readLimit) {
        this.readLimit = readLimit;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }
}
