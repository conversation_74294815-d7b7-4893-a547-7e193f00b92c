const url = 'user';
export default (builder, onQueryStarted, transformResponse) => {
    return {
        getUserListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '?page=' + queryArg.page + '&pageSize=' + queryArg.pageSize + '&search=' + queryArg.search,
                    method: 'GET'
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let records = content.list.records;
                for (let i = 0; i < records.length; i++) {
                    records[i].key = records[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
        getUnDeliveredUserListData: builder.query({
            query: (queryArg) => {
                return {
                    url: url + '/deliverList?page=' + queryArg.page + '&pageSize=' + queryArg.pageSize + '&search=' + queryArg.search,
                    method: 'GET'
                }
            },
            transformResponse: (response = {content: ''}, meta, arg) => {
                //增加key属性
                let content = response.content;
                let records = content.list.records;
                for (let i = 0; i < records.length; i++) {
                    records[i].key = records[i].id;
                }
                return content;
            },
            onQueryStarted: onQueryStarted
        }),
        postUserData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'POST',
                    body: queryArg,
                }
            },
            transformResponse: (response) => response,
            onQueryStarted: onQueryStarted
        }),
        putUserData: builder.query({
            query: (queryArg) => {
                return {
                    url: url,
                    method: 'PUT',
                    body: queryArg,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        delUserData: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('id', queryArg.id);
                return {
                    url: url,
                    method: 'DELETE',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        postAssignUserTrafficRule: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('userId', queryArg.userId);
                formData.set('trafficRuleId', queryArg.trafficRuleId);
                return {
                    url: url + '/assignUserTrafficRule',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
        postIsUserExist: builder.query({
            query: (queryArg) => {
                let formData = new FormData();
                formData.set('username', queryArg.username);
                return {
                    url: url + '/isUserExist',
                    method: 'POST',
                    body: formData,
                }
            },
            transformResponse: transformResponse,
            onQueryStarted: onQueryStarted
        }),
    }
}
