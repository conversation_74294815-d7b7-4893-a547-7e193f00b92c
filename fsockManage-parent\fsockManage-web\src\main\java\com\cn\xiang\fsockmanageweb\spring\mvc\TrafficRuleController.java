package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.po.TrafficRulePo;
import com.cn.xiang.fsockmanageweb.services.ITrafficRuleService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import com.cn.xiang.fsockmanageweb.vo.TrafficRuleVo;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api_v1/trafficRule")
public class TrafficRuleController extends ControllerSupport {
    @Autowired
    private ITrafficRuleService trafficRuleService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list() {
        List<TrafficRulePo> list = trafficRuleService.list();

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.POST)
    public Object add(@RequestBody @Validated(value = {TrafficRuleVo.AddGroup.class}) TrafficRuleVo trafficRuleVo) {
        TrafficRulePo trafficRulePo = new TrafficRulePo();
        BeanUtils.copyProperties(trafficRuleVo, trafficRulePo);
        trafficRuleService.save(trafficRulePo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Object modify(@RequestBody @Validated(value = {TrafficRuleVo.ModifyGroup.class}) TrafficRuleVo trafficRuleVo) {
        TrafficRulePo userPo = new TrafficRulePo();
        BeanUtils.copyProperties(trafficRuleVo, userPo);
        trafficRuleService.updateById(userPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public Object del(@Validated @NotEmpty String id) {
        trafficRuleService.removeById(id);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }
}
