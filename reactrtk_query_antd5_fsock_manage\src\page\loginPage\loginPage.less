
.login-ctn {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  /*background-image: url('http://localhost:3000/loginBackground.jpg');*/
  /*background-repeat: no-repeat;*/
  /*background-size: cover;*/
  /*background-position: center;*/

  > img {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: -1000;
  }

  .form-container {
    margin-top: 12%;
    width: 400px;
    align-self: center;
    padding: 40px 20px 25px 20px;
    background-color: white;
    border-radius: 25px;
    opacity: 0.7;

    .form-login-top {
      .form-login-header {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        line-height: 44px;

        .form-login-logo {
          width: 44px;
          height: 44px;
          margin-right: 16px;
          vertical-align: top;

          > img {
            width: 100%;
            height: 100%;
          }
        }

        .form-login-title {
          position: relative;
          top: 2px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 600;
          font-size: 33px;
        }
      }
    }

    .form-login-main {
      min-width: 328px;
      max-width: 500px;
      margin: 0 auto;
      width: 328px;
    }
  }
}

