import React, {Component} from 'react';
import global from '../../../utils/global'

/**
 * @deprecated 由官方OfficialAsyncComponent替代
 * @param loadComponent
 * @returns {React.ForwardRefExoticComponent<React.PropsWithoutRef<{}> & React.RefAttributes<unknown>>}
 */
const enhance = (loadComponent) => {
    class AsyncComponent extends Component {
        constructor(props) {
            super(props);

            this.state = {
                Component: null,
            };
        }

        hasLoadedComponent() {
            return this.state.Component !== null;
        }

        componentDidMount() {
            if (this.hasLoadedComponent()) {
                return;
            }
            //如果0.5秒组件还没加载完，则显示加载遮罩，避免遮罩一闪而过
            setTimeout(() => {
                if (!this.hasLoadedComponent()) {
                    global.store.dispatch(global.actions.loadingMask.showMask(true))
                }
            }, 500);

            loadComponent().then(module => module.default)
                .then((Component) => {
                    const self = this;

                    if (!self.isUnamount) {
                        self.setState({Component});

                        //隐藏加载遮罩
                        global.store.dispatch(global.actions.loadingMask.showMask(false))
                    }
                })
                .catch((err) => {
                    console.error('Cannot load component in <AsyncComponent />');
                    throw err;
                });
        }

        render() {
            const {Component} = this.state;
            return Component ? (<Component ref={this.props.forwardedRef}{...this.props} />) : null;
        }
    }

    return React.forwardRef((props, ref) => {
        return <AsyncComponent {...props} forwardedRef={ref}/>
    });
};

export default enhance;
