<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/fsockManage-parent/fsockManage-common/fsockManage-mybaits/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/fsockManage-parent/fsockManage-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/fsockManage-parent/fsockManage-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/fsockManage-parent/fsockManage-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/fsockManage-parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/fsockManage-parent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/orderManage-parent/orderManage-common/orderManage-mybaits/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/orderManage-parent/orderManage-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/orderManage-parent/orderManage-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/orderManage-parent/orderManage-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/orderManage-parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/orderManage-parent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/traffic-3.0/eureka-server/src/main/java" charset="UTF-8" />
  </component>
</project>