package com.cn.xiang.ordermanageweb.services.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.ordermanageweb.mapper.ISysUserMapper;
import com.cn.xiang.ordermanageweb.po.SysUserPo;
import com.cn.xiang.ordermanageweb.services.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class SysUserServiceImpl extends ServiceImpl<ISysUserMapper, SysUserPo> implements ISysUserService {
    @Override
    public SysUserPo queryByUsername(String username) {
        LambdaQueryWrapper<SysUserPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserPo::getUsername, username);
        return this.baseMapper.selectOne(wrapper);
    }
}
