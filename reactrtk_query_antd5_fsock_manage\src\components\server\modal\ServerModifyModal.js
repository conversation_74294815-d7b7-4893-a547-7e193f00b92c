import React, {Component} from 'react';
import {Form, Input, Modal, Switch} from 'antd'

/**
 * 服务器修改弹窗
 */
class ServerModifyModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };


    render() {
        return <Modal
            title="修改"
            open={this.state.visible}
            onOk={this.props.handleModifyOk}
            onCancel={this.handleCancel}
            okText={'修改'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    hidden={true}
                    label="id"
                    name="id"
                    rules={[{required: true, message: 'Please input your id!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="ip地址"
                    name="ip"
                    rules={[{required: true, message: 'Please input your ip address!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="端口"
                    name="port"
                    rules={[{required: true, message: 'Please input your port!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="cert端口"
                    name="certPort"
                    rules={[{required: true, message: 'Please input your port!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="shell端口"
                    name="shellPort"
                    rules={[{required: true, message: 'Please input your shell port!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="服务器用户名"
                    name="username"
                    rules={[{required: true, message: 'Please input your username!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="密码"
                    name="password"
                    rules={[{required: true, message: 'Please input your password!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="服务器组名"
                    name="groupName"
                    rules={[{required: true, message: 'Please input your group name!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="最大用户量"
                    name="maxUserCount"
                    rules={[{required: true, message: 'Please input  maxUserCount!'}]}
                >
                    <Input/>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default ServerModifyModal;
