package com.cn.xiang.ordermanageweb.config.provider;

import com.cn.xiang.ordermanageweb.po.SysUserPo;
import com.cn.xiang.ordermanageweb.services.ISysUserService;
import com.cn.xiang.ordermanageweb.spring.ApplicationContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class LoginValidateAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private ISysUserService sysUserService;

//    @Autowired
//    IAutoritiesService authoritiesService;

    @Bean
    public PasswordEncoder configBCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException {
        String username = authentication.getName();
        String rawPassword = (String) authentication.getCredentials();

        // 查询用户是否存在
        SysUserPo sysUserPo = sysUserService.queryByUsername(username);

        if (sysUserPo == null) {
            throw new BadCredentialsException("用户名或密码有误!");
        }

        // 验证密码
        PasswordEncoder passwordEncoder = (PasswordEncoder) ApplicationContextHolder.getBean(PasswordEncoder.class);
        if (!passwordEncoder.matches(rawPassword, sysUserPo.getPassword())) {
            throw new BadCredentialsException("用户名或密码有误!");
        }

        // 查找用户权限
//        List<RoleToAuthoritiesEntity> list = authoritiesService
//                .queryGroupToAuthoritiesByUsername(username);
//        List<GrantedAuthority> autorities = new ArrayList<>();
//        for (RoleToAuthoritiesEntity entity : list) {
//            autorities.add(new SimpleGrantedAuthority(entity.getAuthority()));
//        }
        List<GrantedAuthority> autorities = new ArrayList<>();
        User user = new User(sysUserPo.getUsername(), sysUserPo.getPassword(),
                sysUserPo.isStatus(), true, true, true, autorities);

        if (!user.isEnabled()) {
            throw new DisabledException("该账户已被禁用，请联系管理员");

        } else if (!user.isAccountNonLocked()) {
            throw new LockedException("该账号已被锁定");

        } else if (!user.isAccountNonExpired()) {
            throw new AccountExpiredException("该账号已过期，请联系管理员");

        } else if (!user.isCredentialsNonExpired()) {
            throw new CredentialsExpiredException("该账户的登录凭证已过期，请重新登录");
        }

        return new UsernamePasswordAuthenticationToken(user, rawPassword,
                user.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UsernamePasswordAuthenticationToken.class);
    }
}
