package com.cn.xiang.mybaits.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.mybaits.config.base.BaseEntity;
import com.cn.xiang.mybaits.po.typeHandler.TrafficRulePoTypeHandler;

/**
 * <AUTHOR>
 * @date 2024/2/5 12:15
 */
@TableName(value = "tb_user", autoResultMap = true)
public class UserPo extends BaseEntity {
    private String username;

    private String password;

    private String groupId;

    @TableField(value = "traffic_rule_id", typeHandler = TrafficRulePoTypeHandler.class)
    private TrafficRulePo trafficRulePo;

    private boolean enabled;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public TrafficRulePo getTrafficRulePo() {
        return trafficRulePo;
    }

    public void setTrafficRulePo(TrafficRulePo trafficRulePo) {
        this.trafficRulePo = trafficRulePo;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
       return "UserPo{" +
                "username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", groupId='" + groupId + '\'' +
                ", trafficRulePo=" + trafficRulePo +
                ", enabled=" + enabled +
                '}';
    }
}
