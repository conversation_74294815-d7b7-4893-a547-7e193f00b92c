package com.cn.xiang.fsockmanageweb.utils;

import com.jcraft.jsch.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/4/14 12:07
 */
public class ShellUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShellUtils.class);

    private Session session;

    private ShellUtils() {
    }

    public static ShellUtils getInstance() {
        return new ShellUtils();
    }

    /**
     * 初始化
     *
     * @param ip       远程Linux地址
     * @param port     端口
     * @param username 用户名
     * @param password 密码
     * @throws JSchException JSch异常
     * @date 2019/3/15 12:41
     */
    public void init(String ip, Integer port, String username, String password) throws JSchException {
        JSch jsch = new JSch();
        session = jsch.getSession(username, ip, port);
        session.setPassword(password);
        Properties sshConfig = new Properties();
        sshConfig.put("StrictHostKeyChecking", "no");
        session.setConfig(sshConfig);
        session.connect(1200 * 1000);
    }

    /**
     * 执行一条命令,探活使用
     */
    public static String execCmd(Session session, String command) throws Exception {
        // 打开执行shell指令的通道
        Channel channel = session.openChannel("exec");
        ChannelExec channelExec = (ChannelExec) channel;
        channelExec.setCommand("source /etc/profile && source ~/.bash_profile && source ~/.bashrc &&  adb devices && locale charmap");
        channelExec.setCommand(command);
        channel.setInputStream(null);
        channelExec.setErrStream(System.err);
        // channel.setXForwarding();
        channel.connect();

        StringBuilder sb = new StringBuilder(16);
        try (InputStream in = channelExec.getInputStream();
             InputStreamReader isr = new InputStreamReader(in, StandardCharsets.UTF_8);
             BufferedReader reader = new BufferedReader(isr)) {
            String buffer;
            while ((buffer = reader.readLine()) != null) {
                sb.append("\n").append(buffer);
            }

            //2023-02-21 关闭流
            closeStream(reader);
            closeStream(isr);
            closeStream(in);

            return sb.toString();
        } finally {
            if (channelExec.isConnected()) {
                channelExec.disconnect();
            }
            if (channel.isConnected()) {
                channel.disconnect();
            }
        }
    }

    /**
     * 执行一条命令
     */
    public String execCmd(String command) throws Exception {
        // 打开执行shell指令的通道
        Channel channel = session.openChannel("exec");
        ChannelExec channelExec = (ChannelExec) channel;
        channelExec.setCommand("source /etc/profile && source ~/.bash_profile && source ~/.bashrc &&  adb devices && locale charmap");
        channelExec.setCommand(command);
        channel.setInputStream(null);
        channelExec.setErrStream(System.err);
        // channel.setXForwarding();
        channel.connect();


        StringBuilder sb = new StringBuilder(16);
        try (InputStream in = channelExec.getInputStream(); InputStreamReader isr = new InputStreamReader(in, StandardCharsets.UTF_8); BufferedReader reader = new BufferedReader(isr)) {
            String buffer;
            while ((buffer = reader.readLine()) != null) {
                sb.append("\n").append(buffer);
            }

            //2023-02-21 关闭流
            closeStream(reader);
            closeStream(isr);
            closeStream(in);

            return sb.toString();
        } finally {
            if (channelExec != null && channelExec.isConnected()) {
                channelExec.disconnect();
            }
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }

    /**
     * 执行一条命令 获取错误流中的内容
     */
    public String execCmdErrContent(String command) throws Exception {
        // 打开执行shell指令的通道
        Channel channel = session.openChannel("exec");
        ChannelExec channelExec = (ChannelExec) channel;
        channelExec.setCommand(command);
        channel.setInputStream(null);
        ByteArrayOutputStream err = new ByteArrayOutputStream();
        channelExec.setErrStream(err);
        channel.connect();
        StringBuilder sb = new StringBuilder(16);
        try (InputStream in = channelExec.getErrStream(); InputStreamReader isr = new InputStreamReader(in, StandardCharsets.UTF_8); BufferedReader reader = new BufferedReader(isr)) {
            String buffer;
            while ((buffer = reader.readLine()) != null) {
                sb.append("\n").append(buffer);
            }

            //2023-02-21 关闭流
            closeStream(reader);
            closeStream(isr);
            closeStream(in);
            closeStream(err);

            if (sb.toString().contains("没有那个文件或目录")) {
                return "";
            } else {
                return sb.toString();
            }

        } finally {
            if (channelExec != null && channelExec.isConnected()) {
                channelExec.disconnect();
            }
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
        }
    }

    public void downloadFile(String saveDir, String downloadFile) throws JSchException, FileNotFoundException, SftpException {
        ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
        channelSftp.connect();
        LOGGER.info("start download channel file!");
        String directory = "/tmp";
        channelSftp.cd(directory);
//        String saveDir = "D:\\desktop\\" + System.currentTimeMillis() + ".txt";
        File file = new File(saveDir);
//        String downloadFile = "Test.java";
        channelSftp.get(downloadFile, new FileOutputStream(file));
        LOGGER.info("Download Success!");
        channelSftp.disconnect();
        LOGGER.info("end execute channel sftp!");
    }

    public String uploadFile(String dist, String localFileStr) throws JSchException, SftpException, FileNotFoundException {
        ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
        channelSftp.connect();
        LOGGER.info("start upload channel file!");
        channelSftp.cd(dist);
        File file = new File(localFileStr);
//        String uploadFileName = file.getName().replace(".", System.currentTimeMillis() + ".");
        String uploadFileName = file.getName();
        channelSftp.put(new FileInputStream(file), uploadFileName);
        LOGGER.info("Upload Success!");
        channelSftp.disconnect();
        LOGGER.info("end execute channel sftp!");
        return uploadFileName;
    }

    public void closeConnect() {
        if (session != null && session.isConnected()) {
            session.disconnect();
        }
    }

    private static void closeStream(Closeable stream) {
        if (stream != null) {
            try {
                stream.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
