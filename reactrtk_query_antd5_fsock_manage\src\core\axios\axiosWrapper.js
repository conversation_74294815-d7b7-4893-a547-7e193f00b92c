/**
 * 采用react toolkit query发送请求
 * @deprecated
 */
import axios from './axios'
import actions from "../../redux/actions";
import global from '../utils/global'
import Utils from "../utils/utils";
import Constants from "../utils/constants";

/**
 * 包装axios，增加遮罩
 * @type {AxiosStatic}
 */
let self = axios;
const axios_ = (config) => {
    let promise = self(config);

    // 如果0.5秒没完成数据加载，则显示遮罩，避免遮罩一闪而过
    setTimeout(() => {
        Utils.getStatePromise(promise).then((state) => {
            if (state === Constants.PROMISE_STATE.PENDING) {
                //开启遮罩
                global.store.dispatch(actions.loadingMask.showMask(true));
            }
        })
    }, 500);

    return promise.then(response => {
            //关闭遮罩
            global.store.dispatch(actions.loadingMask.showMask(false));
            return response;
        },
        error => {
            //关闭遮罩
            global.store.dispatch(actions.loadingMask.showMask(false));
            console.log('An error occurred.', error)
        });
};
export default axios_;
