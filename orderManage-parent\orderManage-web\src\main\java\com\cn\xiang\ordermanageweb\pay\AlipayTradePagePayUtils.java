package com.cn.xiang.ordermanageweb.pay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.cn.xiang.ordermanageweb.config.properties.AlipayConfigProperties;
import com.cn.xiang.ordermanageweb.spring.ApplicationContextHolder;
import com.cn.xiang.ordermanageweb.vo.PayVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/26 11:38
 */
public class AlipayTradePagePayUtils {

    public static String pay(PayVo payVo) throws AlipayApiException {
        // 初始化SDK
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        AlipayTradePagePayModel model = new AlipayTradePagePayModel();

        // 设置商户门店编号
//        model.setStoreId("NJ_001");

        // 设置订单绝对超时时间
//        model.setTimeExpire("2016-12-31 10:05:01");

        // 设置业务扩展参数
//        ExtendParams extendParams = new ExtendParams();
//        extendParams.setSysServiceProviderId("2088511833207846");
//        extendParams.setHbFqSellerPercent("100");
//        extendParams.setHbFqNum("3");
//        extendParams.setIndustryRefluxInfo("{\"scene_code\":\"metro_tradeorder\",\"channel\":\"xxxx\",\"scene_data\":{\"asset_name\":\"ALIPAY\"}}");
//        extendParams.setSpecifiedSellerName("XXX的跨境小铺");
//        extendParams.setRoyaltyFreeze("true");
//        extendParams.setCardType("S0JP0000");
//        model.setExtendParams(extendParams);

        // 设置订单标题
        model.setSubject(payVo.getSubject());

        // 设置请求来源地址,请求来源地址。如果使用ALIAPP的集成方式，用户中途取消支付会返回该地址。
//        model.setRequestFromUrl("https://");

        // 设置产品码,销售产品码，与支付宝签约的产品码名称。
        // 注：目前电脑支付场景下仅支持FAST_INSTANT_TRADE_PAY
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        // 设置PC扫码支付的方式
        model.setQrPayMode("1");

        // 设置商户自定义二维码宽度
        model.setQrcodeWidth(100L);

        // 设置请求后页面的集成方式
        model.setIntegrationType("PCWEB");

        // 设置订单包含的商品列表信息
        model.setGoodsDetail(payVo.getGoodsDetails());

        // 设置商户的原始订单号
//        model.setMerchantOrderNo("20161008001");

        // 设置二级商户信息
//        SubMerchant subMerchant = new SubMerchant();
//        subMerchant.setMerchantId("2088000603999128");
//        subMerchant.setMerchantType("alipay");
//        model.setSubMerchant(subMerchant);

        // 设置开票信息
//        InvoiceInfo invoiceInfo = new InvoiceInfo();
//        InvoiceKeyInfo keyInfo = new InvoiceKeyInfo();
//        keyInfo.setTaxNum("1464888883494");
//        keyInfo.setIsSupportInvoice(true);
//        keyInfo.setInvoiceMerchantName("ABC|003");
//        invoiceInfo.setKeyInfo(keyInfo);
//        invoiceInfo.setDetails("[{\"code\":\"*********\",\"name\":\"服饰\",\"num\":\"2\",\"sumPrice\":\"200.00\",\"taxRate\":\"6%\"}]");
//        model.setInvoiceInfo(invoiceInfo);

        // 设置商户订单号
        model.setOutTradeNo(payVo.getOutTradeNo());

        // 设置外部指定买家
//        ExtUserInfo extUserInfo = new ExtUserInfo();
//        extUserInfo.setCertType("IDENTITY_CARD");
//        extUserInfo.setCertNo("362334768769238881");
//        extUserInfo.setName("李明");
//        extUserInfo.setMobile("***********");
//        extUserInfo.setMinAge("18");
//        extUserInfo.setNeedCheckInfo("F");
//        extUserInfo.setIdentityHash("27bfcd1dee4f22c8fe8a2374af9b660419d1361b1c207e9b41a754a113f38fcc");
//        model.setExtUserInfo(extUserInfo);

        // 设置订单总金额
        model.setTotalAmount(payVo.getTotalAmount() + "");

        // 设置商户传入业务信息
//        model.setBusinessParams("{\"mc_create_trade_ip\":\"127.0.0.1\"}");

        // 设置优惠参数
//        model.setPromoParams("{\"storeIdType\":\"1\"}");

        AlipayConfigProperties alipayConfigProperties = ApplicationContextHolder.getBean(AlipayConfigProperties.class);
        request.setNotifyUrl(alipayConfigProperties.getNotifyUrl());
        request.setReturnUrl(alipayConfigProperties.getReturnUrl());

        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token
        // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

        AlipayTradePagePayResponse response = alipayClient.pageExecute(request, "POST");
        // 如果需要返回GET请求，请使用
        // AlipayTradePagePayResponse response = alipayClient.pageExecute(request, "GET");
        String pageRedirectionData = response.getBody();

        if (response.isSuccess()) {
            System.out.println("调用成功");
            return pageRedirectionData;
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            return diagnosisUrl;
        }
    }

    public static String tradeQuery(String outTradeNo, String tradeNo) throws AlipayApiException {
        // 初始化SDK
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();

        // 设置订单支付时传入的商户订单号
        model.setOutTradeNo(outTradeNo);

        // 设置查询选项
        List<String> queryOptions = new ArrayList<>();
        queryOptions.add("trade_settle_info");
        model.setQueryOptions(queryOptions);

        // 设置支付宝交易号
        model.setTradeNo(tradeNo);

        request.setBizModel(model);
        AlipayTradeQueryResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            System.out.println(diagnosisUrl);
        }
        return response.getBody();
    }

    private static AlipayConfig getAlipayConfig() {
        AlipayConfigProperties alipayConfigProperties = ApplicationContextHolder.getBean(AlipayConfigProperties.class);
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(alipayConfigProperties.getServerUrl());
        alipayConfig.setAppId(alipayConfigProperties.getAppId());
        alipayConfig.setPrivateKey(alipayConfigProperties.getPrivateKey());
        alipayConfig.setFormat(alipayConfigProperties.getFormat());
        alipayConfig.setAlipayPublicKey(alipayConfigProperties.getAlipayPublicKey());
        alipayConfig.setCharset(alipayConfigProperties.getCharset());
        alipayConfig.setSignType(alipayConfigProperties.getSignType());
        return alipayConfig;
    }
}
