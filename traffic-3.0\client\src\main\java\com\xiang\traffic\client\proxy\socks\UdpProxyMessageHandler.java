package com.xiang.traffic.client.proxy.socks;

import com.xiang.traffic.client.proxy.ProxyRequest;
import com.xiang.traffic.client.proxy.ProxyRequestManager;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.ReferenceCountUtil;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:14
 */
public class UdpProxyMessageHandler extends ChannelInboundHandlerAdapter {

    private final ProxyRequestManager proxyRequestManager;

    private final Map<InetSocketAddress, ProxyRequest> requestCache = new HashMap<>();

    public UdpProxyMessageHandler(ProxyRequestManager proxyRequestManager) {
        this.proxyRequestManager = Objects.requireNonNull(proxyRequestManager);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof UdpProxyMessage) {
            try {
                channelRead0(ctx, (UdpProxyMessage) msg);
            } finally {
                ReferenceCountUtil.release(msg);
            }
        } else {
            ctx.fireChannelRead(msg);
        }
    }


    protected void channelRead0(ChannelHandlerContext ctx, UdpProxyMessage message) throws IOException {
        String host = message.getHost();
        int port = message.getPort();
        ByteBuf data = message.getData();

        InetSocketAddress address = new InetSocketAddress(host, port);
        ProxyRequest request = requestCache.get(address);
        if (request != null) {
            if (!request.isClose()) {
                request.transferClientMessage(data);
                return;
            }

            requestCache.remove(address);
        }

        request = new ProxyRequest(host, port, ctx.channel(), ProxyRequest.Protocol.UDP);
        requestCache.put(address, request);
        request.transferClientMessage(data);
        proxyRequestManager.publish(request);

        cleanClosedRequest();
    }


    private void cleanClosedRequest() {
        requestCache.values().removeIf(ProxyRequest::isClose);
    }


    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        requestCache.values().forEach(ProxyRequest::close);
        requestCache.clear();
    }
}
