package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.mapper.IGroupMapper;
import com.cn.xiang.fsockmanageweb.mapper.ITrafficCapacityMapper;
import com.cn.xiang.fsockmanageweb.po.GroupPo;
import com.cn.xiang.fsockmanageweb.po.TrafficCapacityPo;
import com.cn.xiang.fsockmanageweb.po.TrafficRulePo;
import com.cn.xiang.fsockmanageweb.po.UserPo;
import com.cn.xiang.fsockmanageweb.mapper.ITrafficRuleMapper;
import com.cn.xiang.fsockmanageweb.mapper.IUserMapper;
import com.cn.xiang.fsockmanageweb.services.IGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class GroupServiceImpl extends ServiceImpl<IGroupMapper, GroupPo> implements IGroupService {
    @Autowired
    private IUserMapper userMapper;

    @Autowired
    private ITrafficCapacityMapper trafficCapacityMapper;

    @Autowired
    private ITrafficRuleMapper trafficRuleMapper;

    public GroupPo findGroupByName(String groupName) {
        return baseMapper.findGroupByName(groupName);
    }

    public GroupPo findGroupByUserName(String username) {
        LambdaQueryWrapper<UserPo> wrapper = new LambdaQueryWrapper<UserPo>();
        wrapper.eq(UserPo::getUsername, username);
        UserPo userPo = userMapper.selectOne(wrapper);

        if (userPo != null) {
            return this.getById(userPo.getGroupId());
        }
        return null;
    }

    @Override
    public void assignTrafficRule(String groupId, String trafficRuleId) {
        GroupPo groupPo = this.getById(groupId);

        TrafficRulePo trafficRulePo = new TrafficRulePo();
        trafficRulePo.setId(trafficRuleId);
        groupPo.setTrafficRulePo(trafficRulePo);

        this.baseMapper.updateById(groupPo);
    }

    @Override
    public void assignTrafficCapacity(String groupId, String trafficCapacityId) {
        GroupPo groupPo = this.getById(groupId);

        TrafficCapacityPo trafficCapacityPo = new TrafficCapacityPo();
        trafficCapacityPo.setId(trafficCapacityId);
        groupPo.setTrafficCapacityPo(trafficCapacityPo);

        this.baseMapper.updateById(groupPo);
    }

    @Override
    public void saveWithDefaultTrafficCapacity(GroupPo groupPo) {
        //设置默认流量容量 49 GB
        TrafficCapacityPo trafficCapacityPo = trafficCapacityMapper.selectById("1754478760872460223");
        groupPo.setTrafficCapacityPo(trafficCapacityPo);

        //设置默认流量规则
        LambdaQueryWrapper<TrafficRulePo> wrapper1 = new LambdaQueryWrapper<TrafficRulePo>();
        wrapper1.like(TrafficRulePo::getName, "no limit");
        TrafficRulePo trafficRulePo = trafficRuleMapper.selectOne(wrapper1);
        groupPo.setTrafficRulePo(trafficRulePo);

        this.save(groupPo);
    }

    @Override
    public List<GroupPo> listBySearch(String search) {
        LambdaQueryWrapper<GroupPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(GroupPo::getName, search);
        return this.list(wrapper);

//        return this.baseMapper.listBySearch(search);
    }
}
