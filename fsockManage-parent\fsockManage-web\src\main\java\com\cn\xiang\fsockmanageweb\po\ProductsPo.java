package com.cn.xiang.fsockmanageweb.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;

/**
 * <AUTHOR>
 * @date 2024/6/7 19:45
 */
@TableName(value = "tb_products")
public class ProductsPo extends BaseEntity {
    private String name;
    private int monthlyTraffic;
    private String bandwidth;
    private double price;
    private int stock;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getStock() {
        return stock;
    }

    public void setStock(int stock) {
        this.stock = stock;
    }

    public int getMonthlyTraffic() {
        return monthlyTraffic;
    }

    public void setMonthlyTraffic(int monthlyTraffic) {
        this.monthlyTraffic = monthlyTraffic;
    }

    public String getBandwidth() {
        return bandwidth;
    }

    public void setBandwidth(String bandwidth) {
        this.bandwidth = bandwidth;
    }
}
