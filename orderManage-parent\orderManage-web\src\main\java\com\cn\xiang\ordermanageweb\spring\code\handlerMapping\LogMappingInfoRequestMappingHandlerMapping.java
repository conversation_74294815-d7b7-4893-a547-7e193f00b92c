package com.cn.xiang.ordermanageweb.spring.code.handlerMapping;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;

/**
 *
 * 覆盖registerHandlerMethod方法，完成请求映射日志打印
 *
 * <AUTHOR>
 * @date 2021年03月16日 16:05:48
 * @version 1.0
 *
 */
public class LogMappingInfoRequestMappingHandlerMapping
        extends RequestMappingHandlerMapping {
    private static final Logger LOGGER = LoggerFactory
            .getLogger(LogMappingInfoRequestMappingHandlerMapping.class);
    private boolean isLogFirst = true;

    @Override
    public void registerMapping(RequestMappingInfo mapping, Object handler,
            Method method) {
        super.registerMapping(mapping, handler, method);

        logRequestMappingInfo(handler, method, mapping);
    }

    @Override
    protected void registerHandlerMethod(Object handler, Method method,
            RequestMappingInfo mapping) {
        super.registerHandlerMethod(handler, method, mapping);

        logRequestMappingInfo(handler, method, mapping);
    }

    /**
     * 打印handler映射日志
     *
     * @param handler
     * @param method
     * @param mapping
     */
    private void logRequestMappingInfo(Object handler, Method method,
            RequestMappingInfo mapping) {

        if (LOGGER.isInfoEnabled()) {
            if (isLogFirst) {
                LOGGER.info(
                        "--------------------------------handlerMapping LOG---------------------------------------");
                isLogFirst = false;
            }

            String handlerName = null;
            Class<?> handlerClazz = null;
            if (handler instanceof String) {
                handlerName = (String) handler;
                handlerClazz = getApplicationContext().getType(handlerName);
            } else if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                handlerClazz = handlerMethod.getBeanType();
            }
            LOGGER.info(String.format("%s is mapped to %s.%s()", mapping,
                    handlerClazz.getTypeName(), method.getName()));
        }
    }
}
