package com.cn.xiang.fsockmanageweb.vo;


import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/4/8 13:51
 */
public class ServerVo {
    @NotEmpty(groups = ModifyGroup.class)
    private String id;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String ip;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String port;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String certPort;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String shellPort;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String username;
    @NotEmpty(groups = {AddGroup.class, ModifyGroup.class})
    private String password;
    private String groupName;
    @Min(value = 1, groups = {AddGroup.class, ModifyGroup.class})
    private int maxUserCount;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    private String groupId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getCertPort() {
        return certPort;
    }

    public void setCertPort(String certPort) {
        this.certPort = certPort;
    }

    public String getShellPort() {
        return shellPort;
    }

    public void setShellPort(String shellPort) {
        this.shellPort = shellPort;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public int getMaxUserCount() {
        return maxUserCount;
    }

    public void setMaxUserCount(int maxUserCount) {
        this.maxUserCount = maxUserCount;
    }

    public interface AddGroup {
    }

    public interface ModifyGroup {
    }
}
