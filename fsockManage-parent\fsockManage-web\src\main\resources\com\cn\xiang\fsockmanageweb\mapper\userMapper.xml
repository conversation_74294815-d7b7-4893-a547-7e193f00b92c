<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.fsockmanageweb.mapper.IUserMapper">
    <resultMap id="userResultMap" type="com.cn.xiang.fsockmanageweb.po.UserPo">
        <!-- <id column="id" property="id"/>
         <result column="group_id" property="groupId"/>
         <result column="created_by" property="createdBy"/>
         <result column="created_time" property="createdTime"/>
         <result column="updated_by" property="updatedBy"/>
         <result column="updated_time" property="updatedTime"/>
         <result column="username" property="username"/>
         <result column="password" property="password"/>-->

        <association property="trafficRulePo" column="traffic_rule_id"
                     select="com.cn.xiang.fsockmanageweb.mapper.ITrafficRuleMapper.selectById"/>
    </resultMap>

    <select id="findUserById1" parameterType="String" resultType="com.cn.xiang.fsockmanageweb.po.UserPo">
        select * from tb_user where id = #{id} and deleted=0
    </select>
    <select id="findUserByGroupNameAndUsername" parameterType="String"
            resultType="com.cn.xiang.fsockmanageweb.po.UserPo">
        select u.* from tb_user u, tb_group g where g.id = u.group_id and g.name = #{groupName} and u.username=
        #{username} and u.deleted=0 and g.deleted=0
    </select>

    <select id="findUsersByGroupName" parameterType="String" resultMap="userResultMap">
        select u.* from tb_user u, tb_group g where g.id = u.group_id and g.name = #{groupName} and u.deleted=0 and
        g.deleted=0
    </select>

    <resultMap id="userResultMap1" type="java.util.Map">
        <id column="id" property="id"/>
        <result column="group_id" property="groupId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="groupName" property="groupName"/>
        <result column="trafficRuleId" property="trafficRuleId"/>
        <result column="trafficRuleName" property="trafficRuleName"/>
        <result column="status" property="status"/>
        <result column="no_expiration" property="noExpiration"/>
        <result column="expiration" property="expiration"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <select id="findPages" resultMap="userResultMap1">
        select u.*, g.name as groupName,t.id as trafficRuleId, t.name as trafficRuleName from tb_user u
        left outer join tb_group as g on(g.id = u.group_id)
        left outer join tb_traffic_rule as t on(t.id = u.traffic_rule_id)
        where (
        g.name like concat('%',#{pageVo.search},'%')
        or u.username like concat('%',#{pageVo.search},'%')
        or t.name like concat('%',#{pageVo.search},'%')
        )
        and u.deleted=0
        limit #{pageVo.offset},#{pageVo.pageSize}
    </select>

    <select id="findDeliverPages" resultMap="userResultMap1">
        select u.*, g.name as groupName,t.id as trafficRuleId, t.name as trafficRuleName from tb_user u
        left outer join tb_group as g on(g.id = u.group_id)
        left outer join tb_traffic_rule as t on(t.id = u.traffic_rule_id)
        where (
        g.name like concat('%',#{pageVo.search},'%')
        or u.username like concat('%',#{pageVo.search},'%')
        or t.name like concat('%',#{pageVo.search},'%')
        )
        and u.delivered = 0
        and u.deleted=0
        limit #{pageVo.offset},#{pageVo.pageSize}
    </select>

    <select id="findTotalBySearch" resultType="java.lang.Long">
        select count(u.id) from tb_user u
        left outer join tb_group as g on(g.id = u.group_id)
        left outer join tb_traffic_rule as t on(t.id = u.traffic_rule_id)
        where
        (
        g.name like concat('%',#{pageVo.search},'%')
        or u.username like concat('%',#{pageVo.search},'%')
        or t.name like concat('%',#{pageVo.search},'%')
        )
        and u.deleted=0
    </select>

</mapper>
