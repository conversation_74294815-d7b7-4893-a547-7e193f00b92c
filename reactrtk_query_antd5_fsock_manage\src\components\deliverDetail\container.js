import {connect} from "react-redux";
import LIFECYCLE_METHOD_ENUM from "../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../redux/store/request/services/base/base";
import Utils from "../../core/utils/utils";
import DeliverModal from './DeliverModal'

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getDeliverDetailData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            deliverData: data.deliverData
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期
            // const {unsubscribe} = dispatch(baseApi.endpoints.getOrderDetailData.initiate(undefined, {
            //     //subscribe属性可以配置不把数据存储到store
            //     // subscribe: false,
            //     forceRefetch: true
            // }));
            // apiUnsubscribeSet.add(unsubscribe);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
        },
        deliverConfirm: function (e) {
            let orderId = this.props.deliverData.orderId;
            dispatch(baseApi.endpoints.deliverConfirm.initiate({orderId: orderId})).then((res) => {
                if(res.status){
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "确认订单成功.",
                        delay: 3,
                    });

                    this.setState({
                        visible: false
                    }, () => {
                        const {unsubscribe} = dispatch(baseApi.endpoints.getOrderListData.initiate(undefined, {
                            //subscribe属性可以配置不把数据存储到store
                            // subscribe: false,
                            forceRefetch: true
                        }));
                        apiUnsubscribeSet.add(unsubscribe);
                    });
                }else{
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: res.errorMsg,
                        delay: 3,
                    });

                }
            });
        }
    };
};

let Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(DeliverModal);
export default Container;
