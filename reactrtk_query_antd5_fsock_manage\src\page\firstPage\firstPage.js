import React, {Component} from "react";
import LoginPage from "../loginPage/loginPage";
import IndexPage from "../indexPage/indexPage";

/**
 * 首屏页面，用于协调登录页与首页
 */
class FirstPage extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isLogined: false
        };
    }

    componentDidMount() {
        //TODO send a requset to check whether user has logined in.
      this.props.isSysUserLogin.call(this);
    }

    showIndexPage = (isShow) => {
        this.setState({
            isLogined: isShow
        });
    }


    loginSuccessCallback = () => {
        console.log('login success');
        this.setState({
            isLogined: true
        });
    }

    render() {
        return (
            <>
                {/*登录页总是渲染，但是如果用户没登录，则显示；已经登录，则隐藏*/}
                <LoginPage isVisable={!this.state.isLogined} loginSuccessCallback={this.loginSuccessCallback}/>
                {/*首页只有用户登录后才会渲染*/}
                {this.state.isLogined && <IndexPage/>}
            </>
        );
    }
}

export default FirstPage;
