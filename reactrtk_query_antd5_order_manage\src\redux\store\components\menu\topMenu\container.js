import {connect} from 'react-redux'
import TopMenu_bak from '../../../../../components/menu/TopMenu';
import Utils from "../../../../../core/utils/utils";
import {baseApi} from "../../../request/services/base/base";

//已经没有使用
const mapStateToProps = (state, ownProps) => {
    // if (Utils.isFetched(state, 'menu/topMenuData')) {
    //     return {
    //         data: state.fetchBySubreddit['menu/topMenuData'].data
    //     }
    // }
    // const result = Utils.selectDataByParam('getTopMenuData', state);
    const result = Utils.selectLatestDataByEndpoint('getTopMenuData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            data: data
        };
    }

    return ownProps;
};

let apiUnsubscribeSet = new Set();

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上，下面是把updateState方法注入到App组件props中
    return {
        loadMenu: () => {
            // dispatch(actions.request.fetch('menu/topMenuData', {
            //     url: '/topMenu',
            //     method: 'GET',
            //     successCallback: (json) => {
            //         console.log('successCallback');
            //         console.log(json);
            //     }
            // }));
            const {unsubscribe} = dispatch(baseApi.endpoints.getTopMenuData.initiate());
            apiUnsubscribeSet.add(unsubscribe);
        },
        loadLeftMenu: (pId) => {
            // dispatch(actions.request.fetch('menu/leftMenuData', {
            //     url: '/leftMenu',
            //     method: 'GET',
            //     params: {
            //         pId: pId
            //     }
            // }));
            const {unsubscribe} = dispatch(baseApi.endpoints.getLeftMenuData.initiate({
                pId: pId
            }, {
                forceRefetch: true
            }));
            apiUnsubscribeSet.add(unsubscribe);
        },
        componentDidMount: function () {
            this.props.loadMenu();
        },
        componentWillUnmount: function () {
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }
    }
};

const Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(TopMenu_bak);

export default Container;
