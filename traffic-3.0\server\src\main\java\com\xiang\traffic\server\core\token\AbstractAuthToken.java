package com.xiang.traffic.server.core.token;

import com.xiang.traffic.server.enumeration.ClientAuthType;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/2/6 21:29
 */
public abstract class AbstractAuthToken<T extends IUser> implements ITrafficLimit {
    private final LocalDateTime loginDateTime;

    private final String remoteIp;

    private final T principle;


    public AbstractAuthToken(T principle, String remoteIp) {
        this.principle = principle;
        this.remoteIp = remoteIp;
        this.loginDateTime = LocalDateTime.now();
    }

    public LocalDateTime getLoginDateTime() {
        return loginDateTime;
    }

    /**
     * 获取远程连接用户ip
     *
     * @return
     */
    public String getRemoteIp() {
        return remoteIp;
    }

    @Override
    public int getWriteLimit() {
        return this.principle.getWriteLimit();
    }

    @Override
    public int getReadLimit() {
        return this.principle.getReadLimit();
    }

    /**
     * 获取令牌的主体
     *
     * @return
     */
    public T getPrinciple() {
        return principle;
    }

    /***
     * 获取令牌类型
     *
     * <AUTHOR>
     * @date 2024/2/6 22:32
     *
     */
    public abstract ClientAuthType getTokenType();

}
