package com.cn.xiang.ordermanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.ordermanageweb.mapper.IServerMapper;
import com.cn.xiang.ordermanageweb.po.ServerPo;
import com.cn.xiang.ordermanageweb.services.IServerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional
public class ServerServiceImpl extends ServiceImpl<IServerMapper, ServerPo> implements IServerService {

    @DS("flyingsocks")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public ServerPo getById1(String serverId) {
        return this.getById(serverId);
    }
}
