import React, {Component} from "react";
import {Form, Input, Button, Checkbox} from 'antd';
import {
    AlipayCircleOutlined,
    LockOutlined,
    PhoneOutlined,
    TaobaoCircleOutlined,
    WechatOutlined,
} from '@ant-design/icons';
import './phoneLoginForm.less';
import Utils from "../../../../core/utils/utils";
import ThirdPartLogin from "../thirdPartLogin/thirdPartLogin";

const LIST_ITEMS = [
    {
        Cmpt: TaobaoCircleOutlined,
        key: 'Taobao',
        props: {
            onClick: (e) => {
                console.log('click Taobao');
            }
        }
    },
    {
        Cmpt: AlipayCircleOutlined,
        key: 'Alipay',
        props: {
            onClick: (e) => {
                console.log('click Alipay');
            }
        }
    },
    {
        Cmpt: WechatOutlined,
        key: 'Wechat',
        props: {
            onClick: (e) => {
                console.log('click Wechat');
            }
        }
    },
];

class PasswordLoginForm extends Component {
    constructor(props) {
        super(props);

        this.captchaBtnRef = React.createRef();
        this.formRef = React.createRef();
        this.state = {
            isPasswordVisible: false
        }
    }

    onLoginFormSuccess = (values) => {
        console.log(values)

        //重置表单
        let formInst = this.formRef.current;
        formInst.resetFields();

        this.props.loginSuccessCallback && this.props.loginSuccessCallback();
    }
    onLoginFormFailed = (error) => {
        console.log(error)
    }

    onCaptchaBtnClick = (e) => {
        let btnEle = this.captchaBtnRef.current;

        this.controlCaptchaBtnDisable(btnEle);
    }

    controlCaptchaBtnDisable = (btnEle) => {
        btnEle.disabled = true;
        let textTmp = btnEle.innerText;

        let timer = {};
        let counter = 5;
        Utils.setInterval(() => {
            if (counter === -1) {
                btnEle.innerText = textTmp;
                btnEle.disabled = false;

                return false;
            }

            btnEle.innerText = counter-- + 's';
            return true;
        }, 1000, timer, true);
    }

    render() {
        return (
            <Form
                ref={this.formRef}
                name="login-form-phone"
                className="login-form-phone"
                initialValues={{remember: true}}
                onFinish={this.onLoginFormSuccess}
                onFinishFailed={this.onLoginFormFailed}
                autoComplete="off"
                scrollToFirstError={true}
            >
                <Form.Item
                    name="phone"
                    rules={[{required: true, message: 'Please input your phone!'}]}
                >
                    <Input prefix={<PhoneOutlined className="site-form-item-icon"/>} placeholder="phone"/>
                </Form.Item>
                <Input.Group compact>
                    <Form.Item
                        name="captcha"
                        rules={[{required: true, message: 'Please input your captcha!'}]}
                    >
                        <Input prefix={<LockOutlined className="site-form-item-icon"/>}
                               type="password"
                               placeholder="captcha"/>
                    </Form.Item>
                    <Form.Item name={'captchaBtn'}>
                        <Button ref={this.captchaBtnRef} onClick={this.onCaptchaBtnClick} className={'captcha_btn'}
                                type={'primary'}>获取验证码</Button>
                    </Form.Item>
                </Input.Group>

                <Form.Item>
                    <Form.Item name="remember" valuePropName="checked" noStyle>
                        <Checkbox>自动登录</Checkbox>
                    </Form.Item>

                    <a className="login-form-forgot" href="#">
                        忘记密码？
                    </a>
                </Form.Item>

                <Form.Item>
                    <Button type="primary" htmlType="submit" className="login-form-button">
                        登录
                    </Button>
                    {/*或者 <a href="">注册</a>*/}
                </Form.Item>
                <ThirdPartLogin labelText={'其他登录方式：'} listItems={LIST_ITEMS}/>
            </Form>
        );
    }
}

export default PasswordLoginForm;