import React, {Component} from 'react';
import pureStateData<PERSON>rapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {Button, Form, Input, Space, Table} from "antd";
import './index.less'

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource',
    itemKeyName: 'key',
    isForwardedRef: false
});

class UserGroupAssignModule extends Component {
    constructor(props) {
        super(props);
        this.state = {
            btnDisabled: true, selectedRowKeys: [],
        }
        this.addModalRef = React.createRef();
        this.tableRef = React.createRef();
        this.searchInputFormRef = React.createRef();
    }

    selectedRows;

    onChange = (selectedRowKeys, selectedRows) => {
        this.selectedRows = selectedRows;

        this.setState({
            selectedRowKeys: selectedRowKeys, btnDisabled: false,
        })
    }

    render() {
        return (<div className={"user-group-assign-module"}>
            <Space style={{marginBottom: 16}}>
                <Button type="primary" disabled={this.state.btnDisabled}
                        onClick={this.props.assignBtnOnClick.bind(this)}>
                    分配
                </Button>
                <Form ref={this.searchInputFormRef}
                      style={{width: '230px'}}
                      labelCol={{span: 0}}
                      wrapperCol={{span: 30}}
                      name="searchInputForm"
                >
                    <Form.Item name='search'>
                        <Input.Search ref={this.searchInput} placeholder={'groupName'}
                                      defaultValue={''}
                                      onSearch={this.props.inputOnSearch.bind(this)}/>
                    </Form.Item>
                </Form>
            </Space>
            <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource} rowSelection={{
                type: 'radio', selectedRowKeys: this.state.selectedRowKeys, onChange: this.onChange
            }}>
                <Column align={"center"} title="组名" dataIndex="name"/>
                <Column align={"center"} title="流量规则" dataIndex={['trafficRulePo', 'name']}/>
            </TableWrapper>
        </div>)
    }
}

export default UserGroupAssignModule;
