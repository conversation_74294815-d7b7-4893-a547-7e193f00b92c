package com.cn.xiang.fsockmanageweb.task.utils.enum_;

/**
 * <AUTHOR>
 * @date 2024/7/3 15:18
 */
public enum ScheduleStatus {
    PAUSE(0, "stopped"),
    RUNNING(1, "running");

    private int code;
    private String desc;

    ScheduleStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
