import React, {Component} from 'react';
import LineChart from '../../components/echarts/lineChart/container'
import PieChart from '../../../../components/echarts/pieChart'
import ChinaChart from "../../../../components/echarts/chinaChart/chinaChart";
import BarChart from "../../../../components/echarts/barChart";
import './index.less'

class IndexModule5 extends Component {
    constructor(props) {
        super(props);

        this.lineChartOption = {
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLabel: {interval: 0}
            },
            series: [{
                data: [],
                type: 'line',
                areaStyle: {}
            }],
        };

        this.pieChartOption = {
            title: {
                text: 'Referer of a Website',
                subtext: 'Fake Data',
                left: 'center'
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: 'Access From',
                    type: 'pie',
                    radius: '50%',
                    data: [
                        {value: 1048, name: 'Search Engine'},
                        {value: 735, name: 'Direct'},
                        {value: 580, name: 'Email'},
                        {value: 484, name: 'Union Ads'},
                        {value: 300, name: 'Video Ads'},
                        {value: 150, name: 'Video Ads ccccc'}
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };

        this.chinaChartOption = {};
        this.chinaChartData = [
            {name: '北京', value: '100'}, {name: '天津', value: this.randomVal()},
            {name: '上海', value: this.randomVal()}, {name: '重庆', value: this.randomVal()},
            {name: '河北', value: this.randomVal()}, {name: '河南', value: this.randomVal()},
            {name: '云南', value: this.randomVal()}, {name: '辽宁', value: this.randomVal()},
            {name: '黑龙江', value: this.randomVal()}, {name: '湖南', value: this.randomVal()},
            {name: '安徽', value: this.randomVal()}, {name: '山东', value: this.randomVal()},
            {name: '新疆', value: this.randomVal()}, {name: '江苏', value: this.randomVal()},
            {name: '浙江', value: this.randomVal()}, {name: '江西', value: this.randomVal()},
            {name: '湖北', value: this.randomVal()}, {name: '广西', value: this.randomVal()},
            {name: '甘肃', value: this.randomVal()}, {name: '山西', value: this.randomVal()},
            {name: '内蒙古', value: this.randomVal()}, {name: '陕西', value: this.randomVal()},
            {name: '吉林', value: this.randomVal()}, {name: '福建', value: this.randomVal()},
            {name: '贵州', value: this.randomVal()}, {name: '广东', value: this.randomVal()},
            {name: '青海', value: this.randomVal()}, {name: '西藏', value: this.randomVal()},
            {name: '四川', value: this.randomVal()}, {name: '宁夏', value: this.randomVal()},
            {name: '海南', value: this.randomVal()}, {name: '台湾', value: this.randomVal()},
            {name: '香港', value: this.randomVal()}, {name: '澳门', value: this.randomVal()}
        ];


        // let yMax = 500;
        // let dataShadow = [];
        // for (let i = 0; i < data.length; i++) {
        //     dataShadow.push(yMax);
        // }
        this.barChartOption = {};
        this.barChartData = [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149, 210, 122, 133, 334, 198, 123, 125, 220];
    }

    randomVal = () => {
        return Math.ceil(Math.random() * Math.pow(10, 3));
    }


    render() {
        return <div id={'chart-module-ctn'} style={
            {
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'wrap',
                width: '100%',
                height: '100%',
            }
        }>
            <div className={'chart-module-item-ctn'}>
                <LineChart style={{
                    width: '100%',
                    height: '100%'
                }} option={this.lineChartOption} id={'lineChart'}/>
            </div>

            <div className={'chart-module-item-ctn'}>
                <PieChart style={{
                    width: '100%',
                    height: '100%',
                }} option={this.pieChartOption} id={'pieChart'}/>
            </div>


            <div className={'chart-module-item-ctn'}>
                <ChinaChart style={{
                    width: '100%',
                    height: '100%',
                }} option={this.chinaChartOption}
                            data={this.chinaChartData} id={'chinaChart'}
                />
            </div>

            <div className={'chart-module-item-ctn'}>
                <BarChart style={{
                    width: '100%',
                    height: '100%',
                }} option={this.barChartOption}
                          data={this.barChartData} id={'barChart'}/>
            </div>
        </div>;
    }
}

export default IndexModule5;
