#!/bin/bash

echo "Testing Traffic Client Native Executable..."

if [ ! -f "target/traffic-client" ]; then
    echo "ERROR: Native executable not found at target/traffic-client"
    echo "Please run build-native-linux.sh first"
    exit 1
fi

echo "Found native executable: target/traffic-client"

# 显示文件信息
echo "File information:"
ls -lh target/traffic-client

echo ""
echo "Testing executable (will run for 5 seconds then terminate)..."
echo ""

# 启动程序并在5秒后终止
./target/traffic-client &
PID=$!
sleep 5
kill $PID 2>/dev/null

echo ""
echo "Test completed. If no errors appeared above, the native executable is working correctly."
