package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.po.UserPo;
import com.cn.xiang.fsockmanageweb.services.IUserService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import com.cn.xiang.fsockmanageweb.vo.UserVo;
import com.cn.xiang.fsockmanageweb.vo.PageVo;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping("/api_v1/user")
public class UserController extends ControllerSupport {
    @Autowired
    private IUserService userService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list(PageVo pageVo) {
        ObjectNode objectNode = userService.findPages(pageVo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", objectNode);
        return message;
    }

    @RequestMapping(value = "deliverList", method = RequestMethod.GET)
    public Object deliverList(PageVo pageVo) {
        ObjectNode objectNode = userService.findDeliverPages(pageVo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", objectNode);
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.POST)
    public Object add(@RequestBody @Validated(value = {UserVo.AddGroup.class}) UserVo userVo) {
        UserPo userPo = new UserPo();
        BeanUtils.copyProperties(userVo, userPo);
        userService.saveUserWithGroupName(userPo, userVo.getGroupName());

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Object modify(@RequestBody @Validated(value = {UserVo.ModifyGroup.class}) UserVo userVo) {
        UserPo userPo = new UserPo();
        BeanUtils.copyProperties(userVo, userPo);
        userService.updateWithGroupName(userPo, userVo.getGroupName());

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public Object del(@Validated @NotEmpty String id) {
        userService.removeById(id);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "/assignUserGroup", method = RequestMethod.POST)
    public Object assignUserGroup(@Validated @NotEmpty String userId, @NotEmpty String groupId) {
        userService.assignUserGroup(userId, groupId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "/assignUserTrafficRule", method = RequestMethod.POST)
    public Object userTrafficRuleAssign(@Validated @NotEmpty String userId, @NotEmpty String trafficRuleId) {
        userService.assignUserTrafficRule(userId, trafficRuleId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "/isUserExist", method = RequestMethod.POST)
    public Object userTrafficRuleAssign(@Validated @NotEmpty String username) {
        boolean isUserExist = userService.isUserExist(username);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("isUserExist", isUserExist);
        return message;
    }
}
