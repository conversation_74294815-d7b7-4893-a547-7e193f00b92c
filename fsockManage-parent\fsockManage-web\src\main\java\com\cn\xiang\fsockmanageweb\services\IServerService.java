package com.cn.xiang.fsockmanageweb.services;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.ServerPo;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/2/5 15:37
 */
public interface IServerService extends IService<ServerPo> {
    ArrayNode listWithUserCount(String search);

    void addServer(ServerPo serverPo);

    public void uploadZipToServer(String id, String packageResourceId);

    public void modifyServerConfig(String id);

    public void installDockerToServer(String id);

    public void startServer(String id);

    public void autoStart(String id,String packageResourceId);

    public  void stopServer(String id);

    ServerPo findServerByUserId(String userId);

    @DS("default")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    ServerPo getById1(String serverId);
}
