package com.cn.xiang.fsockmanageweb.services.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.xiang.fsockmanageweb.mapper.IOrdersMapper;
import com.cn.xiang.fsockmanageweb.po.OrdersPo;
import com.cn.xiang.fsockmanageweb.services.IOrdersService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:47
 */
@Service
@Transactional(propagation = Propagation.REQUIRES_NEW)
@DS("order")
public class OrdersServiceImpl extends ServiceImpl<IOrdersMapper, OrdersPo> implements IOrdersService {
    public List<Map<String, String>> listOrders() {
        List<Map<String, String>> list = this.baseMapper.listOrders();
        for (Map<String, String> map : list) {
            String totalAmount = map.get("total_amount");
            String createdTime = map.get("created_time");
            String paymentDate = map.get("payment_date");
            map.remove("total_amount");
            map.remove("created_time");
            map.remove("payment_date");

            map.put("totalAmount", totalAmount);
            map.put("createdTime", createdTime);
            map.put("paymentDate", paymentDate);
        }
        return list;
    }


    @Override
    public OrdersPo getById1(String orderId) {
        //@DS注解中的holder是能切换的,DynamicDataSourceContextHolder.push("order");
        //但直接使用getById的话，貌似@Transactional(propagation = Propagation.REQUIRES_NEW)无法生效，没有重新获取连接
        return this.getById(orderId);
    }

    @Override
    public void updateById1(OrdersPo ordersPo) {
        this.updateById(ordersPo);
    }
}
