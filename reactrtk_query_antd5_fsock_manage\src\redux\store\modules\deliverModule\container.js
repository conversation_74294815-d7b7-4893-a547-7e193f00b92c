import UserModule from './index';
import Utils from '../../../../core/utils/utils';
import ModuleContainerCreator from "../../../../core/creator/ModuleContainerCreator";
import LIFECYCLE_METHOD_ENUM from "../../../../core/utils/LifecycleMethodEnum";
import {baseApi} from "../../request/services/base/base";
import SimpleRouter from "../../../../core/router/smpleRouter";

const mapStateToProps = (state, ownProps) => {
    const result = Utils.selectLatestDataByEndpoint('getUnDeliveredUserListData', baseApi, state);
    const {data, isSuccess} = result;
    if (isSuccess) {
        return {
            tableData1: data.list.records,
            total: data.list.total
        };
    }
    return ownProps;
};

let apiUnsubscribeSet = new Set();
let initData = function () {
    window.setTimeout(() => {
        let inputEle = this.searchInput.current.input;
        let parentEle = inputEle.parentElement;
        let buttonEle = parentEle.querySelector('.ant-input-group-addon button');
        buttonEle.click();
    }, 100);
};

let orderId = '';

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        [LIFECYCLE_METHOD_ENUM.MODULE_MOUNTED]: function (moduleParam) {
            //模型挂载生命周期

            // const {unsubscribe} = dispatch(baseApi.endpoints.getUserListData.initiate({
            //     page: 1,
            //     pageSize: 10,
            //     search: ''
            // }, {
            //     //subscribe属性可以配置不把数据存储到store
            //     // subscribe: false,
            //     forceRefetch: true
            // }));
            // apiUnsubscribeSet.add(unsubscribe);
            orderId = moduleParam.orderId || '';
            initData.call(this);

        }, [LIFECYCLE_METHOD_ENUM.MODULE_PAUSE]: function (moduleParam) {
            //模型暂停生命周期
        }, [LIFECYCLE_METHOD_ENUM.MODULE_RESUME]: function (moduleParam) {
            //模型恢复生命周期
            // this.setState({page: 1, pageSize: 10});
            // const {unsubscribe} = dispatch(baseApi.endpoints.getUserListData.initiate({
            //     page: 1,
            //     pageSize: 10,
            //     search: ''
            // }, {
            //     //subscribe属性可以配置不把数据存储到store
            //     // subscribe: false,
            //     forceRefetch: true
            // }));
            // apiUnsubscribeSet.add(unsubscribe);
            initData.call(this);
        }, [LIFECYCLE_METHOD_ENUM.MODULE_DESTROY]: function (moduleParam) {
            //模型销毁生命周期
            apiUnsubscribeSet.forEach((unsubscribe) => {
                unsubscribe();
            })
        }, [LIFECYCLE_METHOD_ENUM.MODULE_UPDATED]: function (moduleParam, prevProps, prevState, snapshot) {
            //模型更新生命周期
            //tableData是个假属性，不是表格的数据属性dataSource，通过这种方式触发组件更新，从而直接调用表格组件的api
            if (prevProps.tableData1 !== this.props.tableData1) {
                this.tableRef.current._dataMethod_.refresh(this.props.tableData1);
            }
        },
        loadData: function (page, pageSize) {
            pageSize = pageSize ?? 10;
            page = page ?? 1;
            this.setState({
                page: page,
                pageSize: pageSize
            })
            dispatch(baseApi.endpoints.getUnDeliveredUserListData.initiate({
                page: page,
                pageSize: pageSize,
                search: ''
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
        },
        inputOnSearch: function (value, event) {
            this.setState({page: 1, pageSize: 10});
            dispatch(baseApi.endpoints.getUnDeliveredUserListData.initiate({
                page: 1,
                pageSize: 10,
                search: value
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            }));
        },
        deliverBtnOnClick: function (e) {
            let userId = this.selectedRows[0].id;

            dispatch(baseApi.endpoints.orderDeliver.initiate({
                userId: userId,
                orderId: orderId,
            }, {
                //subscribe属性可以配置不把数据存储到store
                // subscribe: false,
                forceRefetch: true
            })).then((res) => {
                let data = res.data;

                if(data.status){
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.SUCCESS,
                        message: "发货成功。",
                        delay: 3,
                    });

                    SimpleRouter.close('/deliverModule');
                }else{
                    window.global.Notification.open({
                        type: window.global.Notification.TYPE.ERROR,
                        message: data.errorMsg,
                        delay: 3,
                    });
                }
            });
        }
    };
};

let Container = ModuleContainerCreator.create(UserModule, mapStateToProps, mapDispatchToProps);
export default Container;
