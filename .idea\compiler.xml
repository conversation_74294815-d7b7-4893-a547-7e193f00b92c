<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="traffic-client" />
        <module name="fsockManage-mybaits" />
        <module name="traffic-mybaits" />
        <module name="orderManage-mybaits" />
        <module name="fsockManage-web" />
        <module name="orderManage-web" />
        <module name="fsockManage-mybaits (1)" />
        <module name="traffic-server" />
        <module name="traffic-common" />
        <module name="eureka-server" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="eureka-server" options="" />
      <module name="fsockManage-common" options="-parameters" />
      <module name="fsockManage-mybaits" options="-parameters" />
      <module name="fsockManage-mybaits (1)" options="-parameters" />
      <module name="fsockManage-parent" options="-parameters" />
      <module name="fsockManage-web" options="-parameters" />
      <module name="orderManage-common" options="-parameters" />
      <module name="orderManage-mybaits" options="-parameters" />
      <module name="orderManage-parent" options="-parameters" />
      <module name="orderManage-web" options="-parameters" />
    </option>
  </component>
</project>