.order-module-ctn {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .order-module-left-ctn {
    width: 900px;

    .order-module-title {
      font-size: 25px;
      display: inline-block;
    }

    .ant-card {
      margin-top: 20px;
      border-radius: 8px;

      .ant-card-body {
        font-size: 18px;

        div {
          margin-top: 5px;
        }

        .button-ctn {
          margin-top: 20px;
        }

        .available-ctn {
          font-size: 12px;
        }
      }
    }

    .order-type-ctn {
      margin-top: 50px;

      .order-type-title {
        margin-top: 20px;
        font-size: 25px;
      }

      .order-type-content-ctn {
        margin-top: 20px;

        .ant-radio-group {
          display: flex;
          flex-direction: row;


          .order-type-choose-ctn {
            width: 200px;
            height: 100px;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
            background-color: white;
            border-radius: 8px;

            &:nth-child(2) {
              margin-left: 20px;
            }

            .price-ctn {
              font-size: 18px;
              margin-left: 20px;
            }

            .order-type-choose-content {
              margin-top: 25px;
              margin-left: 20px;
            }
          }
        }
      }
    }
  }

  .order-module-right-ctn {
    width: calc(100% - 1200px);
    height: 500px;
    background-color: white;
    border-radius: 8px;
    margin-top: 50px;
    margin-right: 20px;

    .order-detail-ctn {
      padding: 25px;

      .detail-item {
        display: flex;
        flex-direction: row;
        font-size: 22px;

        div:nth-child(1) {
          width: 90%;
        }

      }

      .order-detail-type-title {
        font-size: 25px;
        color: #b8b5b5;
      }

      .order-detail-title {
        font-size: 35px;
      }

      .order-detail-type-item {
        margin-top: 10px;
      }

      .order-detail-content {
        margin-top: 20px;
      }

      .order-detail-total-ctn {
        .order-detail-total-title {
          font-size: 25px;
          color: #b8b5b5;
        }

        .order-detail-total-price {
          font-size: 35px;
        }
      }

      .order-detail-btn-ctn {
        margin-top: 40px;
        width: 100%;
        height: 50px;

        button {
          font-size: 20px;
          width: 100%;
          height: 100%;
        }
      }

    }
  }
}
