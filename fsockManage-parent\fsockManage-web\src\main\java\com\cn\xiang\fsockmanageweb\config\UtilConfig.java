package com.cn.xiang.fsockmanageweb.config;

import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import com.cn.xiang.fsockmanageweb.spring.support.ValidateExceptionResolver;
import org.hibernate.validator.HibernateValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@Configuration
@EnableAspectJAutoProxy(exposeProxy = true)
public class UtilConfig {
    @Bean
    public ApplicationContextHolder configApplicationContextHolder() {
        return ApplicationContextHolder.getInstance();
    }

    @Bean
    public HibernateValidator hibernateValidator() {
        return new HibernateValidator();
    }

    @Bean
    public ValidateExceptionResolver validateExceptionResolver() {
        return new ValidateExceptionResolver();
    }
}
