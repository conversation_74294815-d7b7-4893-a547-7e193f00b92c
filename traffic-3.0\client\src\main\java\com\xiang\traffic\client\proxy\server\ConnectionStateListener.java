package com.xiang.traffic.client.proxy.server;

import java.util.EventListener;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:11
 */
public interface ConnectionStateListener extends EventListener {

    /**
     * {@link ProxyServerComponent} 组件的{@link ConnectionState} 发生改变时的通知
     *
     * @param node  配置对象
     * @param state 改变后的状态
     */
    void connectionStateChanged(ProxyServerConfig.Node node, ConnectionState state);

}
