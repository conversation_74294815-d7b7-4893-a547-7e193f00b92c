package com.cn.xiang.fsockmanageweb.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cn.xiang.common.mybaits.config.base.BaseEntity;
import com.cn.xiang.fsockmanageweb.po.typeHandler.OrderPoTypeHandler;
import com.cn.xiang.fsockmanageweb.po.typeHandler.ServerPoTypeHandler;
import com.cn.xiang.fsockmanageweb.po.typeHandler.UserPoTypeHandler;

/**
 * <AUTHOR>
 * @date 2024/6/7 19:45
 */
@TableName(value = "tb_deliver", autoResultMap = true)
public class DeliverPo extends BaseEntity {

    @TableField(value = "user_id", typeHandler = UserPoTypeHandler.class)
    private UserPo userPo;
    @TableField(value = "order_id", typeHandler = OrderPoTypeHandler.class)
    private OrdersPo ordersPo;
    @TableField(value = "server_id", typeHandler = ServerPoTypeHandler.class)
    private ServerPo serverPo;

    public UserPo getUserPo() {
        return userPo;
    }

    public void setUserPo(UserPo userPo) {
        this.userPo = userPo;
    }

    public OrdersPo getOrdersPo() {
        return ordersPo;
    }

    public void setOrdersPo(OrdersPo ordersPo) {
        this.ordersPo = ordersPo;
    }

    public ServerPo getServerPo() {
        return serverPo;
    }

    public void setServerPo(ServerPo serverPo) {
        this.serverPo = serverPo;
    }
}
