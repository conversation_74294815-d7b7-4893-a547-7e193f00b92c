import {connect} from 'react-redux'
import ModuleTabs from '../../../../core/components/moduleTabs/moduleTabs';
import global from '../../../../core/utils/global'

const mapStateToProps = (state, ownProps) => {
    //这里主要做redux状态status与组件prop的适配，如果store变化不导致组件更新的话，直接返回ownProps即可。
    // if (state.moduleTabs) {
    //     return {
    //         panes: state.moduleTabs.panes,
    //         activeKey: state.moduleTabs.activeKey
    //     }
    // }
    // return ownProps;
    return state.moduleTabs;
};

const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        onChange: (activeKey) => {
            global.SimpleRouter.switchTab(activeKey);
        },
        remove: (targetKey) => {
            global.SimpleRouter.close(targetKey);
        },
        onCurrentTabsCloseClick: function (e) {
            let contextMenuTargetTabActiveKey = this.contextMenuTargetTabActiveKey;
            global.SimpleRouter.close(contextMenuTargetTabActiveKey);
        },
        onOtherTabsCloseClick: function (e) {
            let contextMenuTargetTabActiveKey = this.contextMenuTargetTabActiveKey;
            let state = global.store.getState();
            let panes = state.moduleTabs.panes.filter(item => item.key !== contextMenuTargetTabActiveKey);
            panes.forEach(item => {
                global.SimpleRouter.close(item.key);
            })
        },
        onLeftTabsCloseClick: function (e) {
            let contextMenuTargetTabActiveKey = this.contextMenuTargetTabActiveKey;
            let state = global.store.getState();
            let panes = [];
            for (let i = 0; i < state.moduleTabs.panes.length; i++) {
                let item = state.moduleTabs.panes[i];
                if (item.key === contextMenuTargetTabActiveKey) {
                    break;
                }
                panes.push(item);
            }

            panes.forEach(item => {
                global.SimpleRouter.close(item.key);
            })
        },
        onRightTabsCloseClick: function (e) {
            let contextMenuTargetTabActiveKey = this.contextMenuTargetTabActiveKey;
            let state = global.store.getState();
            let panes = [];
            for (let i = state.moduleTabs.panes.length - 1; i > 0; i--) {
                let item = state.moduleTabs.panes[i];
                if (item.key === contextMenuTargetTabActiveKey) {
                    break;
                }
                panes.push(item);
            }

            panes.forEach(item => {
                global.SimpleRouter.close(item.key);
            })
        },
        onAllTabsCloseClick: function (e) {
            let state = global.store.getState();
            state.moduleTabs.panes.forEach(item => {
                global.SimpleRouter.close(item.key);
            })
        }
    }
};

const Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(ModuleTabs);

export default Container;
