package com.cn.xiang.fsockmanageweb.utils;

import com.cn.xiang.fsockmanageweb.spring.ApplicationContextHolder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 传递给前端页面的基础消息格式
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021年04月18日 下午8:10:36
 */
public class JSONResultMessage {

    /**
     * 默认的错误码
     */
    private static final String DEFAULT_ERRORCODE = "000000";
    private static final Logger LOGGER = LoggerFactory.getLogger(JSONResultMessage.class);

    private boolean status;
    private ObjectNode content;
    private String errorCode;
    private String errorMsg;
    private Object objErrorMsg;

    public JSONResultMessage() {
        status = false;
    }

    /**
     * 置为成功消息，消息体为空
     */
    public void success() {
        success(null);
    }

    /**
     * 置为成功消息，content为返回内容
     *
     * @param content
     */
    public void success(Object content) {
        this.status = true;

        JsonNode node;
        if (content instanceof JsonNode) {
            node = (JsonNode) content;
        } else {
            node = JsonUtils.getObjectMapper().valueToTree(content);
        }
        if (node instanceof ObjectNode) {
            this.content = (ObjectNode) node;
        }

        this.errorCode = null;
        this.errorMsg = null;
    }

    /**
     * 添加参数
     *
     * @param key
     * @param value
     */
    public void addParameter(String key, Object value) {
        ObjectMapper om = ApplicationContextHolder.getBean(ObjectMapper.class);
        if (this.content == null) {
            this.content = om.createObjectNode();
        }
        this.content.set(key, om.valueToTree(value));
    }

    /**
     * 置为失败消息
     */
    public void error() {
        error(null);
    }

    /**
     * 置为失败消息，并添加错误信息
     *
     * @param errorMsg
     */
    public void error(Object errorMsg) {
        error(DEFAULT_ERRORCODE, errorMsg);
    }

    /**
     * 置为失败消息，并添加错误码和错误信息
     *
     * @param errorMsg
     */
    public void error(String errorCode, Object errorMsg) {
        this.status = false;

        this.errorCode = errorCode;
        if (errorMsg instanceof String) {
            this.errorMsg = (String) errorMsg;
        } else {
            this.objErrorMsg = errorMsg;
        }
    }

    /**
     * 获取消息内容
     *
     * @return
     */
    public Object getContent() {
        if (this.status) {
            return this.content;
        }

        return null;
    }

    /**
     * 获取错误码
     *
     * @return
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取错误信息
     *
     * @return
     */
    public Object getErrorMsg() {
        return errorMsg;
    }

    public Object getObjErrorMsg() {
        return objErrorMsg;
    }

    /**
     * 是否为成功消息，true代表成功
     *
     * @return
     */
    public boolean getStatus() {
        return this.status;
    }

    @Override
    public String toString() {
        ObjectMapper om = JsonUtils.getObjectMapper();
        try {
            return om.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            // to fix
            LOGGER.error(e.getMessage(), e);
            return "";
        }
    }
}
