package com.cn.xiang.fsockmanageweb.config;

import com.cn.xiang.fsockmanageweb.task.ScheduleManager;
import com.cn.xiang.fsockmanageweb.task.jobs.UserExpirationJob;
import com.cn.xiang.fsockmanageweb.task.lifecicle.QuartzSmartLifecycle;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class QuartzTaskConfig {
    @Bean
    public ScheduleManager scheduleManager() {
        return new ScheduleManager();
    }

    @Bean
    public UserExpirationJob userExpirationJob() {
        return new UserExpirationJob();
    }

    @Bean
    public QuartzSmartLifecycle quartzSmartLifecycle() {
        return new QuartzSmartLifecycle();
    }
}
