@echo off
echo Testing Traffic Client Native Executable...

if not exist target\traffic-client.exe (
    echo ERROR: Native executable not found at target\traffic-client.exe
    echo Please run build-native-windows.bat first
    pause
    exit /b 1
)

echo Found native executable: target\traffic-client.exe

:: 显示文件信息
echo File information:
dir target\traffic-client.exe

echo.
echo Testing executable (will run for 5 seconds then terminate)...
echo.

:: 启动程序并在5秒后终止
start /b target\traffic-client.exe
timeout /t 5 /nobreak > nul
taskkill /f /im traffic-client.exe > nul 2>&1

echo.
echo Test completed. If no errors appeared above, the native executable is working correctly.
pause
