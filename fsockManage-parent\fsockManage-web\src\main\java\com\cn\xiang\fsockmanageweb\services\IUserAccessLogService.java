package com.cn.xiang.fsockmanageweb.services;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cn.xiang.fsockmanageweb.po.UserAccessLogPo;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * <AUTHOR>
 * @date 2024/2/5 23:12
 */
public interface IUserAccessLogService extends IService<UserAccessLogPo> {
    public String userEnter(String userId, String address);

    public void userLeave(String accessLogId, long uploadTrafficUsage, long downloadTrafficUsage);

    public long getUserDownloadTrafficUsage(String username);

    public Page<ObjectNode> findPagesWithUsername(PageDTO<UserAccessLogPo> pageDTO);
}
