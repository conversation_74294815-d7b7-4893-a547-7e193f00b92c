package com.xiang.traffic.client.proxy.http;

import io.netty.channel.ChannelPipeline;
import io.netty.handler.codec.http.HttpHeaderNames;
import org.apache.commons.lang3.StringUtils;

import java.util.Base64;

/**
 * HTTP代理认证逻辑
 * 基于请求头proxy-authorization实现
 * 具体规范参考<a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Proxy-Authorization" />
 *
 * <AUTHOR>
 * @date 2024/6/6 15:08
 * @see HttpHeaderNames#PROXY_AUTHORIZATION
 */
interface AuthenticationStrategy {

    /**
     * 认证未通过或者未发送认证信息时响应头包含的proxy-authenticate字段
     *
     * @return proxy-authenticate字段值
     */
    String proxyAuthenticateHeader();

    /**
     * 执行认证
     *
     * @param type        认证类型
     * @param credentials 认证凭据
     * @return 认证是否通过
     */
    boolean grantAuthorization(String type, String credentials);


    /**
     * 认证成功后对{@link ChannelPipeline}的操作
     *
     * @param pipeline    当前HTTP代理连接管道对象
     * @param type        认证类型
     * @param credentials 认证凭据
     */
    default void afterAuthorizationSuccess(ChannelPipeline pipeline, String type, String credentials) {
    }

}

/**
 * 固定用户名和密码
 */
class SimpleAuthenticationStrategy implements AuthenticationStrategy {

    private final String username;

    private final String password;

    public SimpleAuthenticationStrategy(String username, String password) {
        if (StringUtils.isAnyBlank(username, password)) {
            throw new IllegalArgumentException();
        }

        this.username = username;
        this.password = password;
    }


    @Override
    public String proxyAuthenticateHeader() {
        return "Basic realm=\"flyingsocks HTTP Proxy service\"";
    }


    @Override
    public boolean grantAuthorization(String type, String credentials) {
        if (StringUtils.isBlank(type) || StringUtils.isBlank(credentials)) {
            return false;
        }

        if (!type.equals("Basic")) {
            return false;
        }

        try {
            String[] arr = StringUtils.split(new String(Base64.getDecoder().decode(credentials)), ':');
            if (arr.length != 2) {
                return false;
            }

            return this.username.equals(arr[0]) && this.password.equals(arr[1]);
        } catch (RuntimeException e) {
            return false;
        }
    }
}
