import React, {Component} from 'react';
import {Space, Table} from 'antd';
import pureStateDataWrapperHoc from "../../../../core/components/hocComponent/data/PureStateDataWrapper";
import {CheckCircleTwoTone, CloseCircleFilled, InfoCircleFilled, PayCircleFilled} from "@ant-design/icons";
import DeliverModal from "../../../../components/deliverDetail/container";

const {Column} = Table;

let TableWrapper = pureStateDataWrapperHoc(Table, {
    dataPropName: 'dataSource', itemKeyName: 'key', isForwardedRef: false
});

class OrderListModule extends Component {
    constructor(props) {
        super(props);
        this.tableRef = React.createRef();
        this.deliverModalRef = React.createRef();
    }

    render() {
        return (
            <>
                <TableWrapper ref={this.tableRef} dataSource={this.props.dataSource}>
                    <Column align={"center"} title="订单id" dataIndex="id"/>
                    <Column align={"center"} title="产品名称" dataIndex="name"/>
                    <Column align={"center"} title="数量" dataIndex="quantity"/>
                    <Column align={"center"} title="单价" dataIndex="price"/>
                    <Column align={"center"} title="总价" dataIndex="totalAmount"/>
                    <Column align={"center"} title="订单状态" dataIndex="status" render={(text, record, index) => {
                        switch (text) {
                            case 'created':
                                return <>
                                    待支付 <InfoCircleFilled style={{color: '#4866c5'}}/>
                                </>
                            case 'paid':
                                return <>
                                    已支付 <PayCircleFilled style={{color: '#4866c5'}}/>
                                </>
                            case 'delivered':
                                return <>
                                    已发货 <CheckCircleTwoTone twoToneColor="#52c41a"/>
                                </>;
                            case 'completed':
                                return <>
                                    已完成 <CheckCircleTwoTone twoToneColor="#52c41a"/>
                                </>;
                            case 'canceled':
                                return <>
                                    已取消 <CloseCircleFilled style={{color: '#f5222d'}}/>
                                </>
                        }
                    }}/>
                    <Column align={"center"} title="订单创建日期" dataIndex="createdTime"/>
                    <Column align={"center"} title="订单支付日期" dataIndex="paymentDate"
                            render={(text, record, index) => {
                                let status = record.status;
                                if (status !== 'created' && status !== 'canceled') {
                                    return text;
                                }
                            }}/>
                    <Column align={"center"} title="操作" render={(_, record) => {
                        let orderId = record.id;

                        if (record.status === 'paid') {
                            return <Space size="middle">
                                <a data-orderid={orderId} onClick={this.props.deliverOnClick.bind(this)}>发货</a>
                            </Space>
                        } else if (record.status === 'delivered' || record.status === 'completed') {
                            return <Space size="middle">
                                <a data-orderid={orderId} onClick={this.props.viewDetailOnClick.bind(this)}>查看</a>
                            </Space>
                        }
                    }}/>
                </TableWrapper>

                <DeliverModal ref={this.deliverModalRef}/>
            </>
        )
    }
}

export default OrderListModule;
