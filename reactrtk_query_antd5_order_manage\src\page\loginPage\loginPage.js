import './loginPage.less';
import React, {Component} from 'react';
import {Tabs} from 'antd';
import PasswordLoginForm from '../../components/form/login/password/container';
import PhoneLoginForm from '../../components/form/login/phone/phoneLoginForm';

class LoginPage extends Component {

    render() {
        const tabsItem = [
            {
                label: '账号密码登录',
                key: 'password',
                children: <PasswordLoginForm loginSuccessCallback={this.props.loginSuccessCallback}/>
            },
            {
                label: '手机登录',
                key: 'plone',
                children: <PhoneLoginForm loginSuccessCallback={this.props.loginSuccessCallback}/>
            },
        ];

        return (<div className={'login-ctn'} style={(() => {
                return this.props.isVisable ? {display: 'flex'} : {display: 'none'}
            }
        )()}>
            <img alt={''} src={'loginBackground.jpg'}/>
            <div className={'form-container'}>
                <div className={'form-login-top'}>
                    <div className={'form-login-header'}>
                       {/* <span className={'form-login-logo'}>
                            <img alt={'logo'}
                                //TODO 更换图片
                                 src={'logo512.png'}/>
                        </span>
                        <span className={'form-login-title'}>Design title</span>*/}
                    </div>
                </div>
                <div className={'form-login-main'}>
                    <Tabs centered={true} items={tabsItem}/>
                </div>
            </div>
        </div>)
    }
}

export default LoginPage;
