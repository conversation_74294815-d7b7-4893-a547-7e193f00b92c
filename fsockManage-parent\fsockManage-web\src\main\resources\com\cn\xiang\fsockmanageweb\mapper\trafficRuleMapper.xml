<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.xiang.fsockmanageweb.mapper.ITrafficRuleMapper">
    <select id="findTrafficRuleByGroupName" parameterType="String" resultType="com.cn.xiang.fsockmanageweb.po.TrafficRulePo">
        select * from tb_group g, tb_traffic_rule t where g.traffic_rule_id = t.id and groupName = #{groupName}
        and g.deleted=0 and t.deleted=0
    </select>

</mapper>
