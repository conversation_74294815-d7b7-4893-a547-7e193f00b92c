import {createSlice} from "@reduxjs/toolkit";

let initialState = {
    panes: [],
    activeKey: ''
};

const moduleTabsSlice = createSlice({
    name: 'moduleTabsSlice',
    initialState,
    reducers: {
        addTab: (state, action) => {
            let panes = [].concat(state.panes);
            panes.push({
                title: action.payload.title,
                key: action.payload.key
            });
            state.panes = panes;
            state.activeKey = action.payload.key;
        },
        selectTab: (state, action) => {
            state.activeKey = action.payload.activeKey;
        },
        removeTab: (state, action) => {
            let key = action.payload.key;
            let activeKey = state.activeKey;

            let lastIndex;
            let panes = [].concat(state.panes);
            panes.forEach((pane, i) => {
                if (pane.key === key) {
                    lastIndex = i - 1;
                }
            });
            panes = panes.filter(pane => pane.key !== key);
            if (panes.length && activeKey === key) {
                if (lastIndex >= 0) {
                    activeKey = panes[lastIndex].key;
                } else {
                    activeKey = panes[0].key;
                }
            }
            state.panes = panes;
            state.activeKey = activeKey;
        },
        changeTabTitle: (state, action) => {
            let title = action.payload.title;
            let key = action.payload.key;

            for (let pane of state.panes) {
                if (pane.key === key) {
                    pane.title = title;
                }
            }
        }
    },
});

let result = {
    reducer: {
        moduleTabs: moduleTabsSlice.reducer
    },
    actions: moduleTabsSlice.actions
}

export default result;