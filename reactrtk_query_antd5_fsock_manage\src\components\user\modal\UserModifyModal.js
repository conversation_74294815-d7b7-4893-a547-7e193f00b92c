import React, {Component} from 'react';
import {DatePicker, Form, Input, Modal, Switch} from 'antd'

/**
 * 用户修改弹窗
 */
class UserModifyModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };


    render() {
        return <Modal
            title="修改"
            open={this.state.visible}
            onOk={this.props.handleModifyOk}
            onCancel={this.handleCancel}
            okText={'修改'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    hidden={true}
                    label="id"
                    name="id"
                    rules={[{required: true, message: 'Please input your id!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="用户名"
                    name="username"
                    rules={[{required: true, message: 'Please input your username!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="密码"
                    name="password"
                    rules={[{required: true, message: 'Please input your password!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="有效日期"
                    name="expiration"
                    rules={[{required: true, message: 'Please input expiration!'}]}
                >
                    <DatePicker
                        showTime
                    />
                </Form.Item>
                <Form.Item
                    label="组名（可选）"
                    name="groupName"
                    rules={[{required: false, message: 'Please input your groupName!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="是否可用"
                    name="enabled"
                    valuePropName="checked"
                    initialValue={true}
                    rules={[{required: true, message: 'Please input your status!'}]}
                >
                    <Switch defaultChecked={true}/>
                </Form.Item>
                <Form.Item
                    label="是否永久"
                    name="noExpiration"
                    valuePropName="checked"
                    initialValue={false}
                >
                    <Switch defaultChecked={false}/>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default UserModifyModal;
