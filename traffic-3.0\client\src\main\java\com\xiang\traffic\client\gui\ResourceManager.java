package com.xiang.traffic.client.gui;

import io.netty.util.internal.PlatformDependent;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * GUI资源管理器
 *
 * <AUTHOR>
 * @date 2024/6/6 15:03
 */
public final class ResourceManager {

    static {
        try {
            //加载这个类以便支持‘classpath:’类型的URL
            Class.forName("com.xiang.traffic.url.ClasspathURLHandlerFactory");
        } catch (ClassNotFoundException e) {
            throw new Error(e);
        }
    }

    public static InputStream openIconImageStream() throws IOException {
        return new URL("classpath://META-INF/ui-resource/icon.png").openStream();
    }

    public static InputStream openSystemTrayImageStream() throws IOException {
        if (PlatformDependent.isOsx()) {
            return new URL("classpath://META-INF/ui-resource/icon-tray-mac.png").openStream();
        }

        return new URL("classpath://META-INF/ui-resource/icon-tray.png").openStream();
    }

    public static InputStream openSaveIconImageStream() throws IOException {
        return new URL("classpath://META-INF/ui-resource/save-icon.png").openStream();
    }

    public static InputStream openDeleteIconImageStream() throws IOException {
        return new URL("classpath://META-INF/ui-resource/delete-icon.png").openStream();
    }

    private ResourceManager() {
        throw new UnsupportedOperationException();
    }

}
