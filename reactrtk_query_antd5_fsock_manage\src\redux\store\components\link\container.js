import {connect} from 'react-redux'
import Link from '../../../../components/link/Link'
import global from '../../../../core/utils/global'

const mapStateToProps = (state, ownProps) => {
    //这里主要做redux状态status与组件prop的适配，如果store变化不导致组件更新的话，直接返回ownProps即可。
    return ownProps;
};
const mapDispatchToProps = (dispatch, ownProps) => {
    //注入属性到组件props上
    return {
        onClick: function (event) {
            event.preventDefault();

            //优先找 event.target.dataset，如果没有则用event.target.text
            let menuName = event.target.dataset.menuName;
            if (!menuName) {
                menuName = event.target.text;
            }

            //从href=http://localhost:3000/option1/#indexModule1#sub截取到#indexModule1#sub并转换为/indexModule1/sub
            let href = event.target.href;
            href = href.substring(href.indexOf('#'), href.length).replace(/#/g, '/');

            //打开页面
            global.SimpleRouter.open(menuName, href, this.props.data);
        }
    };
};

const Container = connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    {forwardRef: true}
)(Link);


export default Container;
