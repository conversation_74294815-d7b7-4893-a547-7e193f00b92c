package com.cn.xiang.fsockmanageweb.spring.mvc;

import com.cn.xiang.fsockmanageweb.po.GroupPo;
import com.cn.xiang.fsockmanageweb.services.IGroupService;
import com.cn.xiang.fsockmanageweb.spring.support.ControllerSupport;
import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import com.cn.xiang.fsockmanageweb.vo.GroupVo;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api_v1/group")
public class GroupController extends ControllerSupport {
    @Autowired
    private IGroupService groupService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object list(String search) {
        List<GroupPo> list = groupService.listBySearch(search);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        message.addParameter("list", list);
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.POST)
    public Object add(@RequestBody @Validated(value = {GroupVo.AddGroup.class}) GroupVo groupVo) {
        GroupPo groupPo = new GroupPo();
        BeanUtils.copyProperties(groupVo, groupPo);
        groupService.saveWithDefaultTrafficCapacity(groupPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Object modify(@RequestBody @Validated(value = {GroupVo.ModifyGroup.class}) GroupVo groupVo) {
        GroupPo groupPo = new GroupPo();
        BeanUtils.copyProperties(groupVo, groupPo);
        groupService.updateById(groupPo);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "", method = RequestMethod.DELETE)
    public Object del(@Validated @NotEmpty String id) {
        groupService.removeById(id);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "/groupTrafficRuleAssign", method = RequestMethod.POST)
    public Object groupTrafficRuleAssign(@Validated @NotEmpty String groupId, @NotEmpty String trafficRuleId) {
        groupService.assignTrafficRule(groupId, trafficRuleId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }

    @RequestMapping(value = "/groupTrafficCapacityAssign", method = RequestMethod.POST)
    public Object groupTrafficCapacityAssign(@Validated @NotEmpty String groupId, @NotEmpty String trafficCapacityId) {
        groupService.assignTrafficCapacity(groupId, trafficCapacityId);

        JSONResultMessage message = this.getJSONResultMessage();
        message.success();
        return message;
    }
}
