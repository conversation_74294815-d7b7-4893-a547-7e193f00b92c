package com.cn.xiang.fsockmanageweb.spring.support;

import com.cn.xiang.fsockmanageweb.utils.JSONResultMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 所有其他异常统一回默认消息
 */
@RestControllerAdvice
public class ControllerExceptionAdvice {
    private static final Logger LOGGER = LoggerFactory.getLogger(ControllerExceptionAdvice.class);
    private static final String UNNKOWN_EXCEPTION_ERROR_CODE = "999999";

    @ExceptionHandler(Exception.class)
    public Object defaultExceptionHandler(Exception e) {
        LOGGER.error(e.getMessage(), e);
        if (e instanceof RuntimeException) {
            JSONResultMessage resultMessage = new JSONResultMessage();
            resultMessage.error(UNNKOWN_EXCEPTION_ERROR_CODE, e.getMessage());
            return resultMessage;
        } else {
            JSONResultMessage resultMessage = new JSONResultMessage();
            resultMessage.error(UNNKOWN_EXCEPTION_ERROR_CODE, "unknown expeption!");
            return resultMessage;
        }
    }
}
