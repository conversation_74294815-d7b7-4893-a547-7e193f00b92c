import {createSlice} from "@reduxjs/toolkit";

let initialState = {};

const lineChartSlice = createSlice({
    name: 'lineChartSlice',
    initialState,
    reducers: {
        refreshData: (state, action) => {
            state.data = action.payload.data
        }
    },
});

let result = {
    reducer: {
        lineChart: lineChartSlice.reducer
    },
    actions: lineChartSlice.actions
}
export default result;