package com.cn.xiang.fsockmanageweb.task.lifecicle;

import com.cn.xiang.fsockmanageweb.task.jobs.UserExpirationJob;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.SmartLifecycle;

/**
 * <AUTHOR>
 * @date 2024/7/3 15:50
 */
public class QuartzSmartLifecycle implements SmartLifecycle {
    private boolean isRunning = false;

    private boolean autoStartup = true;

    @Override
    public void start() {
        UserExpirationJob.init();

        isRunning = true;
    }

    @Value("${quartz-task.auto-startup}")
    public void setAutoStartup(boolean autoStartup) {
        this.autoStartup = autoStartup;
    }

    @Override
    public boolean isAutoStartup() {
        return autoStartup;
    }

    @Override
    public void stop() {

    }

    @Override
    public boolean isRunning() {
        return isRunning;
    }
}
