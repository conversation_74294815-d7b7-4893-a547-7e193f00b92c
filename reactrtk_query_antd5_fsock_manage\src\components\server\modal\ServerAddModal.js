import React, {Component} from 'react';
import {Form, Input, Modal, Switch} from 'antd'

/**
 * 服务器添加弹窗
 */
class ServerAddModal extends Component {
    constructor(props) {
        super(props);

        this.formRef = React.createRef();
        this.state = {
            visible: false
        };
    }

    handleCancel = () => {
        this.setState({
            visible: false
        })
    };

    handleIpAddressOnblur = (event) => {
        let ipAddress = event.target.value;
        this.formRef.current.setFieldsValue({
            "groupName": ipAddress
        })
    };

    render() {
        return <Modal
            title="添加"
            open={this.state.visible}
            onOk={this.props.handleAddOk}
            onCancel={this.handleCancel}
            okText={'添加'}
            cancelText={'取消'}>
            <Form ref={this.formRef}
                  labelCol={{span: 6}}
                  wrapperCol={{span: 16}}
                  name="basic"
            >
                <Form.Item
                    label="ip地址"
                    name="ip"
                    rules={[{required: true, message: 'Please input your ip address!'}]}
                >
                    <Input onBlur={this.handleIpAddressOnblur}/>
                </Form.Item>

                <Form.Item
                    label="端口"
                    name="port"
                    initialValue={1090}
                    rules={[{required: true, message: 'Please input your port!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="cert端口"
                    name="certPort"
                    initialValue={7060}
                    rules={[{required: true, message: 'Please input your cert port!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="shell端口"
                    name="shellPort"
                    initialValue={22}
                    rules={[{required: true, message: 'Please input your shell port!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="服务器用户名"
                    name="username"
                    rules={[{required: true, message: 'Please input your server name!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="密码"
                    name="password"
                    rules={[{required: true, message: 'Please input your password!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="服务器组名"
                    name="groupName"
                    rules={[{required: true, message: 'Please input your server group name!'}]}
                >
                    <Input/>
                </Form.Item>
                <Form.Item
                    label="最大用户量"
                    name="maxUserCount"
                    initialValue={3}
                    rules={[{required: true, message: 'Please input  maxUserCount!'}]}
                >
                    <Input/>
                </Form.Item>
            </Form>
        </Modal>
    }
}

export default ServerAddModal;
