package com.xiang.traffic.client.gui.swt;

import com.xiang.traffic.AbstractModule;
import com.xiang.traffic.client.ClientOperator;
import com.xiang.traffic.client.gui.ResourceManager;
import com.xiang.traffic.client.proxy.socks.SocksConfig;
import com.xiang.traffic.misc.BaseUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

import static com.xiang.traffic.client.gui.swt.Utils.*;

/**
 * Socks5代理设置界面
 *
 * <AUTHOR>
 * @date 2024/6/6 15:02
 */
final class SocksSettingModule extends AbstractModule<SWTViewComponent> {

    public static final String NAME = SocksSettingModule.class.getSimpleName();

    private final ClientOperator operator;

    private final Shell shell;

    SocksSettingModule(SWTViewComponent component) {
        super(Objects.requireNonNull(component), NAME);
        this.operator = component.getParentComponent();

        Image icon;
        try (InputStream is = ResourceManager.openIconImageStream()) {
            icon = loadImage(is);
        } catch (IOException e) {
            throw new Error(e);
        }

        this.shell = createShell(component.getDisplay(), "swtui.socks5.title", icon, 600, 250);
        initial();
        adaptDPI(shell);
    }

    private void initial() {
        createLabel(shell, "swtui.socks5.form.label.validate", 20, 5, 80, 30, SWT.CENTER);
        createLabel(shell, "swtui.socks5.form.label.username", 20, 40, 80, 30, SWT.CENTER);
        createLabel(shell, "swtui.socks5.form.label.password", 20, 75, 80, 30, SWT.CENTER);
        createLabel(shell, "swtui.socks5.form.label.port", 20, 110, 80, 30, SWT.CENTER);

        Button open = createRadio(shell, "swtui.socks5.form.button.open", 160, 5, 80, 30);
        Button off = createRadio(shell, "swtui.socks5.form.button.close", 250, 5, 80, 30);

        Text user = new Text(shell, SWT.BORDER);
        user.setBounds(160, 40, 380, 30);
        Text pass = new Text(shell, SWT.BORDER | SWT.PASSWORD);
        pass.setBounds(160, 75, 380, 30);
        Text port = new Text(shell, SWT.BORDER);
        port.setBounds(160, 110, 130, 30);

        Button enter = createButton(shell, "swtui.socks5.form.button.enter", 170, 145, 150, 40);
        addButtonSelectionListener(enter, e -> {
            boolean auth = open.getSelection();
            String username = user.getText();
            String password = pass.getText();
            int p;
            if (BaseUtils.isPortString(port.getText())) {
                p = Integer.parseInt(port.getText());
            } else {
                showMessageBox(shell, "swtui.socks5.notice.title", "swtui.socks5.notice.port_error", SWT.ICON_ERROR | SWT.OK);
                return;
            }

            operator.updateSocksProxyAuthentication(p, auth, username, password);
            SocksConfig cfg = operator.getSocksConfig();
            if (cfg.getPort() != p) {
                showMessageBox(shell, "swtui.socks5.notice.title", "swtui.socks5.notice.update_success",
                        SWT.ICON_INFORMATION | SWT.OK);
            } else {
                showMessageBox(shell, "swtui.socks5.notice.title", "swtui.socks5.notice.unchanged",
                        SWT.ICON_INFORMATION | SWT.OK);
            }
        });

        addButtonSelectionListener(open, e -> {
            user.setEditable(true);
            pass.setEditable(true);
        });

        addButtonSelectionListener(off, e -> {
            user.setEditable(false);
            pass.setEditable(false);
        });

        Button cancel = createButton(shell, "swtui.socks5.form.button.cancel", 360, 145, 150, 40);
        addButtonSelectionListener(cancel, e -> setVisiable(false));

        SocksConfig cfg = operator.getSocksConfig();
        if (cfg.isAuth()) {
            open.setSelection(true);
        } else {
            off.setSelection(true);
            user.setEditable(false);
            pass.setEditable(false);
        }

        if (cfg.getUsername() != null) {
            user.setText(cfg.getUsername());
        }

        if (cfg.getPassword() != null) {
            pass.setText(cfg.getPassword());
        }

        port.setText(String.valueOf(cfg.getPort()));
    }

    void setVisiable(boolean visiable) {
        shell.setVisible(visiable);
    }
}
